#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 134217728 bytes for Failed to commit area from 0x0000000080400000 to 0x0000000088400000 of length 134217728.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/os/windows/os_windows.cpp:3386), pid=11608, tid=7024
#
# JRE version:  (11.0.16+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (11.0.16+8, mixed mode, sharing, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @C:\Nova pasta\app\node_modules\react-native-screens\android\build\20221115_18139049276432922813.compiler.options

Host: AMD FX-8320E Eight-Core Processor              , 8 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.2251)
Time: Tue Nov 15 11:13:47 2022 Hora oficial do Brasil elapsed time: 0.025494 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001c29d966800):  JavaThread "Unknown thread" [_thread_in_vm, id=7024, stack(0x0000007016c00000,0x0000007016d00000)]

Stack: [0x0000007016c00000,0x0000007016d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x61a63a]
V  [jvm.dll+0x757439]
V  [jvm.dll+0x758b28]
V  [jvm.dll+0x759203]
V  [jvm.dll+0x24d9a8]
V  [jvm.dll+0x61794a]
V  [jvm.dll+0x60c0f5]
V  [jvm.dll+0x304f5c]
V  [jvm.dll+0x304de2]
V  [jvm.dll+0x309af6]
V  [jvm.dll+0x34fe4f]
V  [jvm.dll+0x34f880]
V  [jvm.dll+0x2e1fd8]
V  [jvm.dll+0x2e3136]
V  [jvm.dll+0x731417]
V  [jvm.dll+0x732b9f]
V  [jvm.dll+0x35cb99]
V  [jvm.dll+0x7142b0]
V  [jvm.dll+0x3ca5ef]
V  [jvm.dll+0x3cc7a1]
C  [jli.dll+0x5207]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x174b4]
C  [ntdll.dll+0x526a1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001c29b62c110, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001c29d981000 GCTaskThread "GC Thread#0" [stack: 0x0000007016d00000,0x0000007016e00000] [id=5696]
  0x000001c29d9b4000 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000007016e00000,0x0000007016f00000] [id=14384]
  0x000001c29d9b5000 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000007016f00000,0x0000007017000000] [id=8332]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffc1e1117a7]

VM state:not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001c29d963d10] Heap_lock - owner thread: 0x000001c29d966800

Heap address: 0x0000000080400000, size: 2044 MB, Compressed Oops mode: Non-zero based: 0x0000000080400000
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

Events (1 events):
Event: 0.013 Loaded shared library C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\zip.dll


Dynamic libraries:
0x00007ff785920000 - 0x00007ff78592d000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\java.exe
0x00007ffc5f390000 - 0x00007ffc5f588000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffc5e990000 - 0x00007ffc5ea4f000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffc5ccf0000 - 0x00007ffc5cfc2000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffc5cbf0000 - 0x00007ffc5ccf0000 	C:\Windows\System32\ucrtbase.dll
0x00007ffc56190000 - 0x00007ffc561a5000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\VCRUNTIME140.dll
0x00007ffc4e3e0000 - 0x00007ffc4e3f7000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\jli.dll
0x00007ffc5e280000 - 0x00007ffc5e41d000 	C:\Windows\System32\USER32.dll
0x00007ffc51ad0000 - 0x00007ffc51d6a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007ffc5d320000 - 0x00007ffc5d342000 	C:\Windows\System32\win32u.dll
0x00007ffc5e6b0000 - 0x00007ffc5e74e000 	C:\Windows\System32\msvcrt.dll
0x00007ffc5f2e0000 - 0x00007ffc5f30b000 	C:\Windows\System32\GDI32.dll
0x00007ffc5d160000 - 0x00007ffc5d26f000 	C:\Windows\System32\gdi32full.dll
0x00007ffc5ca90000 - 0x00007ffc5cb2d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffc5f310000 - 0x00007ffc5f342000 	C:\Windows\System32\IMM32.DLL
0x00007ffc382e0000 - 0x00007ffc3837b000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\msvcp140.dll
0x00007ffc1de20000 - 0x00007ffc1e92e000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\server\jvm.dll
0x00007ffc5e490000 - 0x00007ffc5e53e000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffc5f240000 - 0x00007ffc5f2dc000 	C:\Windows\System32\sechost.dll
0x00007ffc5ea50000 - 0x00007ffc5eb75000 	C:\Windows\System32\RPCRT4.dll
0x00007ffc5eb90000 - 0x00007ffc5eb98000 	C:\Windows\System32\PSAPI.DLL
0x00007ffc4e850000 - 0x00007ffc4e877000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffc55550000 - 0x00007ffc5555a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffc50ce0000 - 0x00007ffc50ce9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffc5e420000 - 0x00007ffc5e48b000 	C:\Windows\System32\WS2_32.dll
0x00007ffc5a890000 - 0x00007ffc5a8a2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffc56230000 - 0x00007ffc56240000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\verify.dll
0x00007ffc483f0000 - 0x00007ffc485d4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffc47cd0000 - 0x00007ffc47cfc000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffc5d350000 - 0x00007ffc5d3d2000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffc4e340000 - 0x00007ffc4e368000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\java.dll
0x00007ffc56210000 - 0x00007ffc5621a000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\jimage.dll
0x00007ffc4e320000 - 0x00007ffc4e338000 	C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @C:\Nova pasta\app\node_modules\react-native-screens\android\build\20221115_18139049276432922813.compiler.options
java_class_path (initial): C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.6.21\97bdd5a752ce73b58ef16c17bc7f0ea2faff7a2e\kotlin-compiler-embeddable-1.6.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.21\5dc3574d9b7bebfcb4ec6b10ada4aaa9e140bd0b\kotlin-reflect-1.6.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.6.21\11ef67f1900634fd951bad28c53ec957fabbe5b8\kotlin-stdlib-1.6.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.6.21\99c6675100da5d6d5b1c5a1032f27f28008d101b\kotlin-script-runtime-1.6.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.6.21\46b78cf392e1c4f10854c3cadba44abb7b82250f\kotlin-daemon-embeddable-1.6.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna\5.6.0\330f2244e9030119ab3030fc3fededc86713d9cc\jna-5.6.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.6.21\5e5b55c26dbc80372a920aef60eb774b714559b8\kotlin-stdlib-common-1.6.21.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2143289344                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\OpenJDK\openjdk-11.0.16_8
CLASSPATH=C:\Nova pasta\app\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Nova pasta\app\android\node_modules\.bin;C:\Nova pasta\app\node_modules\.bin;C:\Nova pasta\node_modules\.bin;C:\node_modules\.bin;E:\nodejs;C:\Users\<USER>\AppData\Local\Temp\yarn--1668521474184-0.197675844010198;C:\Nova pasta\app\node_modules\.bin;C:\Users\<USER>\AppData\Local\Yarn\Data\link\node_modules\.bin;E:\libexec\lib\node_modules\npm\bin\node-gyp-bin;E:\lib\node_modules\npm\bin\node-gyp-bin;E:\nodejs\node_modules\npm\bin\node-gyp-bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;E:\nodejs;C:\ProgramData\chocolatey\bin;E:\Git\cmd;C:\Program Files (x86)\Yarn\bin\;C:\Program Files\OpenJDK\openjdk-11.0.16_8\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Yarn\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;E:\nodejs;
USERNAME=caiog
LANG=pt_BR.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 21 Model 2 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 19041 (10.0.19041.2251)
OS uptime: 1 days 19:49 hours

CPU:total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 21 model 2 stepping 0 microcode 0x0, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, aes, clmul, mmxext, 3dnowpref, lzcnt, sse4a, tsc, tscinvbit, tscinv, bmi1, fma

Memory: 4k page, system-wide physical 8174M (320M free)
TotalPageFile size 22238M (AvailPageFile size 18M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 60M, peak: 188M

vm_info: OpenJDK 64-Bit Server VM (11.0.16+8) for windows-amd64 JRE (11.0.16+8), built on Jul 17 2022 01:33:05 by "tester" with MS VC++ 15.9 (VS2017)

END.
