{"forms": {"stores": {"select2": {"label": "Category", "placeholder": "Select categories", "cancelButtonText": "Cancel", "saveButtonText": "Save"}, "select": {"open": {"label": "Store is...", "placeholder": "Choose an option", "optionsLabels": ["Open", "Closed"]}, "active": {"label": "Store status", "placeholder": "Choose an option", "optionsLabels": ["Inactive", "Active"]}}, "input": {"name": {"label": "Store name", "placeholder": "Store name"}, "cnpj": {"label": "CNPJ/CPF", "placeholder": "CNPJ/CPF"}, "email": {"label": "Email", "placeholder": "<EMAIL>"}, "phone": {"label": "Phone", "placeholder": "Phone"}, "slug": {"label": "Slug", "placeholder": "Slug"}, "description": {"label": "Description", "placeholder": "Type here..."}, "pixKey": {"label": "Pix", "placeholder": "Pix"}}, "toasts": {"create_success": "Store created successfully!", "create_hours_success": "Store hours created successfully!", "create_error": "Error! Couldn't create store hours.", "edit_hours_success": "Store hours edited successfully!", "edit_success": "Store edited successfully!", "edit_error": "Error! Couldn't edit store hours.", "not_found": "Error! Couldn't load this user's stores"}, "button": {"banner": "Upload banner", "deleteBanner": "Delete banner", "submit": "Add store", "update": "Update store", "viewMore": "View more"}}, "selectProfile": {"title": "Profile", "select": "Select your profile type:", "noOptions": "You don't have more options", "remove": "Remove profile", "add": "Add profile", "save": "Save", "profileNames": {"shopkeeper": "Shopkeeper", "deliveryman": "Deliveryman", "client": "Buyer"}, "profileStatus": {"requested": "Requested", "review": "Review", "approved": "Approved", "disapproved": "Disapproved", "pending_documents": "Pending documents", "sent_document": "Sent documents", "filled_data": "Filled data"}}, "client": {}, "shopkeeper": {"button": {"addFiles": "Add files", "files": "Files"}}, "deliveryman": {"text": {"documentsUnderReview": "Documents under review"}, "input": {"cpf": {"label": "CPF", "placeholder": "111.111.111.11"}, "cnh": {"label": "Insert CNH"}, "vehicleDocument": {"label": "Insert vehicle document"}, "criminalRecord": {"label": "Insert criminal record"}}, "select": {"type": {"label": "Delivery type", "placeholder": "Select your delivery type", "options": ["Fulltime", "Flexible"]}, "modality": {"label": "Modality", "placeholder": "Select your modality", "options": ["Bicycle", "Bike", "Car"]}}, "pixField": {"label": "Pix"}, "button": {"signIn": "SignIn", "addFiles": "Add files", "files": "Files", "click_here": "Click here"}}, "card": {"input": {"cardHolder": {"label": "Card Holder", "placeholder": "Your Name"}, "cardNumber": {"label": "Card Number", "placeholder": "0000 0000 0000 0000"}, "expiration": {"label": "Expiration", "placeholder": "00/00"}, "securityCode": {"label": "CVV", "placeholder": "000"}}, "checkbox": {"saveCard": {"placeholder": "Save Card."}}, "button": {"confirm": "Confirm"}, "select": {"paymentMethod": {"label": "Payment Method", "placeholder": "Select payment method", "options": ["Credit", "Debit"]}, "installments": {"label": "Installments", "placeholder": "Select the installment", "option": "Installment(s)"}}, "errors": {"expiredCard": "Expired Card"}, "toasts": {"create_error": "Error! Unable to create card!", "payment_failure": "Payment process failed. Please check your data and try again."}}}, "auth": {"signin": {"button": {"signIn": "<PERSON><PERSON>", "createAccount": "Don't have an account?", "signup": "Sign Up", "signInGoogle": "Sign In with Google", "signInFacebook": "Sign In with Facebook", "forgotPassword": "Forgot password"}, "placeholder": {"email": "Email", "password": "Password"}, "label": {"email": "Email", "password": "Password"}, "header": {"tittle": "<PERSON><PERSON>"}, "errors": {"incorrect_credentials": "Incorrect data. Check again.", "email_incorrect": "Email incorrect.", "password_incorrect": "Password incorrect."}}}, "users": {"create": {"button": {"register": "Create account", "address": "Add an address", "terms": "Terms & Conditions", "accept": "I accept the Terms & Conditions", "errorCheckbox": "You must accept the Terms & Conditions", "edit": "Save user", "continue": "Continue"}, "placeholder": {"firstName": "First Name", "lastName": "Last Name", "phone": "(00) 0000-0000", "cpf": "000.000.000-00", "email": "Email", "password": "Password", "address": "Address added", "passwordConfirm": "Confirm password", "retypePass": "Retype password", "dateOfBirth": "01/01/2000", "pix": "Pix"}, "label": {"name": "Name", "email": "Email", "phone": "Phone (with DDD)", "cpf": "CPF", "password": "Password", "sms": "Sms", "whatsapp": "WhatsApp", "passTrackerContain": "The password must contain an uppercase letter, a lowercase letter, a special character, a number and at least {arg} characters.", "passwordRequirements": "Password requirements", "contact_preference": "Contact Preference", "dateOfBirth": "Date of birth (Month/day/year)", "pix": "Pix"}, "text": {"terms": "I agree to the ", "titleTerms": "Terms & Conditions", "checkbox": "Would you like to be contact by: ", "complete_register": "The first login requires further information. Finish the registration to access the application."}, "header": {"tittle": "Create"}, "toasts": {"create_success": "User created successfully!", "create_error": "Error! Couldn't create user!", "edit_success": "User edited successfully!", "edit_error": "Error! Couldn't edit user!", "name": "Name used to identify you in the app", "phone": "Number used to contact you", "cpf": "CPF used to identify you in the app", "email": "Email used to access the app", "password": "Password used to access the app", "passwordConfirm": "Retype the password to confirm it", "dateOfBirth": "Your Date of birth to register in the application", "pix": "Key used in transactions"}, "errors": {"user_alreadyExists": "User already existis.", "user_register_cognito_failed": "User register cognito failed.", "userPhone_alreadyExists": "This phone number is already associated to another account", "cpf_alreadyExists": "This CPF is already associated with another account", "email_alreadyExists": "This Email is already associated with another account."}}, "edit": {"button": {"edit": "Edit"}, "placeholder": {"firstName": "First Name", "lastName": "Last Name", "phone": "Phone (with DDD)", "email": "Email"}, "label": {"name": "Name", "email": "Email"}, "header": {"tittle": "Edit"}, "errors": {}, "components": {"datePicker": {"text": {"dateSelection": "Select a date"}, "button": {"cancel": "Cancel", "save": "Save"}}}}, "config": {"placeholder": {"selectorName": "Language", "english": "English", "portuguese": "Portuguese"}, "errors": {}}, "profile": {"header": {"tittle": "Profile"}, "placeholder": {"withoutaddress": "No address found"}, "errors": {"profile_notFound": "Profile not found."}}, "permission": {"errors": {"permission": "Permission not found."}}, "recoverPassword": {"button": {"sendCode": "Send verification code", "resetPassword": "Reset password", "alreadyHaveCode": "I already have the code"}, "header": {"forgotPassword": "Forgot password", "passwordRequirements": "Password requirements"}, "label": {"email": "Email", "code": "Verification code", "newPassword": "New password", "confirmNewPassword": "Confirm new password"}, "placeholder": {"email": "<EMAIL>", "code": "Verification code", "newPassword": "New password", "confirmNewPassword": "Re-type the password"}, "text": {"enterEmail": "Enter your email for the verification process. We will send a code to your email/phone.", "enterCode": "Enter the code that you received on your email/phone and set the new password for your account.", "passTrackerContain": "The password must contain an uppercase letter, a lowercase letter, a special character, a number and at least {arg} characters."}, "toasts": {"code_sent": "Code sent successfully to your email/phone!", "code_not_sent": "An error occurred. Verification code was not sent.", "password_changed": "Password was changed successfully.", "invalid_verification_code": "Invalid verification code"}}, "errors": {"user_notFound": "User not found."}, "reactivateAccount": {"label": {"title": "Your account has been temporally deleted. But you can reactivate it in an 30 days period.", "subtitle": "To reactivate it inform your email and cpf.", "reactivate": "Account reactivated!\n Log in again to keep using the application"}, "button": {"reactivate": "Reactivate account"}}}, "address": {"home": {"header": {"title": "Addresses", "editAddress": "Edit address"}, "menu": {"map": "See on map", "default": "Set as default", "delete": "Delete"}, "text": {"default": "<PERSON><PERSON><PERSON>"}}, "create": {"placeholder": {"nickname": "Address nickname", "city": "City", "complement": "Complement", "country": "Country", "district": "District", "location": "-15.7975154, -47.8918874", "number": "Number", "postcode": "Postcode", "state": "State", "street": "Street"}, "label": {"nickname": "Address nickname", "city": "City", "complement": "Complement", "country": "Country", "district": "District", "location": "Location", "number": "Number", "postcode": "Postcode", "state": "State", "street": "Street", "brasil": "Brazil"}, "header": {"map": "Improve your address"}, "selectOptions": ["Charge", "Delivery"], "button": {"submit": "Create address", "yes": "Yes", "no": "No", "not_found": "No addresses registered", "edit": "Edit address", "addAddress": "Add address", "confirm": "Confirm"}, "errors": {}, "text": {"deleteModalTittle": "Delete Address", "deleteModalText": "Do you want to delete the address?", "selectAddressLocationMessage": "Select your address location on the map"}, "toasts": {"create_success": "Address created successfully!", "create_error": "Error! Couldn't create address.", "edit_success": "Address edited successfully!", "edit_error": "Error! Couldn't edit <PERSON><PERSON>.", "delete_success": "Address deleted successfully!", "delete_error": "Error! Couldn't delete Address.", "postcode_error": "Failed to query postcode. Please proceed with the manual registration."}}, "edit": {"placeholder": {"nickname": "Nickname", "city": "City", "complement": "Complement", "country": "Country", "district": "District", "location": "Location", "number": "Number", "postcode": "Postcode", "state": "State", "street": "Street"}, "label": {"nickname": "Nickname", "city": "City", "complement": "Complement", "country": "Country", "district": "District", "location": "Location", "number": "Number", "postcode": "Postcode", "state": "State", "street": "Street"}, "selectOptions": ["Charge", "Delivery"], "button": {"submit": "Save", "map": "Edit on map"}, "errors": {"address_notFound": "Address not found"}}, "components": {"confirm_address": {"confirm_address": "Confirm address", "address_confirmed": "Address confirmed", "error_required": "It's necessary to confirm the location!"}}}, "settings": {"changePassword": {"button": {"updatePassword": "Update password"}, "text": {}, "radiobutton": {}, "label": {"previousPassword": "Previous Password", "proposedPassword": "Proposed Password", "confirmProposedPassword": "Confirm Proposed Password"}, "placeholder": {"previousPassword": "Previous Password", "proposedPassword": "Proposed Password", "confirmProposedPassword": "Confirm Proposed Password"}, "header": {}, "errors": {"invalidPassword": "Invalid password", "passwordMatch": "Password doesn't match", "wrong_old_password": "Old password is wrong. This old password should match the one set in registration"}, "toasts": {"successful_update": "Password updated successfully. Just log in using the new password"}}, "home": {"button": {"yes": "Yes", "no": "No", "cancel": "Cancel", "logout": "Logout", "address": "Addresses", "personalData": "Personal data", "deleteAccount": "Delete account", "addProfile": "Add profile", "changeProfile": "Change profile", "changePassword": "Change password", "addPhoto": "Add Photo", "phone_settings": "Access permissions", "documents": "Documents", "faq": "Frequently Asked Questions", "privacy_policy": "Privacy Policy", "terms_of_use": "Terms of Use", "contract": "Contract", "about": "About"}, "text": {"deleteModalTittle": "Delete Account", "deleteModalText": "Do you want to remove this?", "deleteModalReactivate": "After you choose to delete it, You have a 30 days period to reactive this account. Past that period your account is permanently deleted", "profile_picture": "Profile Picture"}, "header": {"tittle": "Home"}, "toasts": {"delete_success": "Success! User deleted!", "delete_error": "Error! Couldn't delete user!"}, "logout": {"button": {"logout": "Logout"}, "toasts": {"logout": "You have logged out of the system."}, "modal": {"title": "Logout", "text": "Are you sure you want to logout?", "btnPositive": "Logout", "btnNegative": "Go Back"}}, "errors": {}}, "verifyEmail": {"button": {"yes": "Yes", "no": "No", "cancel": "Cancel", "logout": "Logout", "confirm": "Verify code", "click_here": "Click here", "validate_email": "Validate email"}, "text": {"verify_email_modal": "To use all the features of the application, you must validate your registration email", "verify_email_modal_tittle": "Email verification", "check_email": "Check your email", "check_email_caption": "Please enter the 6-digit code we sent to:", "code_info": "Didn't receive the code?", "resend_code": "Resend code ", "click_here": "to verify your email"}, "header": {"tittle": "Home"}, "toasts": {"delete_success": "Success! User deleted!", "delete_error": "Error! Couldn't delete user!"}, "logout": {"button": {"logout": "Logout"}, "toasts": {"logout": "You have logged out of the system."}, "modal": {"title": "Logout", "text": "Are you sure you want to logout?", "btnPositive": "Logout", "btnNegative": "Go Back"}}, "errors": {"invalid": "Invalid code"}}, "modalContent": {"title": {"faq": "FAQ", "privacyPolicy": "Privacy Policy", "termsOfUse": "Terms of Use", "contract": "Contract", "about": "About"}, "btnClose": "Close", "text": {"not_found": "Not found"}}}, "stores": {"create": {"button": {"addstore": "Add store", "updatestore": "Update store", "listStore": "List Stores", "banner": "Upload banner", "yes": "Yes", "no": "No", "cancel": "Cancel", "saveChanges": "Save changes", "save": "Save", "storeSettings": "Store settings", "storeHoursSettings": "Store hours", "addressNotFound": "No address found.", "selectAll": "Select all"}, "text": {"deleteModalTittle": "Delete Store", "deleteModalText": "Do you want to delete the store?", "modalReplicateHours": "Select the days you want to replicate:", "modalReplicateHoursTittle": "Replicate Schedules", "storeNotFound": "Stores not found", "categoriesNotFound": "Categories not found", "permission_denied": "Location permission denied", "modalProfileTitle": "Profile image", "modalBannerTitle": "Banner image", "timeSelection": "Select a hour"}, "radiobutton": {"open": "Open", "closed": "Closed", "active": "Active", "inactive": "Inactive", "status": "Store status", "storeis": "Store is...", "chooseOption": "Choose am option"}, "selectOptionsStatus": ["Active", "Inactive"], "selectOptionsOpen": ["Open", "Closed"], "placeholder": {"email": "Email", "name": "Store name", "cnpj": "CNPJ/CPF", "phone": "Phone", "description": "Description", "type_here": "Type here...", "slug": "Slug", "open": "Opening hour", "close": "Closing hour", "permission_denied": "Some stores aren't available because location permission was denied"}, "header": {"tittle": "Stores", "editTitle": "Edit store", "createStore": "Create store", "setStoreHours": "Set Store Hours", "Settings": "Settings", "StoreSettings": "General Store Settings", "storeCategory": "Categories", "productsSuggestions": "Suggestions", "options_address": "Deliver to"}, "errors": {"specific_test": "SPECIFIC ERROR", "is_greater": "Time must be greater", "store_notFound": "Store not found", "slug_already_exists": "slug already exists.", "cnpj_required": "CNPJ required.", "cpf_cnpj_already_exists": "CPF/CNPJ already existis.", "email_alreadyExists": "Email already existis."}, "toasts": {"create_success": "Store created successfully!", "create_error": "Error! Couldn't create store.", "create_hours_success": "Store hours created successfully!", "create_hours_error": "Error! Couldn't create store hours.", "edit_hours_success": "Store hours edited successfully!", "edit_success": "Store edited successfully!", "edit_error": "Error! Couldn't edit store hours."}, "workingHours": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]}, "edit": {"button": {"addstore": "Add store", "updatestore": "Update hours", "listStore": "List Stores", "banner": "Upload banner", "yes": "Yes", "no": "No", "cancel": "Cancel", "saveChanges": "Save changes", "save": "Save", "storeSettings": "Store settings", "storeHoursSettings": "Store hours settings"}, "text": {"deleteModalTittle": "Delete Store", "deleteModalText": "Do you want to delete the store?", "modalProfileTitle": "Profile image", "modalBannerTitle": "Banner image", "disabledByAdmin": "Disabled by <PERSON><PERSON>", "modalModeration": "Administrator's reason", "edit_general_info": "General information", "edit_categories": "Categories", "edit_products": "Products", "edit_hours": "Working hours", "edit_settings": "Settings", "edit_address": "Address", "edit_review": "Reviews", "edit_delete": "Delete store", "edit_leave": "Remove moderation", "categories": "Categories", "edit_banner": "Select a banner", "edit_avatar": "Select a avatar", "delete_warning": "Would you like to delete this store?", "leave_warning": "Would you like to leave this store moderation?", "files": "Files", "edit_gallery": "Gallery", "edit_moderators": "Moderators"}, "radiobutton": {"open": "Open", "closed": "Closed", "active": "Active", "inactive": "Inactive", "status": "Store status", "storeis": "Store is...", "chooseOption": "Choose am option"}, "selectOptionsStatus": ["Active", "Inactive"], "selectOptionsOpen": ["Open", "Closed"], "placeholder": {"email": "Email", "name": "Store name", "cnpj": "CNPJ/CPF", "phone": "Phone", "description": "Description", "type_here": "Type here...", "slug": "Slug", "open": "Opening hour", "close": "Closing hour"}, "header": {"tittle": "Add store", "editTitle": "Edit store", "tittleList": "List stores", "setStoreHours": "Set Store Hours", "Settings": "Settings", "StoreSettings": "General Store Settings"}, "errors": {"specific_test": "SPECIFIC ERROR", "is-greater": "Time must be greater", "store_notFound": "Store not found", "slug_already_exists": "Slug already exists.", "cnpj_already_exists": "Cnpj already exists.", "email_already_exists": "Email already exists."}, "toasts": {"create_success": "Store created successfully!", "create_hours_success": "Store hours created successfully!", "create_error": "Error! Couldn't create store hours.", "edit_hours_success": "Store hours edited successfully!", "edit_success": "Store edited successfully!", "edit_error": "Error! Couldn't edit store hours."}, "workingHours": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]}, "home": {"text": {"deleteModalTitle": "Delete Store", "deleteModalText": "Do you want to delete the store?", "minimumOrder": "Min.order R$ {arg}", "feedbackInfo": "{arg} ({arg})", "waitingTime": "{arg} - {arg} min", "empty": "Store has no registered products", "productsNotFound": "Products not found", "active": "Active", "inactive": "Inactive", "open": "Open", "close": "Closed", "toleranceTimeEnd": "This store is not accepting orders. Try again later.", "toleranceTimeEndCart": "This store is not accepting orders. Order these items later or pick another store.", "inviteText": "The {arg} store is inviting you to be a moderator", "invite": "Invite", "verifyPix": "There is stores missing pix key"}, "button": {"addstore": "Add store", "addressNotFound": "No address registered", "yes": "Yes", "no": "No", "delete": "Delete", "settings": "Settings", "orders": "Orders", "reviews": "Reviews", "accept": "Accept", "refuse": "Refuse", "click_here": "Click here"}, "selectOptionsStatus": ["Active", "Inactive"], "selectOptionsOpen": ["Open", "Closed"]}, "storesSettings": {"button": {"createSettings": "Create settings", "updateSettings": "Update settings"}, "text": {"paymentMethod": "Payment methods", "deliveryRangeTitle": "Delivery range isn't displayed to the clients", "deliveryRange": "The delivery range is the straight-line distance. The value displayed to the client is the shortest possible route. The route from the client's address to the store (with traffic conditions) may be greater than the delivery range, as the straight-line distance is smaller than the range."}, "placeholder": {"maxHour": "Max. tolerance to order (min)", "deliveryRange": "Delivery range (km)", "cash": "Cash", "debt": "Debt", "credit": "Credit", "ticket": "Ticket", "pix": "Pix", "facebook": "Facebook", "instagram": "Instagram", "tiktok": "TikTok"}, "header": {"editTitle": "Edit store settings"}, "error": {"required": "Required field", "moreThanZero": "Should be more than 0", "payment_method": "Select at least 1 payment method"}, "toasts": {"create_success": "Store settings created successfully!", "create_error": "Error! Couldn't create store settings.", "edit_success": "Store settings edited successfully!", "edit_error": "Error! Couldn't edit store settings.", "tolerance_order": "Due time (in minutes) to get new orders before the store closes. The due hours is calculated by the diff between the close hours and the tolerance minutes."}}, "storeHours": {"button": {"updateWorkingHours": "Save"}}, "storeReviews": {"header": {"title": "Reviews", "rateStore": "Rating Store"}, "text": {"reviews": "{arg} ratings", "rateStoreTitle": "What do you think about the store?", "rateStoreText": "Tap a star to rate"}, "button": {"createReview": "Rate this store", "submitReview": "Submit"}, "placeholder": {"comment": "Write a Review (Optional)"}}, "storeDetails": {"text": {"address": "Address", "categories": "Categories", "paymentMethod": "Payment method", "workingHours": "Opening Hours", "description": "Description", "phone": "Contact", "storeClosed": "Closed"}, "button": {"gallery": "Gallery"}}, "storeModerators": {"text": {"title": "Add Moderator", "owner": "Owner", "moderator": "Moderator", "blocked": "Blocked", "invite": "Invite", "missingPix": "Missing Pix"}, "modals": {"title": {"remove": "Remove Moderator", "block": "Block Moderator", "unblock": "Unblock Moderator"}, "body": {"remove": "Are you sure you want to remove this moderator?", "block": "Are you sure you want to block this moderator?", "unblock": "Are you sure you want to unblock this moderator?"}, "button": {"no": "No", "yes": "Yes"}}, "placeholder": {"email": "User email "}, "toasts": {"email": "Email used to access the app", "create_success": "Moderator added successfully.", "update_success": "Moderator status successfully changed.", "remove_success": "Moderator successfully removed.", "refuse_success": "<PERSON><PERSON><PERSON> successfully refused.", "accept_success": "<PERSON><PERSON><PERSON> successfully accepted.", "create_error": "Error! Unable to add moderator.", "update_error": "Error! Unable to change moderator status.", "remove_error": "Error! Unable to remove Moderator.", "refuse_error": "Error! Unable to refuse invite.", "accept_error": "Error! Unable to accept invite.", "blocked": "Your moderation is blocked by the owner."}, "buttons": {"confirm": "Confirm"}, "status": {"active": "Active", "blocked": "Blocked", "pending": "Pending"}, "errors": {"is_already": "There is already a moderator with this email.", "not_found": "User not found.", "not_profile": "User does not have a shopkeeper profile."}}}, "product": {"create": {"header": {"title": "Products", "editProduct": "Edit product"}, "label": {"name": "Name", "short_description": "Short Description", "description": "Description", "price": "Price", "sale_price": "Sale Price", "sku": "S<PERSON>", "sku_info": "Unique code, used to identify the product in the store.", "active": "Active", "icon": "Icon", "banner": "Banner", "category": "Category", "subcategory": "Subcategory", "attributes": "Attributes", "textAreaLength": "{arg}/{arg}", "preparationTime": "Preparation time"}, "placeholder": {"name": "Name", "short_description": "Short Description", "description": "Description", "price": "R$ 00.00", "sale_price": "R$ 00.00", "sku": "132LSKFLS5DS5F", "active": "Select status", "icon": "Icon", "banner": "Banner", "category": "Select categories", "subcategory": "Select subcategories", "selected_category": "Selected categories", "selected_subcategory": "Selected subcategories", "required": "* is required", "category_notFound": "Categories are not available for this store", "preparationTime": "Time in minutes"}, "toasts": {"create_success": "Product created successfully!", "edit_success": "Product edited successfully!", "delete_success": "Product deleted successfully!", "create_error": "Error! Couldn't create product.", "edit_error": "Error! Couldn't edit product.", "delete_error": "Error! Couldn't delete product.", "details_error": "Error! Couldn't show product details."}, "button": {"submit": "Save product", "addproduct": "Add product", "update": "Update product", "not_found": "No products registered", "yes": "Yes", "no": "No", "cancel": "Cancel", "delete": "Delete", "add": "Add", "photos": "Photos and videos", "attribute": "Attributes", "save": "Save"}, "text": {"deleteModalTittle": "Delete Product", "deleteModalText": "Do you want to delete the product?", "modalProductTitle": "Product image"}, "errors": {"product_notCreated": "Couldn't create the product", "product_notUpdated": "Couldn't update the product", "product_notDeleted": "Couldn't delete the product", "product_notFound": "Couldn't find the product", "category_notFound": "Couldn't find the categories", "subcategory_notFound": "Couldn't find the subcategories", "product_subcategory_notDeleted": "Couldn't delete the subcategories", "product_category_notDeleted": "Couldn't delete the categories", "product_categories_notUpdated": "Couldn't update the categories", "product_subcategories_notUpdated": "Couldn't update the subcategories", "product_attributes_notUpdated": "Couldn't update the attributes", "product_sku_already_exists": "Product with this SKU already exists"}, "selectOptionsStatus": ["Active", "Inactive"]}, "edit": {"label": {"name": "Name", "short_description": "Short Description", "description": "Description", "price": "Price", "sale_price": "Sale Price", "sku": "S<PERSON>", "sku_info": "Unique code, used to identify the product in the store.", "active": "Active", "icon": "Icon", "banner": "Banner", "category": "Category", "subcategory": "Subcategory", "attributes": "Attributes", "textAreaLength": "{arg}/{arg}", "preparationTime": "Preparation time (minutes)"}, "placeholder": {"name": "Name", "short_description": "Short Description", "description": "Description", "price": "R$ 00.00", "sale_price": "R$ 00.00", "sku": "132LSKFLS5DS5F", "active": "Select status", "icon": "Icon", "banner": "Banner", "category": "Select categories", "subcategory": "Select subcategories", "selected_category": "Selected categories", "selected_subcategory": "Selected subcategories", "required": "* is required", "category_notFound": "Categories are not available for this store", "preparationTime": "Time in minutes"}, "text": {"edit_general_info": "General information", "edit_categories": "Categories", "edit_attributes": "Attributes", "edit_gallery": "Gallery", "subcategory": "Subcategory", "cancel": "Cancel", "save": "Save", "active": "Active", "inactive": "Inactive", "modalProductTitle": "Product image", "disabledByAdmin": "Disabled by <PERSON><PERSON>.", "modalModeration": "Administrator's reason"}, "button": {"viewMore": "View more"}, "toasts": {"edit_success": "Product edited successfully!"}}, "FavoriteProducts": {"text": {"distance": "Distance", "not_found": "No favorite products were found"}}, "search": {"findProduct": "Search the entire app", "notFound": "Product not found.", "todayOrders": "What's today's orders?", "notSearched": "Type something related to what you want.", "saveFilter": "Filter results", "loadMore": "Load more products", "priceValidation": "Minimum price should be smaller then maximum price", "filters": {"all": "Filters", "category": "Category", "subcategory": "Subcategory", "storeName": "Store", "name": "Product name", "distance": "Distance", "attributes": "Attributes", "trending": "Trending products", "discounts": "Discounts", "price": "Price"}, "label": {"minPrice": "Minimum price", "maxPrice": "Maximum price"}, "placeholder": {"minPrice": "Type the order's minimum price", "maxPrice": "Type the order's maximum price", "distance": "This is the straight-line distance. Actual distance may be a different value"}}}, "attribute": {"create": {"header": {"title": "Specific Attributes", "title_modal": "Specific Attribute", "attribute_association": "Attribute Association"}, "label": {"name": "Name", "short_description": "Short Description", "required": "Required", "type": "Selection type"}, "placeholder": {"name": "Name", "short_description": "Short Description", "input_select": "Select Attribute"}, "toasts": {"create_attribute_success": "Attribute created successfully!", "delete_attribute_success": "Attribute deleted successfully!", "create_attribute_error": "Error! Couldn't create attribute.", "delete_attribute_error": "Error! Couldn't delete attribute.", "loading_attribute_error": "Error! Couldn't show attributes."}, "button": {"submit": "Confirm", "add_attribute": "New Attribute", "not_found": "Not found", "cancel": "Cancel", "delete": "Delete", "save": "Save"}, "errors": {}, "select_selection_type": ["single", "multiple"]}, "edit": {"placeholder": {}, "label": {}}, "errors": {"attribute_notFound": "Attribute not found.", "product_attribute_alreadyExists": "Product/Attribute already exists.", "product_attribute_notFound": "Product/Attribute not found."}}, "attribute_option": {"create": {"header": {"title": "New attribute option"}, "label": {"value": "Option"}, "placeholder": {"value": "Option"}, "toasts": {"create_attribute_option_success": "Attribute Option created successfully!", "delete_attribute_option_success": "Attribute Option deleted successfully!", "create_attribute_option_error": "Error! Couldn't create attribute option.", "delete_attribute_option_error": "Error! Couldn't delete attribute option."}, "button": {"submit": "Confirm", "add_attribute_option": "Add new option", "not_found": "Not found", "cancel": "Cancel", "delete": "Delete", "save": "Save"}}, "edit": {"placeholder": {}, "label": {}}, "errors": {"attribute_option_notFound": "Attribute option not found.", "product_attribute_option_alreadyExists": "Product/Attribute Option already exists.", "product_attribute_option_notFound": "Product/Attribute not found.", "product_attribute_option_notUpdated": "Couldn't update the attribute option"}}, "select2": {"button": {"cancel": "Cancel", "select": "Select"}}, "attachmentFile": {"button": {"choose": "Browse", "close": "Close", "cancel_token": "Operation canceled by the user.", "add_file": "Add File", "view_gallery": "View Gallery"}, "errors": {"remove_file": "Failed to remove file", "add_file": "File upload failed", "init_files": "There was an error initializing attachments", "cancellation": "Cancellation failed", "maxSizeFile": "Maximum file size is {arg} MB"}, "toasts": {"download_success": "Your file has been downloaded!", "upload_success": "Your file has been uploaded!", "download_error": "Download failed.", "upload_error": "Upload failed.", "select_error": "File selection failed.", "just_one_document": "Just one document allowed"}, "badge": {"canceled": "Canceled"}, "text": {"chooseFiles": "Choose the files", "noFiles": "No files were found"}}, "imagePicker": {"button": {"camera": "Camera", "gallery": "Gallery", "delete": "Delete"}, "errors": {"did_cancel": "User cancelled image picker.", "picker_failed": "Error selecting image.", "remove_file": "Failed to remove file", "add_file": "File upload failed"}, "toasts": {"download_success": "Download success!", "upload_success": "Upload success!", "download_error": "Download failed.", "upload_error": "Upload failed.", "select_error": "File selection failed.", "loading_failure": "Loading failure."}, "maxSizeFile": "Maximum file size:"}, "generic": {"button": {"moreDetails": "More details"}, "errors": {"generic_message": "Something went wrong", "label": "Oops... Something went wrong", "required": "Required field", "email": "Enter a valid email", "short_field": "It should be longer than {arg} characters", "email_invalid": "Enter a valid email!", "phone_invalid": "Enter a valid phone!", "passwordMinSix": "Minimum of {arg} characters", "email_alreadyExists": "Email already exists.", "phone_short_field": "Minimum of {arg} numbers", "email_incorrect": "Incorrect email", "incorrect_credentials": "Incorrect data. Check again.", "password_incorrect": "Incorrect password", "password_invalid": "Invalid password.", "invalid": "Invalid", "invalid-cep": "Invalid CEP", "more_than_zero": "Must be greater than {arg}", "more_than_years": "Must be greater than {arg} years", "user_notFound": "User not found", "long_field": "Maximum of {arg} characters allowed", "smaller_value": "This field should be a smaller value", "greater_value": "This field should be a greater value", "exactly_number": "Must be exactly {arg} characters", "customer_code": "The code must be exactly {arg} numbers", "password_match": "Passwords must match", "unMatch_rules": "Must match with the password requirements", "login": "Failed to authenticate. Enter your credentials.", "try_again": "An error ocurred, try again.", "refreshing": "An error ocurred, login again.", "forbidden_exception": "Forbidden exception.", "invalid_parameter": "invalid parameter.", "not_authorized": "Not authorized.", "username_already_exists": "Username already exists.", "password_reset_required": "Password reset required.", "user_not_confirmed": "User not confirmed.", "max_allowed": "Maximum value allowed is {arg}", "internal_error": "Internal error encountered.", "code_delivery_fail": "Error to deliver verification code.", "code_mismatch": "Invalid code.", "expired_code": "Expired code.", "check_data_try_again": "An error ocurred, check the data and try again.", "favorite_error": "Error to set favorite", "profileList": "In<PERSON>t at least one profile.", "userDisabledByAdmin": "Account disabled by Administrator", "invalidDate": "Invalid date", "invalid_cpf": " Cpf doesn't match the one set in registration", "location_error": "Loading location failed"}, "axiosErros": {"ECONNABORTED": "Connection aborted", "ERR_BAD_OPTION": "Internal error", "ERR_BAD_OPTION_VALUE": "Internal error", "ERR_BAD_REQUEST": "Invalid request", "ERR_BAD_RESPONSE": "Internal Server Error", "ERR_CANCELED": "Connection canceled", "ERR_DEPRECATED": "Depracated error", "ERR_FR_TOO_MANY_REDIRECTS": "Redirection error", "ERR_NETWORK": "No connection", "ETIMEDOUT": "Response timed out"}, "toasts": {"successful_request": "Request successful"}}, "routes": {"DrawerHome": "Home", "UserHome": "", "UserApp": "User", "ProductHome": "Products", "AddressHome": "Addresses", "StoreHome": "Stores", "UserEdit": "Edit User", "ProductEdit": "Edit Product", "AddressEdit": "Edit Address", "StoreEdit": "Edit Store", "UserCreate": "Register User", "ProductCreate": "Register Product", "AddressCreate": "Register Address", "StoreCreate": "Register Store", "StoreHoursSettings": "Set Store Hours", "Settings": "Settings", "StoreSettings": "General Store Settings", "SignUp": "Create Account", "Cart": "<PERSON><PERSON>", "Orders": "Orders", "CartHome": "<PERSON><PERSON>", "OrdersHome": "Orders", "StoreList": "Home", "StoreDetails": "Details", "FavoriteProducts": "Favorite products", "StoreShowCase": "Showcase", "ProductDetails": "Product Details", "OrderSummary": "Summary", "OrderDetails": "Details", "OrderPaymentMethod": "Payment Method", "UserRecoverPassword": "Forgot Password", "ChangePassword": "Change password", "UserNotifications": "Notifications", "EditProductGeneralInfo": "Edit product", "EditProductCategories": "Edit categories", "EditProductAttributes": "Edit attributes", "EditProductGallery": "Edit gallery", "EditStoreGallery": "Edit gallery", "SocialSignUp": "Complete register", "OrderChat": "Cha<PERSON>", "ReactivateAccount": "Reactivate Account", "Documents": "Documents", "SalesHome": "Sales", "CheckEmailCpf": "Check Email/CPF", "ProductSearch": "Product Search", "StoreModerators": "Moderators", "CreateModerators": "Create Moderator", "CreateCard": "Register new card"}, "home": {"label": {"search": "Search"}, "placeholder": {"search_store": "Search store", "search_categories": "Search category"}, "sort": {"title": "Sort by", "options": {"favorite": "Favorite", "name": "Name"}}, "store_card": {"text": {"distance": "Distance"}}}, "multiStepForm": {"skip_step": "Skip step", "next": "Next", "go_back": "Previous", "submit": "Save", "modal": {"title": "Leave form", "text": "Do you really want to go back? All the inserted data will be lost", "button": {"positive": "Exit", "negative": "Cancel"}}}, "orders": {"label": {"observation": "Observation", "notFound": "This product doesn't have attributes", "required": "Required", "onlyOne": "Only 1 option", "empty": "No orders found", "status_menu": "Status Menu", "details": "Orders Details", "radio_group": "Select status", "cancel_reason": "Cancel order reason", "delivery": "Send to delivery", "request_deliveryman": "Request a deliveryman", "quantity_order": "Quantity", "quantity_items": "Quantity Items", "quantity_product": "Quantity Product", "unity_price": "Unity price", "total_price": "Total price", "atributes": "Attributes", "product_name": "Product name", "product_description": "Product description", "order_item": "{arg}º Order Item", "payment_method": "Payment method", "order": "Order (subtotal)", "shipping": "Shipping fee", "total": "Total", "choose_status": "Choose status", "accept_order": "Accept order", "delivery_route": "Carry out delivery", "release_code": "Release customer code", "customer_confirmation": "Confirm customer code", "reject_order": "Reject order", "status_steps": "Status", "noReview": "No review", "address": "Address", "complement": "Complement", "orderNumber": "Order #", "selectStore": "Select store", "permission_denied": "Notification permission denied", "select": "Select", "cancel_order": "Cancel Order", "at": "at {arg}", "items": "Items", "amountCharged": "Amount charged", "deliveryAddress": "Delivery address", "deliveryman": "Deliveryman", "background_location_task_name": "Background location", "background_location_task_title": "Current location", "customerCode": "Delivery code", "estimatedDeliveryTime": "Estimated delivery time", "retry_payment": "Retry payment"}, "status": {"all_status": "All status", "preparing_order": "Preparing order", "canceled_order": "Canceled order", "rejected_order": "Rejected order", "on_delivery_route": "On delivery route", "delivered": "Delivered", "placed_order": "Placed order", "payment_made": "Payment approved", "canceled": "Canceled", "rejected": "Rejected", "preparing": "Preparing", "waiting_for_the_delivery_person": "Waiting for the deliveryman", "on_route_to_store": "<PERSON><PERSON><PERSON> is heading to the store", "canceled_delivery": "Canceled delivery", "pending_payment": "Pending payment", "canceled_payment_failure": "Payment failure"}, "placeholder": {"observation": "Ex: Remove onions, salad...", "address": "Add an address", "permission_denied": "Can't receive notification for order status changes because permission was denied", "map_deliveryman_warning": "Keep the map open while delivering!", "tracking": "Track", "continue_tracking": "Keep tracking order", "order_delivered_map": "See delivery route on map", "background_location_description": "Coop delivery is using your location in background", "open_map": "Open map"}, "text": {"customer_code": "Enter the code provided by the customer", "inform_customer_code": "Provide this code to deliveryman when the order arrives", "cancel_delivery": "Are you sure you want to cancel the delivery?", "accept_permission": "To accept an order you need to allow the background location. Set Allow all the time option.", "deliverymanLocation": "Deliveryman profile needs to activate background location. Set Allow all the time option."}, "close_map": {"title": "Exit map?", "text": "Are you sure you want to leave? The tracking of the current delivery will be paused. The deliveryman may be charged.", "button": {"positive": "Close map", "negative": "Cancel"}}, "toasts": {"create_success": "Order successful!", "create_error": "Error! Order unsuccessful!", "invalid_address": "Order cannot be fulfilled. Address is outside the store's delivery radius!", "cancel_success": "Successfully canceled order!", "order_delivered": "Order completed successfully", "added_cart": "Item added to cart!", "error_load_order": "Error loading order", "payment_success": "Payment completed successfully"}, "button": {"addOrder": "Add", "free": "Free", "save": "Save", "place_order": "Place order", "retry": "Retry Payment", "updateOrder": "Update", "change": "Change", "select": "Select", "finish_delivery": "Finish the delivery", "cancel_delivery": "Cancel the delivery", "cancel": "Cancel", "go_back": "Go back", "close": "Close", "accept": "Accept"}, "errors": {"map_failed": "Couldn't open the map", "load_order": "Orders not found", "load_failed": "Load failed", "order_notFound": "Order not found", "order_already_canceled": "Order has already been canceled", "order_notCanceled": "Order cannot be canceled", "payment_notFound": "Payment not found", "payment_notCanceled": "Payment has not been cancelled. Please contact support.", "order_not_updated": "Couldn't update customer code", "wrong_custom_code": "Invalid code. Confirm code with client and try again", "permission_denied": "Location permission denied. Access Settings and allow location all the time", "not_allowed": "Not not_allowed.", "status_not_found": "Status not found.", "deliveryman_not_found": "Deliveryman not found.", "order_already_taken": "Order already accepted by another delivery person, please select another one."}, "cart": {"label": {"totalOrder": "Total with delivery", "alreadyCartItems": "There are items from another store in your cart.", "eraseCart": "Want to clear your cart?", "tapDelete": "TAP TO DELETE", "deliveryFee": "Delivery fee", "orderPrice": "Order (subtotal)", "deleteAll": "Delete all", "addMoreItems": "Add more items", "addItems": "Add items", "observation": "Obs.:", "emptyCart": "The cart is empty"}, "button": {"continue": "Continue"}}, "select": {"address": "Delivery address"}, "orderPaymentMethods": {"paymentMethods": {"credit": "Credit Card", "debt": "Debt Card", "pix": "Pix", "accordion": {"credit": "Credit", "debt": "Debt", "pix": "Pix"}}, "button": {"addCard": "New Card", "confirm": "Confirm"}, "label": {"expires_in": "Expires in: ", "expired_on": "Expired on: ", "newCard": "New Card", "cardData": "Card Data"}}, "modalPix": {"label": {"title": "Pix Key"}, "button": {"copy": "Copy", "close": "Close", "uploadVoucher": "Upload voucher"}, "toasts": {"copied": "<PERSON>pied", "time_limit": "Time Limit expiration"}}, "modalCancel": {"label": {"title": "Cancel Order", "text": "Are you sure you want to cancel the order?"}, "button": {"no": "No", "yes": "Yes"}}, "modalVoucher": {"label": {"title": "Payment voucher"}}}, "productContainer": {"button": {"yes": "Yes", "no": "No"}}, "model_validation": {"user": {"email_invalid": "Invalid email.", "phone_invalid": "Invalid phone."}, "file": {"corrupted_file": "Corrupted file."}, "product": {"name_very_long": "Product name very long.", "short_description_very_long": "Product short description very long.", "description_very_long": "Product description very long.", "invalid_value": "Invalid product value."}, "store": {"name_very_long": "Store name very long.", "description_very_long": "Store description very long.", "cnpj_invalid": "Invalid CNPJ.", "slug_very_long": "Store slug very long.", "email_invalid": "Invalid email.", "phone_invalid": "Invalid phone."}, "store_settings": {"moreThanZero": "Should be more than 0."}, "address": {"number_invalid": "Invalid address number.", "postcode_invalid": "Invalid address postcode."}, "order": {"code_invalid": "The code must be exactly 4 numbers"}}, "review": {"header": {"title": "Reviews", "rating": "Rating {arg}"}, "label": {"order": "order", "store": "store", "deliveryman": "deliveryman"}, "title": {"order": "What do you think about the order?", "store": "What do you think about the store?", "deliveryman": "What do you think about {arg}?"}, "text": {"reviews": "{arg} ratings", "review": "{arg} rating", "createReviewText": "Tap a star to rate", "rate": "Rate", "rateDeliveryman": "Rate deliveryman", "total_price": "Total", "already_reviewed": "There is already a review for this deliveryman."}, "button": {"createReview": "Rate {arg}", "submitReview": "Submit", "replyReview": "Reply feedback", "reply": "Reply"}, "placeholder": {"comment": "Write a Review (Optional)"}, "update": {"errors": {"not_found": "No review registered"}}, "toasts": {"create_success": "Review created successfully!", "create_error": "Error! Couldn't create review."}}, "permission": {"label": {"title": "Denied permission", "settings": "Open settings"}, "text": {"description": "Some functionalities aren't available due to permission denied."}}, "configApp": {"errors": {"loading": "Error loading configuration"}}, "notifications": {"new_order_title": "New order #{arg}", "change_status_title": "Your order #{arg} was updated", "status_placedOrder": "Status changed to \"Placed Order\"", "status_paymentMade": "Status changed to \"Payment approved\"", "status_canceled": "Status changed to \"Canceled order\"", "status_rejected": "Status changed to \"Rejected order\"", "status_preparing": "Status changed to \"Preparing order\"", "status_acceptedByDeliveryPerson": "Status changed to \"Accept by deliveryman\"", "status_onDeliveryRoute": "Status changed to \"On delivery route\"", "status_customerConfirmation": "Status changed to \"Waiting customer confirmation\"", "status_delivered": "Status changed to \"Order delivered\"", "payment_failure": "Payment process failed. Please check your data and try again.", "cancellation_failed": "Oops... There was an error in the order cancellation process.", "label": {"empty": "No notification found", "filter": "Filter", "mark_as_read": "Mark all as read", "date_filter": "Date", "read_messages_filter": "Read", "unread_messages_filter": "Unread", "order_by": "Order by", "latest_messages": "Latest", "older_messages": "Older"}, "toasts": {"mark_as_read_success": "Message marked as read", "mark_as_read_error": "Couldn't mark message as read", "mark_all_as_read_success": "Couldn't mark all as read", "mark_all_as_read_error": "Marked all as read"}}, "calendar": {"localeConfig": {"monthNames": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "monthNamesShort": ["Jan.", "Feb.", "Mar.", "Apr.", "May,", "Jun.", "Jul.", "Aug.", "Sep.", "Oct.", "Nov.", "Dec."], "dayNames": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "dayNamesShort": ["Sun.", "Mon.", "<PERSON><PERSON>.", "Wed.", "<PERSON><PERSON>.", "Fri.", "Sat."], "today": "Today"}, "title": "Calendar", "save_date": "Filter date"}, "chat": {"placeholder": "Type a new message", "labels": {"empty_chat": "No messages in this chat", "no_chat_messages": "No messages here yet. Send a new message", "closed_chat": "Chat closed. You can't send new messages.", "chat_users": "You, {arg} e {arg} can send and receive messages", "is_typing": "is typing...", "order_code": "Order #{arg}"}, "errors": {"chat_not_found": "Cha<PERSON> not found", "chat_messages_not_found": "Chat messages not found", "socket_connection": "Connection failed. Couldn't open chat", "create_chat_invalid_params": "Invalid params. Unable to create chat."}}, "sales": {"placeholder": {"order": "Order #{arg}", "store": "Store", "price": "Price", "shippingPrice": "Shipping Price", "totalPrice": "Total", "routeLength": "Distance", "address": "Address"}, "totalizers": {"id": "Orders", "price": "Orders price", "totalPrice": "Shipping Price + Order", "routeLength": "Distance", "shippingPrice": "Shipping Price", "payoutsAvailable": "Payouts available", "payoutsExecuted": "Payouts executed"}, "labels": {"calendar": "Calendar", "dateFilter": "Filter by date", "delivered": "Delivered orders"}}, "modalReview": {"text": {"title": "Rate Our App!", "content": "Would you like to help us improve? Please take a moment to rate our app. Your feedback is invaluable to us!"}, "button": {"cancel": "Not now, thanks.", "review": "Sure, I'll rate it!"}}, "popupMap": {"text": {"title": "Available Maps", "message": "Below is the list of map apps on your phone"}, "button": {"cancel": "Cancel"}}, "verifyPix": {"click_here": "Click here", "deliverymanText": "to register a pix key", "shopkeeperText": "to see stores missing pix field", "storeMissingPixText": "Store missing pix field"}}