{"forms": {"stores": {"select2": {"label": "Categoria", "placeholder": "Selecionar categorias", "cancelButtonText": "<PERSON><PERSON><PERSON>", "saveButtonText": "<PERSON><PERSON>"}, "select": {"open": {"label": "Loja está...", "placeholder": "Escolha uma opção", "optionsLabels": ["Abe<PERSON>o", "<PERSON><PERSON><PERSON>"]}, "active": {"label": "Estado da loja", "placeholder": "Escolha uma opção", "optionsLabels": ["Inativo", "Ativo"]}}, "input": {"name": {"label": "Nome da loja", "placeholder": "Nome da loja"}, "cnpj": {"label": "CNPJ/CPF", "placeholder": "CNPJ/CPF"}, "email": {"label": "Email", "placeholder": "<EMAIL>"}, "phone": {"label": "Telefone", "placeholder": "Telefone"}, "slug": {"label": "Slug", "placeholder": "Slug"}, "description": {"label": "Descrição", "placeholder": "Escreva aqui..."}, "pixKey": {"label": "Pix", "placeholder": "Pix"}}, "toasts": {"create_success": "Loja criada com sucesso.", "create_hours_success": "<PERSON><PERSON><PERSON><PERSON><PERSON> da loja criados com sucesso.", "create_error": "Não foi possível criar os horários da loja.", "edit_hours_success": "Hor<PERSON><PERSON>s da loja alterados com sucesso", "edit_success": "Os dados da loja foram editados com sucesso.", "edit_error": "Erro! Não foi possível alterar os horários da loja.", "not_found": "Erro! Não foi possível carregar as loja desse usuário"}, "button": {"banner": "Upload banner", "deleteBanner": "Excluir banner", "submit": "<PERSON><PERSON> loja", "update": "Alterar loja", "viewMore": "Ver mais"}}, "selectProfile": {"title": "Perfil", "select": "Selecione o tipo de perfil:", "noOptions": "Você não tem mais opções", "remove": "Remover perfil", "add": "<PERSON><PERSON><PERSON><PERSON> perfil", "save": "<PERSON><PERSON>", "profileNames": {"shopkeeper": "<PERSON>ji<PERSON>", "deliveryman": "<PERSON><PERSON><PERSON><PERSON>", "client": "Cliente"}, "profileStatus": {"requested": "Solicitado", "review": "<PERSON><PERSON><PERSON><PERSON>", "approved": "<PERSON><PERSON><PERSON>", "disapproved": "Reprovado", "pending_documents": "Documentos pendentes", "sent_document": "Documentos enviados", "filled_data": "Dad<PERSON> pre<PERSON>"}}, "client": {}, "shopkeeper": {"button": {"addFiles": "Inserir arquivos", "files": "<PERSON>r<PERSON><PERSON>"}}, "deliveryman": {"text": {"documentsUnderReview": "Documentos sendo analisados"}, "input": {"cpf": {"label": "CPF", "placeholder": "**************"}, "cnh": {"label": "Inserir CNH"}, "vehicleDocument": {"label": "Inserir documento do veículo"}, "criminalRecord": {"label": "Inserir antecedentes criminais"}}, "select": {"type": {"label": "Tipo de entrega", "placeholder": "Tipo de entrega", "options": ["Todos horários", "Flexível"]}, "modality": {"label": "Modalidade", "placeholder": "Selecione a modalidade", "options": ["Bicicleta", "Moto", "<PERSON><PERSON>"]}}, "pixField": {"label": "Pix"}, "button": {"signIn": "Entrar", "addFiles": "Inserir arquivos", "files": "<PERSON>r<PERSON><PERSON>", "click_here": "Clique aqui"}}, "card": {"input": {"cardHolder": {"label": "Titular do Cartão", "placeholder": "<PERSON><PERSON>"}, "cardNumber": {"label": "Número do Cartão", "placeholder": "0000 0000 0000 0000"}, "expiration": {"label": "Expiração", "placeholder": "00/00"}, "securityCode": {"label": "CVV", "placeholder": "000"}}, "checkbox": {"saveCard": {"placeholder": "<PERSON><PERSON>."}}, "button": {"confirm": "Confirmar"}, "select": {"paymentMethod": {"label": "Forma de Pagamento", "placeholder": "Selecione o método de pagamento", "options": ["<PERSON><PERSON><PERSON><PERSON>", "Débito"]}, "installments": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Selecione o parcelamento", "option": "<PERSON><PERSON><PERSON>(s)"}}, "errors": {"expiredCard": "Cartão expirado"}, "toasts": {"create_error": "Erro! Não foi possível criar cartão!", "payment_failure": "Falha no processo de pagamento. Por favor, verifique seus dados e tente novamente."}}}, "auth": {"signin": {"button": {"signIn": "Entrar", "createAccount": "Não tem uma conta?", "signup": "Cadastre-se", "signInGoogle": "Entrar com Google", "signInFacebook": "Entrar com Facebook", "forgotPassword": "<PERSON><PERSON><PERSON> a senha"}, "placeholder": {"email": "Email", "password": "<PERSON><PERSON>"}, "label": {"email": "Email", "password": "<PERSON><PERSON>"}, "header": {"tittle": "<PERSON><PERSON>"}, "errors": {"incorrect_credentials": "Dados incorretos. Verifique novamente.", "email_incorrect": "Email incorreto.", "password_incorrect": "<PERSON><PERSON> incorreta."}}}, "users": {"create": {"button": {"register": "<PERSON><PERSON>", "address": "Adicione um endereço", "terms": "Termos & Condições", "accept": "Eu aceito os Termos & Condições", "errorCheckbox": "Você precisa aceitar os Termos & Condições", "edit": "<PERSON><PERSON><PERSON>u<PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>"}, "placeholder": {"firstName": "Nome", "lastName": "Sobrenome", "phone": "(00) 0000-0000", "cpf": "000.000.000-00", "email": "Email", "password": "<PERSON><PERSON>", "address": "Endereço selecionado", "passwordConfirm": "Confirmar <PERSON><PERSON><PERSON>", "retypePass": "Digite novamente a senha", "dateOfBirth": "01/01/2000", "pix": "Pix"}, "label": {"name": "Nome", "email": "Email", "phone": "Telefone (com DDD)", "cpf": "CPF", "password": "<PERSON><PERSON>", "sms": "Sms", "whatsapp": "WhatsApp", "passTrackerContain": "A senha deve conter uma letra maiúscula, uma letra minúscula, um caractere especial, um número e no mínimo {arg} caracteres.", "passwordRequirements": "Regras para senha", "contact_preference": "Preferência de Contato", "dateOfBirth": "Data de nascimento (Dia/mês/ano)", "pix": "Pix"}, "text": {"terms": "Li e concordo com os ", "titleTerms": "Termos & Condições", "checkbox": "Você gostaria de ser contactado por: ", "complete_register": "No primeiro acesso é necessário informar alguns dados. Finalize o cadastro para acessar a aplicação."}, "header": {"tittle": "<PERSON><PERSON><PERSON>"}, "toasts": {"create_success": "Sucesso! Usuário criado!", "create_error": "Não foi possível criar usuário!", "edit_success": "Sucesso! Usuário editado!", "edit_error": "Erro! Não foi possível editar usuário!", "name": "Solicitamos seu nome para sua identificação no app", "phone": "Número utilizado para entrarmos em contato", "cpf": "Solicitamos seu CPF para sua identificação no app", "email": "Email utilizado para acessar a plataforma", "password": "Senha utilizada para acessar a plataforma", "passwordConfirm": "Digite novamente a senha para confirmar", "dateOfBirth": "Sua data de nascimento a ser cadastrada na aplicação", "pix": "Chave pix utilizada para transações"}, "errors": {"user_alreadyExists": "O usuário já existe.", "user_register_cognito_failed": "Falha de registro do usuário no cognito.", "userPhone_alreadyExists": "Esse número de telefone já está associado a outra conta.", "cpf_alreadyExists": "Esse CPF já está associado a outra conta", "email_alreadyExists": "Esse Email já está associado a outra conta"}}, "edit": {"button": {"edit": "<PERSON><PERSON>"}, "placeholder": {"firstName": "Nome", "lastName": "Sobrenome", "phone": "Telefone (com DDD)", "email": "Email"}, "label": {"name": "Nome", "email": "Email"}, "header": {"tittle": "<PERSON><PERSON>"}, "errors": {}, "components": {"datePicker": {"text": {"dateSelection": "Selecione uma data"}, "button": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>"}}}}, "config": {"placeholder": {"selectorName": "Idioma", "english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portuguese": "Português"}, "errors": {}}, "profile": {"header": {"tittle": "Perfil"}, "placeholder": {"withoutaddress": "Nenhum endereço encontrado"}, "errors": {"profile_notFound": "Perfil não encontrado."}}, "permission": {"errors": {"permission": "Permissão não encontrada."}}, "recoverPassword": {"button": {"sendCode": "Enviar código de verificação", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "alreadyHaveCode": "Já tenho o código"}, "header": {"forgotPassword": "<PERSON><PERSON><PERSON> se<PERSON>a", "passwordRequirements": "Regras para senha"}, "label": {"email": "Email", "code": "Código de verificação", "newPassword": "Nova senha", "confirmNewPassword": "Digite novamente a senha"}, "placeholder": {"email": "<EMAIL>", "code": "Código de verificação", "newPassword": "Nova senha", "confirmNewPassword": "Confirmar nova senha"}, "text": {"enterEmail": "Insira seu email para o processo de verificação. Enviaremos um código para seu email/celular.", "enterCode": "Insira o código que você recebeu no seu email/celular e defina uma nova senha para sua conta.", "passTrackerContain": "A senha deve conter uma letra maiúscula, uma letra minúscula, um caractere especial, um número e no mínimo {arg} caracteres."}, "toasts": {"code_sent": "Código enviado com sucesso para seu email/phone!", "code_not_sent": "Ocorreu um erro. Código de verificação não enviado.", "password_changed": "Senha alterada com sucesso.", "invalid_verification_code": "Codigo de verificação inválido"}}, "errors": {"user_notFound": "Us<PERSON><PERSON>rio não encontrado."}, "reactivateAccount": {"label": {"title": "Sua conta foi temporariamente desativada. Mas você pode recuperá-la em um período de 30 dias", "subtitle": "Para reativar informe seu email e cpf.", "reactivate": "Conta reativada! Realize o login novamente para usar o aplicativo"}, "button": {"reactivate": "Reativar conta"}}}, "address": {"home": {"header": {"title": "Endereços", "editAddress": "<PERSON><PERSON>"}, "menu": {"map": "Ver no mapa", "default": "Definir como padrão", "delete": "Excluir"}, "text": {"default": "Padrão"}}, "create": {"placeholder": {"nickname": "Apelido do endereço", "city": "Cidade", "complement": "Complemento", "country": "<PERSON><PERSON>", "district": "Bairro", "location": "-15.7975154, -47.8918874", "number": "Número", "postcode": "CEP", "state": "Estado (UF)", "street": "<PERSON><PERSON>"}, "label": {"nickname": "Apelido do endereço", "city": "Cidade", "complement": "Complemento", "country": "<PERSON><PERSON>", "district": "Bairro", "location": "Localização", "number": "Número", "postcode": "CEP", "state": "Estado (UF)", "street": "<PERSON><PERSON>", "brasil": "Brasil"}, "header": {"map": "Aprimore a localização"}, "selectOptions": ["Pedido", "Entrega"], "button": {"submit": "<PERSON><PERSON>", "yes": "<PERSON>m", "no": "Não", "not_found": "Nenhum endereço cadastrado", "edit": "<PERSON><PERSON><PERSON>", "addAddress": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmar"}, "errors": {}, "text": {"deleteModalTittle": "Deletar endereço", "deleteModalText": "Você deseja deletar esse endereço?", "selectAddressLocationMessage": "Selecione a localização do seu endereço no mapa"}, "toasts": {"create_success": "Sucesso! Endereço criado!", "create_error": "Não foi possível criar endereço!", "edit_success": "Sucesso! Endereço editado!", "edit_error": "Não foi possível editar endereço!", "delete_success": "Sucesso! Endereço excluído!", "delete_error": "Erro! Não foi possível excluir Endereço!", "postcode_error": "Ocorreu uma falha ao consultar o CEP. Por favor, prossiga com o cadastro manual."}}, "edit": {"placeholder": {"nickname": "Apelido", "city": "Cidade", "complement": "Complemento", "country": "<PERSON><PERSON>", "district": "Bairro", "location": "Localização", "number": "Número", "postcode": "CEP", "state": "Estado (UF)", "street": "<PERSON><PERSON>"}, "label": {"nickname": "Apelido", "city": "Cidade", "complement": "Complemento", "country": "<PERSON><PERSON>", "district": "Distrito", "location": "Localização", "number": "Número", "postcode": "CEP", "state": "Estado", "street": "<PERSON><PERSON>"}, "selectOptions": ["Pedido", "Entrega"], "button": {"submit": "<PERSON><PERSON>", "map": "Atualizar no mapa"}, "errors": {"address_notFound": "Endereço não encontrado."}}, "components": {"confirm_address": {"confirm_address": "Confirma<PERSON>", "address_confirmed": "Endereço confirmado", "error_required": "É necessário confirmar a localização!"}}}, "settings": {"changePassword": {"button": {"updatePassword": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {}, "radiobutton": {}, "label": {"previousPassword": "<PERSON><PERSON> antiga", "proposedPassword": "Nova senha", "confirmProposedPassword": "Confirme a nova senha"}, "placeholder": {"previousPassword": "<PERSON><PERSON> antiga", "proposedPassword": "Nova senha", "confirmProposedPassword": "Confirme a nova senha"}, "header": {}, "errors": {"invalidPassword": "<PERSON><PERSON>", "passwordMatch": "A senha não está igual", "wrong_old_password": "Senha antiga incorreta. A senha antiga deve ser igual a informada no cadastro inicial"}, "toasts": {"successful_update": "Senha atualizada com sucesso. Realize o login com a nova senha"}}, "home": {"button": {"yes": "<PERSON>m", "no": "Não", "cancel": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON>", "address": "Endereço", "personalData": "<PERSON><PERSON> pessoais", "deleteAccount": "Deletar conta", "addProfile": "<PERSON><PERSON><PERSON><PERSON> perfil", "changeProfile": "<PERSON><PERSON>r perfil", "changePassword": "<PERSON><PERSON><PERSON><PERSON>", "addPhoto": "Inserir Foto", "phone_settings": "Acessar <PERSON>", "documents": "Documentos", "faq": "Perguntas Frequentes", "privacy_policy": "Política de Privacidade", "terms_of_use": "Termos de Uso", "contract": "Contrato", "about": "Sobre"}, "text": {"deleteModalTittle": "Deletar conta", "deleteModalText": "Você deseja remover essa conta?", "deleteModalReactivate": "Ao remover a conta, você poderá reativar até 30 dias após exclusão. Ao fim desse período a conta será excluída permanentemente.", "profile_picture": "Foto do Perfil"}, "header": {"tittle": "Home"}, "toasts": {"delete_success": "Sucesso! Usuário removido!", "delete_error": "<PERSON>rro! Não foi possível remover usuário!"}, "logout": {"button": {"logout": "<PERSON><PERSON>"}, "toasts": {"logout": "Sessão encerrada."}, "modal": {"title": "<PERSON><PERSON><PERSON>", "text": "Tem certeza que quer sair?", "btnPositive": "<PERSON><PERSON>", "btnNegative": "Voltar"}}, "errors": {}}, "verifyEmail": {"button": {"yes": "<PERSON>m", "no": "Não", "cancel": "<PERSON><PERSON><PERSON>", "logout": "Logout", "confirm": "Verificar código", "click_here": "Clique aqui", "validate_email": "Validar email"}, "text": {"verify_email_modal": "Para utilizar todas as funcionalidades do aplicativo é necessário que você valide seu email de cadastro", "verify_email_modal_tittle": "Verificação de email", "check_email": "Confira o seu email", "check_email_caption": "Por favor, insira o código de 6 dígitos que enviamos para: ", "code_info": "Não recebeu o código?", "resend_code": "Reenviar código ", "click_here": "para verificar seu email"}, "header": {"tittle": "Home"}, "toasts": {"delete_success": "Sucesso! Usuário excluído!", "delete_error": "Erro! Não foi possível excluir usuário"}, "logout": {"button": {"logout": "Logout"}, "toasts": {"logout": "Você saiu do sistema"}, "modal": {"title": "Logout", "text": "Tem certeza que deseja sair?", "btnPositive": "<PERSON><PERSON>", "btnNegative": "Voltar"}}, "errors": {"invalid": "<PERSON><PERSON><PERSON>"}}, "modalContent": {"title": {"faq": "Perguntas Frequentes", "privacyPolicy": "Política de Privacidade", "termsOfUse": "Termos de Uso", "contract": "Contrato", "about": "Sobre"}, "btnClose": "<PERSON><PERSON><PERSON>", "text": {"not_found": "Não encontrado"}}}, "stores": {"create": {"button": {"addstore": "<PERSON><PERSON><PERSON><PERSON> loja", "updatestore": "<PERSON><PERSON> loja", "listStore": "<PERSON><PERSON>", "banner": "Upload banner", "yes": "<PERSON>m", "no": "Não", "cancel": "<PERSON><PERSON><PERSON>", "saveChanges": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "storeSettings": "Configurações da loja", "storeHoursSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> loja", "addressNotFound": "Nenhum endereço encontrado.", "selectAll": "Selecionar tudo"}, "text": {"deleteModalTittle": "Deletar <PERSON>", "deleteModalText": "Você deseja deletar essa loja?", "modalReplicateHours": "Selecione os dias que deseja replicar:", "modalReplicateHoursTittle": "<PERSON><PERSON><PERSON>", "storeNotFound": "<PERSON>enhuma loja encontrada", "categoriesNotFound": "Nenhuma categoria encontrada", "permission_denied": "Permissão de localização negada", "modalProfileTitle": "Imagem perfil", "modalBannerTitle": "Imagem banner", "timeSelection": "Selecione um horário"}, "radiobutton": {"open": "Abe<PERSON>o", "closed": "<PERSON><PERSON><PERSON>", "active": "Ativa", "inactive": "Inativa", "status": "Status da loja", "storeis": "A loja está...", "chooseOption": "Escolha uma opção"}, "selectOptionsStatus": ["Ativa", "Inativa"], "selectOptionsOpen": ["Abe<PERSON>o", "<PERSON><PERSON><PERSON>"], "placeholder": {"email": "Email", "name": "Nome da loja", "cnpj": "CNPJ/CPF", "phone": "Telefone", "description": "Descrição", "type_here": "Escreva aqui...", "slug": "Slug", "open": "Aberto às", "close": "<PERSON><PERSON><PERSON>", "permission_denied": "Algumas loja não estarão disponíveis devido a permissão de localização ter sido negada"}, "header": {"tittle": "<PERSON><PERSON>", "editTitle": "<PERSON><PERSON> loja", "createStore": "<PERSON><PERSON><PERSON> loja", "setStoreHours": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings": "Configurações", "StoreSettings": "Configurações gerais da loja", "storeCategory": "Categorias", "productsSuggestions": "Sugestões", "options_address": "Entregar para"}, "errors": {"specific_test": "Erro específico", "is_greater": "Deve aumentar o hor<PERSON>rio", "store_notFound": "Loja não encontrada", "slug_already_exists": "Slug já existe.", "cnpj_required": "CNPJ obrigatório.", "cpf_cnpj_already_exists": "CPF/CNPJ já existe.", "email_alreadyExists": "<PERSON>ail j<PERSON>e."}, "toasts": {"create_success": "Loja criada com sucesso!", "create_error": "Erro! Não foi possível criar a loja.", "create_hours_success": "Ho<PERSON><PERSON><PERSON><PERSON> da loja criados com sucesso!", "create_hours_error": "Erro! Não foi possível criar os horários da loja.", "edit_hours_success": "Hor<PERSON><PERSON>s da loja alterados com sucesso!", "edit_success": "Os dados da loja foram editados com sucesso!", "edit_error": "Erro! Não foi possível alterar os horários da loja."}, "workingHours": ["Domingo", "Segunda", "<PERSON><PERSON><PERSON>", "Quarta", "<PERSON><PERSON><PERSON>", "Sexta", "Sábado"]}, "edit": {"button": {"addstore": "<PERSON><PERSON><PERSON><PERSON> loja", "updatestore": "<PERSON><PERSON><PERSON><PERSON>", "listStore": "<PERSON><PERSON>", "banner": "Upload banner", "yes": "<PERSON>m", "no": "Não", "cancel": "<PERSON><PERSON><PERSON>", "saveChanges": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "storeSettings": "Configurações da loja", "storeHoursSettings": "Horários de funcionamento"}, "text": {"deleteModalTittle": "Deletar <PERSON>", "deleteModalText": "Você deseja deletar essa loja?", "modalProfileTitle": "Imagem perfil", "modalBannerTitle": "Imagem banner", "disabledByAdmin": "Desativada pelo Administrador", "modalModeration": "Justificativa do Administrador", "edit_general_info": "Informações gerais", "edit_categories": "Categorias", "edit_products": "<PERSON><PERSON><PERSON>", "edit_hours": "Horários de funcionamento", "edit_settings": "Configurações", "edit_address": "Endereço", "edit_review": "Avaliações", "edit_delete": "Excluir loja", "edit_leave": "Remover moderação", "categories": "Categorias", "edit_banner": "Selecione um banner", "edit_avatar": "Selecione um avatar", "delete_warning": "Você gostaria de deletar essa loja?", "leave_warning": "Você gostaria de deixar a moderação dessa loja?", "files": "<PERSON>r<PERSON><PERSON>", "edit_gallery": "Galeria", "edit_moderators": "Moderadores"}, "radiobutton": {"open": "Abe<PERSON>o", "closed": "<PERSON><PERSON><PERSON>", "active": "Ativa", "inactive": "Inativa", "status": "Status da loja", "storeis": "A loja está...", "chooseOption": "Escolha uma opção"}, "selectOptionsStatus": ["Ativada", "Desativada"], "selectOptionsOpen": ["Aberta", "<PERSON><PERSON><PERSON>"], "placeholder": {"email": "Email", "name": "Nome da loja", "cnpj": "CNPJ/CPF", "phone": "Telefone (com DDD)", "description": "Descrição", "type_here": "Digite Aqui...", "slug": "Slug", "open": "<PERSON><PERSON><PERSON><PERSON>", "close": " <PERSON><PERSON><PERSON><PERSON> fech<PERSON>"}, "header": {"tittle": "<PERSON><PERSON><PERSON><PERSON> loja", "editTitle": "<PERSON><PERSON> loja", "tittleList": "Listar lojas", "setStoreHours": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings": "Configurações", "StoreSettings": "Configurações gerais da loja"}, "errors": {"specific_test": "Erro específico", "is-greater": "Aumentar o horário", "store_notFound": "Loja não encontrada", "slug_already_exists": "slug já existe.", "cnpj_already_exists": "Cnpj já existe.", "email_already_exists": "<PERSON>ail j<PERSON>e."}, "toasts": {"create_success": "Loja criada com sucesso!", "create_hours_success": "Ho<PERSON><PERSON><PERSON><PERSON> da loja criados com sucesso!", "create_error": "Erro! Não foi possível criar os horários da loja.", "edit_hours_success": "Hor<PERSON><PERSON>s da loja alterados com sucesso!", "edit_success": "Os dados da loja foram editados com sucesso!", "edit_error": "Erro! Não foi possível alterar os horários da loja."}, "workingHours": ["Domingo", "Segunda", "<PERSON><PERSON><PERSON>", "Quarta", "<PERSON><PERSON><PERSON>", "Sexta", "Sábado"]}, "home": {"text": {"deleteModalTitle": "Excluir loja", "deleteModalText": "Você deseja excluir essa loja?", "minimumOrder": "Pedido mín. R$ {arg}", "feedbackInfo": "{arg} ({arg})", "waitingTime": "{arg} - {arg} min", "empty": "Loja não tem produtos cadastrados", "productsNotFound": "Nenhum produto encontrado", "active": "Ativo", "inactive": "Inativo", "open": "Abe<PERSON>o", "close": "<PERSON><PERSON><PERSON>", "toleranceTimeEnd": "Esta loja não está aceitando pedidos. Tente mais tarde.", "toleranceTimeEndCart": "Esta loja não está aceitando pedidos. Compre esses itens mais tarde ou escolha outra loja.", "inviteText": "A loja {arg} está te convidando para ser moderador.", "invite": "<PERSON><PERSON><PERSON>", "verifyPix": "<PERSON>á lojas sem chave pix"}, "button": {"addstore": "<PERSON><PERSON><PERSON><PERSON> loja", "addressNotFound": "Nenhum endereço encontrado.", "yes": "<PERSON>m", "no": "Não", "delete": "Excluir", "settings": "Configurações", "orders": "Pedidos", "reviews": "Avaliações", "accept": "Aceitar", "refuse": "Recusar", "click_here": "Clique aqui"}, "selectOptionsStatus": ["Ativo", "Inativo"], "selectOptionsOpen": ["Abe<PERSON>o", "<PERSON><PERSON><PERSON>"]}, "storesSettings": {"button": {"createSettings": "<PERSON><PERSON><PERSON> configu<PERSON><PERSON><PERSON><PERSON>", "updateSettings": "Atualizar configura<PERSON>"}, "text": {"paymentMethod": "Métodos de pagamento", "deliveryRangeTitle": "O raio de entrega não é exibido para o cliente", "deliveryRange": "O raio de entrega é a distância em linha reta. E o valor exibido para o cliente é a menor rota possível. O percurso do endereço do cliente até a loja (com as condições do tráfego) pode ser maior que o valor do raio de entrega, pois a distância em linha reta é menor que o raio."}, "placeholder": {"maxHour": "Tolerância máx. pedido (min)", "deliveryRange": "Raio de entrega (km)", "cash": "<PERSON><PERSON><PERSON>", "debt": "Débito", "credit": "<PERSON><PERSON><PERSON><PERSON>", "ticket": "Boleto", "pix": "Pix", "facebook": "Facebook", "instagram": "Instagram", "tiktok": "TikTok"}, "header": {"editTitle": "Editar configurações da loja"}, "error": {"required": "Campo requirido", "moreThanZero": "<PERSON><PERSON> ser maior que 0", "payment_method": "Selecione pelo menos 1 método de pagamento"}, "toasts": {"create_success": "Configurações da loja criadas com sucesso!", "create_error": "Erro! Não foi possível criar as configura<PERSON><PERSON><PERSON> da loja.", "edit_success": "Configurações da loja alterados com sucesso!", "edit_error": "Erro! Não foi possível alterar as configurações da loja.", "tolerance_order": "Tempo limite (em minutos) para recebimento de novos pedidos antes da loja fechar. O horário limite é calculado pela diferença entre o horário de fechamento da loja e os minutos da tolerância."}}, "storeHours": {"button": {"updateWorkingHours": "<PERSON><PERSON>"}}, "storeReviews": {"header": {"title": "Avaliações", "rateStore": "Avaliar loja"}, "text": {"reviews": "{arg} avaliações", "rateStoreTitle": "O que você achou desta loja?", "rateStoreText": "Avalie de 1 a 5 estrelas"}, "button": {"createReview": "Fazer uma avaliação", "submitReview": "Enviar avaliação"}, "placeholder": {"comment": "Coment<PERSON>rio (Opcional)"}}, "storeDetails": {"text": {"address": "Endereço", "categories": "Categorias", "paymentMethod": "Método de pagamento", "workingHours": "Horário de Funcionamento", "description": "Descrição", "phone": "Contato", "storeClosed": "<PERSON><PERSON><PERSON>"}, "button": {"gallery": "Galeria"}}, "storeModerators": {"text": {"title": "Adicionar <PERSON>", "owner": "Don<PERSON>", "moderator": "Moderador", "blocked": "Bloqueado", "invite": "<PERSON><PERSON><PERSON>", "missingPix": "Cadastro Pix"}, "modals": {"title": {"remove": "Remover Moderador", "block": "Bloquear Moderador", "unblock": "Desbloquear Moderador"}, "body": {"remove": "Tem certeza que deseja remover este moderador?", "block": "Tem certeza que deseja bloquear este moderador?", "unblock": "Tem certeza que deseja desbloquear este moderador?"}, "button": {"no": "Não", "yes": "<PERSON>m"}}, "placeholder": {"email": "Email do usuário"}, "toasts": {"email": "Email utilizado para acessar a plataforma", "create_success": "Moderador adicionado com sucesso.", "update_success": "Status do moderador alterado com sucesso.", "remove_success": "Moderador removido com sucesso.", "refuse_success": "Convite recusado com sucesso.", "accept_success": "Convite aceito com sucesso.", "create_error": "Erro! Não foi possível incluir o moderador.", "update_error": "Erro! Não foi possível alterar o status do moderador.", "remove_error": "Erro! Não foi possível remover Moderador.", "refuse_error": "Erro! Não foi possível recusar o convite.", "accept_error": "Erro! Não foi possível aceitar o convite.", "blocked": "Sua moderação está bloqueada pelo dono."}, "buttons": {"confirm": "Confirmar"}, "status": {"active": "Ativo", "blocked": "Bloqueado", "pending": "Pendente"}, "errors": {"is_already": "Já existe um moderador com esse email.", "not_found": "Us<PERSON><PERSON>rio não encontrado.", "not_profile": "Usuário não tem o perfil de lojista."}}}, "product": {"create": {"header": {"title": "<PERSON><PERSON><PERSON>", "editProduct": "<PERSON>ar produto"}, "label": {"name": "Nome", "short_description": "Descrição curta", "description": "Descrição", "price": "Preço", "sale_price": "Preço de venda", "sku": "S<PERSON>", "sku_info": "Código <PERSON>, utilizado na identificação do produto na loja.", "active": "Ativo", "icon": "Ícone", "banner": "Banner", "category": "Categoria", "subcategory": "Subcategoria", "attributes": "Atributos", "textAreaLength": "{arg}/{arg}", "preparationTime": "Tempo de preparação"}, "placeholder": {"name": "Nome", "short_description": "Descrição curta", "description": "Descrição", "price": "R$ 00.00", "sale_price": "R$ 00.00", "sku": "132LSKFLS5DS5F", "active": "Selecione status", "icon": "Icon", "banner": "Banner", "category": "Selecionar categorias", "subcategory": "Selecione subcategorias", "selected_category": "Categorias selecionadas", "selected_subcategory": "Subcategorias selecionadas", "required": "* é obrigatório", "category_notFound": "Categorias não estão disponíveis para a loja", "preparationTime": "Tempo em minutos"}, "toasts": {"create_success": "Produto criado com sucesso!", "edit_success": "Produto editado com sucesso!", "delete_success": "Produto excluido com sucesso!", "create_error": "Erro! Não foi possível criar produto!", "edit_error": "Erro! Não foi possível editar produto!", "delete_error": "Erro! Não foi possível excluir produto!", "details_error": "Error! Não foi possível carregar os detalhes do produto!"}, "button": {"submit": "<PERSON><PERSON> produto", "addproduct": "Ad<PERSON><PERSON><PERSON> produto", "update": "Alterar produto", "not_found": "Nenhum produto cadastrado", "yes": "<PERSON>m", "no": "Não", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "add": "Add", "photos": "Fotos e videos", "attribute": "Atributos", "save": "<PERSON><PERSON>"}, "text": {"deleteModalTittle": "Deletar Produto", "deleteModalText": "Você deseja deletar esse produto?", "modalProductTitle": "Imagem produto"}, "errors": {"product_notCreated": "Não foi possível criar o produto", "product_notUpdated": "Não foi possível atualizar o produto", "product_notDeleted": "Não foi possível excluir o produto", "product_notFound": "Não foi possível encontrar o produto", "category_notFound": "Não foi possível encontrar a categoria", "subcategory_notFound": "Não foi possível encontrar a subcategoria", "product_subcategory_notDeleted": "Não foi possível excluir as subcategorias", "product_category_notDeleted": "Não foi possível excluir as categorias", "product_categories_notUpdated": "Não foi possível atualizar as categorias", "product_subcategories_notUpdated": "Não foi possível atualizar as subcategorias", "product_attributes_notUpdated": "Não foi possível atualizar os atributos", "product_sku_already_exists": "SKU já existe"}, "selectOptionsStatus": ["Ativo", "Inativo"]}, "edit": {"label": {"name": "Nome", "short_description": "Descrição curta", "description": "Descrição", "price": "Preço", "sale_price": "Preço de venda", "sku": "S<PERSON>", "sku_info": "Código <PERSON>, utilizado na identificação do produto na loja.", "active": "Ativo", "icon": "Ícone", "banner": "Banner", "category": "Categoria", "subcategory": "Subcategoria", "attributes": "Atributos", "textAreaLength": "{arg}/{arg}", "preparationTime": "Tempo de preparação (minutos)"}, "placeholder": {"name": "Nome", "short_description": "Descrição curta", "description": "Descrição", "price": "R$ 00.00", "sale_price": "R$ 00.00", "sku": "132LSKFLS5DS5F", "active": "Selecione status", "icon": "Icon", "banner": "Banner", "category": "Selecionar categorias", "subcategory": "Selecione subcategorias", "selected_category": "Categorias selecionadas", "selected_subcategory": "Subcategorias selecionadas", "required": "* é obrigatório", "category_notFound": "Categorias não estão disponíveis para a loja", "preparationTime": "Tempo em minutos"}, "text": {"edit_general_info": "Informações gerais", "edit_categories": "Categorias", "edit_attributes": "Atributos", "edit_gallery": "Galeria", "subcategory": "Subcategoria", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "active": "Ativo", "inactive": "Inativo", "modalProductTitle": "Imagem produto", "disabledByAdmin": "Desativado pelo Administrador.", "modalModeration": "Justificativa do Administrador"}, "button": {"viewMore": "Ver mais"}, "toasts": {"edit_success": "Produto editado com sucesso!"}}, "FavoriteProducts": {"text": {"distance": "Distância", "not_found": "Nenhum produto favorito encontrado"}}, "search": {"findProduct": "Pesquisar em todo o app", "notFound": "Nenhum produto encontrado.", "todayOrders": "Qual o pedido de hoje?", "notSearched": "Digite alguma palavra relacionada ao que você deseja.", "saveFilter": "Filtrar resultados", "loadMore": "<PERSON><PERSON><PERSON> mais produtos", "priceValidation": "Preço mínimo deve ser menor que o preço máximo", "filters": {"all": "<PERSON><PERSON><PERSON>", "category": "Categoria", "subcategory": "Subcategoria", "storeName": "<PERSON><PERSON>", "name": "Nome do produto", "distance": "Distância", "attributes": "Atributos do produto", "trending": "Produto em destaque", "discounts": "Produto em promoção", "price": "Preço"}, "label": {"minPrice": "Preço Mínimo", "maxPrice": "Preço Máximo"}, "placeholder": {"minPrice": "Digite o valor mínimo do pedido", "maxPrice": "Digite o valor máximo do pedido", "distance": "Esta é a distância em linha reta até a loja. A distância real pode variar"}}}, "attribute": {"create": {"header": {"title": "Atributos Específicos", "title_modal": "Atributo Específico", "attribute_association": "Associar Atributo"}, "label": {"name": "Nome", "short_description": "Descrição breve", "required": "Requerido", "type": "Tipo de seleção"}, "placeholder": {"name": "Nome", "short_description": "Pequena Descrição", "input_select": "Buscar Atributo"}, "toasts": {"create_attribute_success": "Atributo criado com sucesso!", "delete_attribute_success": "Atributo excluído com sucesso!", "create_attribute_error": "Erro! Não foi possível criar o atributo.", "delete_attribute_error": "Erro! Não foi possível excluir o atributo.", "loading_attribute_error": "Erro! Não foi possível exibir os atributos."}, "button": {"submit": "Confirmar", "add_attribute": "Novo Atributo", "not_found": "Não encontrado", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "save": "<PERSON><PERSON>"}, "errors": {}, "select_selection_type": ["único", "<PERSON><PERSON><PERSON><PERSON>"]}, "edit": {"placeholder": {}, "label": {}}, "errors": {"attribute_notFound": "Atributo não encontrado.", "product_attribute_alreadyExists": "O produto/atributo já existe.", "product_attribute_notFound": "Produto/Atributo não encontrado."}}, "attribute_option": {"create": {"header": {"title": "Nova opção de atributo"}, "label": {"value": "Opção"}, "placeholder": {"value": "Opção"}, "toasts": {"create_attribute_option_success": "Opção de atributo criada com sucesso!", "delete_attribute_option_success": "Opção de atributo excluída com sucesso!", "create_attribute_option_error": "Erro! Não foi possível criar a opção de atributo.", "delete_attribute_option_error": "Erro! Não foi possível excluir a opção de atributo."}, "button": {"submit": "Confirmar", "add_attribute_option": "Adicionar nova opção", "not_found": "Não encontrado", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "save": "<PERSON><PERSON>"}}, "edit": {"placeholder": {}, "label": {}}, "errors": {"attribute_option_notFound": "Opção do atributo não encontrada.", "product_attribute_option_alreadyExists": "A opção de produto/atributo já existe.", "product_attribute_option_notFound": "Opção de produto/atributo não encontrada.", "product_attribute_option_notUpdated": "Não foi possível atualizar a opção do atributo"}}, "select2": {"button": {"cancel": "<PERSON><PERSON><PERSON>", "select": "Selecionar"}}, "attachmentFile": {"button": {"choose": "Selecionar", "close": "<PERSON><PERSON><PERSON>", "cancel_token": "Operação cancelada pelo usuário.", "add_file": "Adicionar <PERSON>", "view_gallery": "<PERSON><PERSON><PERSON>"}, "errors": {"remove_file": "Falha ao remover arquivo", "add_file": "Falha ao fazer upload", "init_files": "Ocorreu um erro na inicialização dos anexos", "cancellation": "Falha no cancelamento", "maxSizeFile": "Tamanho máximo permitido é {arg} MB"}, "toasts": {"download_success": "Download feito com sucesso!", "upload_success": "Upload feito com sucesso!", "download_error": "Download falhou.", "upload_error": "Upload falhou.", "select_error": "Falha na seleção do arquivo.", "just_one_document": "Apenas um documento permitido"}, "badge": {"canceled": "Cancelado"}, "text": {"chooseFiles": "Escolher os arquivos", "noFiles": "Nenhum arquivo encontrado"}}, "imagePicker": {"button": {"camera": "Câmera", "gallery": "Galeria", "delete": "Excluir"}, "errors": {"did_cancel": "<PERSON><PERSON><PERSON> de imagem cancelado pelo usuário.", "picker_failed": "Erro ao selecionar imagem.", "remove_file": "Falha ao remover o arquivo", "add_file": "Falha no upload do arquivo"}, "toasts": {"download_success": "Download com sucesso!", "upload_success": "Upload com sucesso!", "download_error": "Download falhou.", "upload_error": "Upload falhou.", "select_error": "Falha na seleção do arquivo.", "loading_failure": "Falha no carregamento."}, "maxSizeFile": "Tamanho máximo do arquivo:"}, "generic": {"button": {"moreDetails": "<PERSON><PERSON>"}, "errors": {"generic_message": "Algum erro aconteceu", "label": "Oops... Algum erro aconteceu", "required": "Campo obrigatório", "email": "Entre com um email válido", "short_field": "Deve ser maior que {arg} caracteres", "email_invalid": "Entre com um email válido!", "phone_invalid": "Digite um telefone válido!", "passwordMinSix": "<PERSON><PERSON><PERSON> {arg} caracteres", "email_alreadyExists": "<PERSON>ail j<PERSON>e.", "phone_short_field": "<PERSON><PERSON><PERSON> {arg} n<PERSON><PERSON><PERSON>", "email_incorrect": "Email incorreto", "incorrect_credentials": "Dados incorretos. Verifique novamente.", "password_incorrect": "Senha incorreta", "password_invalid": "<PERSON><PERSON>.", "invalid": "<PERSON>v<PERSON><PERSON><PERSON>", "invalid-cep": "CEP inválido", "more_than_zero": "<PERSON>e ser maior que {arg}", "more_than_years": "Deve ser maior que {arg} anos", "user_notFound": "Usuário não encontrado", "long_field": "Máximo de {arg} caracteres permitidos", "smaller_value": "Esse campo deve ter um valor menor", "greater_value": "Esse campo deve ter um valor maior", "exactly_number": "Deve ser exatamente {arg} caracteres", "customer_code": "O código deve ter exatamente {arg} números", "password_match": "Sen<PERSON> devem ser iguais", "unMatch_rules": "<PERSON><PERSON> corresponder as exig<PERSON><PERSON><PERSON> de se<PERSON>a", "login": "Falha ao autenticar. Digite suas credenciais.", "try_again": "Ocorreu um erro, tente novamente.", "refreshing": "Ocorreu um erro, faça o login novamente.", "forbidden_exception": "Exceção proibida.", "invalid_parameter": "Parâmetro inválido.", "not_authorized": "Não autorizado.", "username_already_exists": "O nome de usuário já existe.", "password_reset_required": "Redefinição de senha necessária.", "user_not_confirmed": "<PERSON><PERSON><PERSON><PERSON> não confirmado.", "max_allowed": "Máximo valor permitido é {arg}", "internal_error": "Erro interno encontrado.", "code_delivery_fail": "Erro ao entregar código de verificação.", "code_mismatch": "<PERSON><PERSON><PERSON> in<PERSON>.", "expired_code": "<PERSON><PERSON><PERSON> expirado.", "check_data_try_again": "Ocorreu um erro, verifique os dados e tente novamente.", "favorite_error": "Erro ao colocar nos favoritos", "profileList": "Insira pelo menos um perfil.", "userDisabledByAdmin": "Conta desabilitada pelo Administrador.", "invalidDate": "Data inválida", "invalid_cpf": "Inválido. Cpf diferente do cadastro", "location_error": "Erro ao carregar localização"}, "axiosErros": {"ECONNABORTED": "Conexão abortada", "ERR_BAD_OPTION": "<PERSON>rro <PERSON>no", "ERR_BAD_OPTION_VALUE": "<PERSON>rro <PERSON>no", "ERR_BAD_REQUEST": "Solicitação inválida", "ERR_BAD_RESPONSE": "Erro do Servidor Interno", "ERR_CANCELED": "Conexão cancelada", "ERR_DEPRECATED": "Erro de descontinuidade", "ERR_FR_TOO_MANY_REDIRECTS": "Erro de redirecionamento", "ERR_NETWORK": "<PERSON><PERSON>", "ETIMEDOUT": "Tempo de resposta esgotado"}, "toasts": {"successful_request": "Solicitação realizada com sucesso!"}}, "routes": {"DrawerHome": "Início", "UserHome": "", "UserApp": "<PERSON><PERSON><PERSON><PERSON>", "ProductHome": "<PERSON><PERSON><PERSON>", "AddressHome": "Endereços", "StoreHome": "<PERSON><PERSON>", "UserEdit": "<PERSON><PERSON>", "ProductEdit": "<PERSON><PERSON>", "AddressEdit": "<PERSON><PERSON>", "StoreEdit": "<PERSON><PERSON>", "UserCreate": "C<PERSON><PERSON> conta", "ProductCreate": "Cadastrar Produto", "AddressCreate": "Cadastrar <PERSON>", "StoreCreate": "<PERSON><PERSON><PERSON><PERSON>", "StoreHoursSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings": "Configurações", "StoreSettings": "Configurações gerais da loja", "SignUp": "C<PERSON><PERSON> conta", "Cart": "<PERSON><PERSON><PERSON>", "Orders": "Pedidos", "CartHome": "<PERSON><PERSON><PERSON>", "OrdersHome": "Pedidos", "StoreList": "Home", "StoreDetails": "<PERSON><PERSON><PERSON>", "FavoriteProducts": "Produtos favoritos", "StoreShowCase": "Vitrine", "ProductDetails": "Detalhes do produto", "OrderSummary": "Resumo do pedido", "OrderDetails": "Detalhes do pedido", "OrderPaymentMethod": "Forma de Pagamento", "UserRecoverPassword": "<PERSON><PERSON><PERSON> a senha", "ChangePassword": "<PERSON><PERSON><PERSON><PERSON>", "UserNotifications": "Notificações", "EditProductGeneralInfo": "<PERSON>ar produto", "EditProductCategories": "Editar categorias", "EditProductAttributes": "<PERSON>ar at<PERSON>", "EditProductGallery": "<PERSON>ar <PERSON>", "EditStoreGallery": "<PERSON>ar <PERSON>", "SocialSignUp": "Completar cadastro", "OrderChat": "Cha<PERSON>", "ReactivateAccount": "Reativar conta", "Documents": "Documentos", "SalesHome": "<PERSON><PERSON><PERSON>", "CheckEmailCpf": "Verificar Email/CPF", "ProductSearch": "Pesquisar produto", "StoreModerators": "Moderadores", "CreateModerators": "<PERSON><PERSON>r <PERSON>", "CreateCard": "Cadastrar novo cartão"}, "home": {"label": {"search": "<PERSON><PERSON><PERSON><PERSON>"}, "placeholder": {"search_store": "Pesquisar loja", "search_categories": "Pesquisar categoria"}, "sort": {"title": "Ordenar por", "options": {"favorite": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nome"}}, "store_card": {"text": {"distance": "Distância"}}}, "multiStepForm": {"skip_step": "Ignorar etapa", "next": "Próximo", "go_back": "Voltar", "submit": "<PERSON><PERSON>", "modal": {"title": "Sair do formulário", "text": "Deseja realmente voltar? As informações inseridas serão perdidas", "button": {"positive": "<PERSON><PERSON>", "negative": "<PERSON><PERSON><PERSON>"}}}, "orders": {"label": {"observation": "Observações", "notFound": "Esse produto não possui atributos", "required": "Obrigatório", "onlyOne": "Apenas 1 opção", "empty": "Nenhum pedido encontrado", "status_menu": "Status Menu", "details": "Detalhes do pedido", "radio_group": "Selecione status", "cancel_reason": "Observação do cancelamento do pedido", "delivery": "Liberar para entrega", "request_deliveryman": "Solicitar um entregador", "quantity_order": "Quantidade", "quantity_items": "Quantidade de items", "quantity_product": "Quantidade de produtos", "unity_price": "Preço Unitário", "total_price": "Preço total", "atributes": "Atributos", "product_name": "Nome do produto", "product_description": "Descrição do produto", "order_item": "{arg}º Item no pedido", "payment_method": "Método de pagamento", "order": "Pedido (subtotal)", "shipping": "Taxa de entrega", "total": "Total", "choose_status": "Selecione o status", "accept_order": "Aceitar pedido", "delivery_route": "<PERSON><PERSON><PERSON> en<PERSON>ga", "release_code": "Liberar código do cliente", "customer_confirmation": "Confirmar c<PERSON><PERSON> do cliente", "reject_order": "<PERSON><PERSON><PERSON><PERSON> pedido", "status_steps": "Status", "noReview": "Sem avaliação", "address": "Endereço", "complement": "Complemento", "orderNumber": "Pedido #", "selectStore": "Selecionar loja", "permission_denied": "Permissão de notificação negada", "select": "Selecionar", "cancel_order": "<PERSON><PERSON>ar <PERSON>", "at": "às {arg}", "items": "<PERSON><PERSON>", "amountCharged": "<PERSON>or cobrado", "deliveryAddress": "Endereço de entrega", "deliveryman": "<PERSON><PERSON><PERSON><PERSON>", "background_location_task_name": "Localização em background", "background_location_task_title": "Localização atual", "customerCode": "Código de en<PERSON>ga", "estimatedDeliveryTime": "Tempo estimado para entrega", "retry_payment": "Realizar pagamento"}, "status": {"all_status": "Todos os status", "preparing_order": "Preparando pedido", "canceled_order": "Pedido cancelado", "rejected_order": "Pedido rejeitado", "on_delivery_route": "Em rota de entrega", "delivered": "<PERSON><PERSON><PERSON>", "placed_order": "Pedido realizado", "payment_made": "<PERSON><PERSON><PERSON>", "canceled": "Cancelado", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "preparing": "Preparando pedido", "waiting_for_the_delivery_person": "<PERSON><PERSON><PERSON><PERSON> pelo entregador", "on_route_to_store": "Entregador indo até a loja", "canceled_delivery": "Entrega cancelada", "pending_payment": "Pagamento pendente", "canceled_payment_failure": "Falha no pagamento"}, "placeholder": {"observation": "Ex: Sem cebola, sem salada...", "address": "Selecione o endereço", "permission_denied": "Não receberá notificação da alteração de estado do pedido devido a permissão ter sido negada", "map_deliveryman_warning": "Mantenha o mapa aberto enquanto realiza a entrega.", "tracking": "<PERSON><PERSON><PERSON><PERSON>", "continue_tracking": "<PERSON><PERSON><PERSON><PERSON> entrega", "order_delivered_map": "Ver entrega no mapa", "background_location_description": "Coop delivery está utilizando a sua localização em background", "open_map": "Abrir o mapa"}, "text": {"customer_code": "Digite o código informado pelo cliente", "inform_customer_code": "Informe este código ao entregador quando o pedido chegar", "cancel_delivery": "Tem certeza que deseja cancelar a entrega do pedido?", "accept_permission": "Para aceitar um pedido a localização em background deve ser ativada. Selecione a opção permitir o tempo todo", "deliverymanLocation": "Perfil de entregador precisa da localização em background ativada. Selecione a opção permitir o tempo todo"}, "close_map": {"title": "Sair do mapa?", "text": "Tem certeza que deseja sair? O rastreamento da entrega atual será pausado. <PERSON><PERSON> poderá acarretar prejuízos ao entregador", "button": {"positive": "<PERSON><PERSON><PERSON> mapa", "negative": "<PERSON><PERSON><PERSON>"}}, "toasts": {"create_success": "Pedido realizado com sucesso!", "create_error": "Não foi possível criar o pedido!", "invalid_address": "Pedido não poderá ser realizado. Endereço está fora do raio de entrega da loja!", "cancel_success": "Pedido cancelado com sucesso!", "order_delivered": "Pedido concluído com sucesso!", "added_cart": "Item adicionado ao carrinho!", "error_load_order": "Erro ao carregar pedido!", "payment_success": "Pagamento realizado com sucesso!"}, "button": {"addOrder": "<PERSON><PERSON><PERSON><PERSON>", "free": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "place_order": "Finalizar pedido", "retry": "Reprocessar Pagamento", "updateOrder": "<PERSON><PERSON><PERSON><PERSON>", "change": "Alterar", "select": "Selecionar", "finish_delivery": "Finalizar a entrega", "cancel_delivery": "Cancelar a entrega do pedido", "cancel": "<PERSON><PERSON><PERSON>", "go_back": "Voltar", "close": "<PERSON><PERSON><PERSON>", "accept": "Aceitar"}, "errors": {"map_failed": "Erro ao abrir o mapa", "load_order": "Pedidos não encontrados", "load_failed": "Erro ao carregar", "order_notFound": "Pedido não encontrado", "order_already_canceled": "O pedido já foi cancelado", "order_notCanceled": "O pedido não pode ser cancelado", "payment_notFound": "Pagamento não encontrado", "payment_notCanceled": "Pagamento não foi cancelado. Por favor contate o suporte.", "order_not_updated": "Erro ao gerar código do cliente", "wrong_custom_code": "Código inválido. Confirme o código com o cliente e tente novamente", "permission_denied": "Permissão de localização negada. Acesse as configurações e ative a localização o tempo todo", "not_allowed": "Não permitido", "status_not_found": "Status não encontrado", "deliveryman_not_found": "Entregador não encontrado.", "order_already_taken": "Pedido já aceito por outro entregador, por favor selecione outro."}, "cart": {"label": {"totalOrder": "Total com entrega", "alreadyCartItems": "<PERSON>á itens de outra loja no seu carrinho.", "eraseCart": "<PERSON>eja limpar seu carrinho?", "tapDelete": "TOQUE PARA EXCLUIR", "deliveryFee": "Taxa de entrega", "orderPrice": "Pedido (subtotal)", "deleteAll": "Excluir tudo", "addMoreItems": "<PERSON><PERSON><PERSON>r mais itens", "addItems": "<PERSON><PERSON><PERSON><PERSON> itens", "observation": "Obs.:", "emptyCart": "O carrinho está vazio"}, "button": {"continue": "<PERSON><PERSON><PERSON><PERSON>"}}, "select": {"address": "Endereço de entrega"}, "orderPaymentMethods": {"paymentMethods": {"credit": "Cartão de Crédito", "debt": "Cartão de Débito", "pix": "Pix", "accordion": {"credit": "<PERSON><PERSON><PERSON><PERSON>", "debt": "Débito", "pix": "Pix"}}, "button": {"addCard": "Novo Cartão", "confirm": "Confirmar"}, "label": {"expires_in": "Expira em: ", "expired_on": "<PERSON><PERSON><PERSON> em: ", "newCard": "Novo Cartão", "cardData": "Dados do Cartão"}}, "modalPix": {"label": {"title": "<PERSON><PERSON>"}, "button": {"copy": "Copiar", "close": "<PERSON><PERSON><PERSON>", "uploadVoucher": "Enviar comprovante"}, "toasts": {"copied": "Copiado", "time_limit": "Tempo limite expirado"}}, "modalCancel": {"label": {"title": "<PERSON><PERSON>ar <PERSON>", "text": "Tem certeza que deseja cancelar o pedido?"}, "button": {"no": "Não", "yes": "<PERSON>m"}}, "modalVoucher": {"label": {"title": "Comprovante de pagamento"}}}, "productContainer": {"button": {"yes": "<PERSON>m", "no": "Não"}}, "model_validation": {"user": {"email_invalid": "<PERSON>ail <PERSON>.", "phone_invalid": "Telefone inválido."}, "file": {"corrupted_file": "Arquivo corrompido."}, "product": {"name_very_long": "Nome do produto muito longo.", "short_description_very_long": "Breve descrição do produto muito longa.", "description_very_long": "Descrição do produto muito longa.", "invalid_value": "Valor do produto inválido."}, "store": {"name_very_long": "Nome da loja muito longo.", "description_very_long": "Descrição da loja muito longa.", "cnpj_invalid": "CNPJ inválido.", "slug_very_long": "Slug da loja muito longa.", "email_invalid": "<PERSON>ail <PERSON>.", "phone_invalid": "Telefone inválido."}, "store_settings": {"moreThanZero": "<PERSON><PERSON> ser maior que 0."}, "address": {"number_invalid": "Número de endereço inválido.", "postcode_invalid": "Código postal do endereço inválido."}, "order": {"code_invalid": "O código deve ter exatamente 4 números"}}, "review": {"header": {"title": "Avaliações", "rating": "Avaliar {arg}"}, "label": {"order": "pedido", "store": "loja", "deliveryman": "entregador"}, "title": {"order": "O que você achou deste pedido?", "store": "O que você achou desta loja?", "deliveryman": "O que você achou de {arg}?"}, "text": {"reviews": "{arg} avaliações", "review": "{arg} avaliação", "createReviewText": "Avalie de 1 a 5 estrelas", "rate": "Avaliar", "rateDeliveryman": "<PERSON><PERSON><PERSON> entregador", "total_price": "Total", "already_reviewed": "Esse entregador já foi avaliado."}, "button": {"createReview": "Avaliar {arg}", "submitReview": "Enviar", "replyReview": "Responder avaliação", "reply": "<PERSON><PERSON><PERSON><PERSON>"}, "placeholder": {"comment": "Coment<PERSON>rio (Opcional)"}, "update": {"errors": {"not_found": "Avaliação não encontrada"}}, "toasts": {"create_success": "Avaliação realizada com sucesso!", "create_error": "Erro ao avaliar"}}, "permission": {"label": {"title": "Permissões negadas", "settings": "<PERSON><PERSON>r configuraç<PERSON><PERSON>"}, "text": {"description": "Algumas funcionalidades não estarão disponíveis sem ativar as permissões."}}, "configApp": {"errors": {"loading": "Erro ao carregar configuração"}}, "notifications": {"new_order_title": "Novo pedido #{arg}", "change_status_title": "Seu pedido #{arg} foi atualizado", "status_placedOrder": "Status alterado para \"Pedido realizado\"", "status_paymentMade": "Status alterado para \"Pagamento realizado\"", "status_canceled": "Status alterado para \"Pedido cancelado\"", "status_rejected": "Status alterado para \"Pedido rejeitado\"", "status_preparing": "Status alterado para \"Preparando pedido\"", "status_acceptedByDeliveryPerson": "Status alterado para \"Aceito pelo entregador\"", "status_onDeliveryRoute": "Status alterado para \"Em rota de entrega\"", "status_customerConfirmation": "Status alterado para \"Aguardando confirmação do cliente\"", "status_delivered": "Status alterado para \"Pedido Entregue\"", "payment_failure": "Falha no processo de pagamento. Por favor, verifique seus dados e tente novamente.", "cancellation_failed": "Oops... Ocorreu um erro no processo de cancelamento do pedido.", "label": {"empty": "Nenhuma notificação encontrada", "filter": "Filtrar", "mark_as_read": "Marcar todas como lidas", "date_filter": "Data", "read_messages_filter": "<PERSON><PERSON>", "unread_messages_filter": "Não lidas", "order_by": "Ordenar por", "latest_messages": "<PERSON><PERSON>", "older_messages": "Antigas"}, "toasts": {"mark_as_read_success": "Mensagem marcada como lida", "mark_as_read_error": "Não foi possível marcar a mensagem como lida", "mark_all_as_read_success": "<PERSON><PERSON> as mensagens foram marcadas como lidas", "mark_all_as_read_error": "Não foi possível marcar todas as mensagens como lidas"}}, "calendar": {"localeConfig": {"monthNames": ["Janeiro", "<PERSON><PERSON>", "Março", "Abril", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Agosto", "Setembro", "Out<PERSON>ro", "Novembro", "Dezembro"], "monthNamesShort": ["Jan.", "Fev.", "Mar.", "Abr.", "<PERSON>,", "Jun.", "Jul.", "Ago.", "Set.", "Out.", "Nov.", "Dez."], "dayNames": ["Domingo", "Segunda", "<PERSON><PERSON><PERSON>", "Quarta", "<PERSON><PERSON><PERSON>", "Sexta", "Sábado"], "dayNamesShort": ["Dom.", "Seg.", "<PERSON>r.", "Qua.", "Qui.", "Sex.", "Sáb."], "today": "Hoje"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "save_date": "Filtrar data"}, "chat": {"placeholder": "Digite uma nova mensagem", "labels": {"empty_chat": "Chat encerrado sem mensagens", "no_chat_messages": "Nenhuma mensagem encontrada. Envie uma nova mensagem", "closed_chat": "Chat encerrado. Não é mais possível enviar mensagens.", "chat_users": "Voc<PERSON>, o {arg} e o {arg} podem enviar e receber mensagens", "is_typing": "está digitando...", "order_code": "Pedido #{arg}"}, "errors": {"chat_not_found": "<PERSON>enhum chat encontrado", "chat_messages_not_found": "<PERSON><PERSON><PERSON><PERSON> mensagem nesse chat", "socket_connection": "Falha na conexão. Não foi possível abrir o chat", "create_chat_invalid_params": "Dados inválidos. Não foi possível criar o chat"}}, "sales": {"placeholder": {"order": "Pedido #{arg}", "store": "<PERSON><PERSON>", "price": "Preço do pedido", "shippingPrice": "Taxa de entrega", "totalPrice": "Total", "routeLength": "<PERSON><PERSON><PERSON><PERSON> percorrida", "address": "Endereço"}, "totalizers": {"id": "Pedidos", "price": "Valor dos pedidos", "totalPrice": "Pedidos + Frete", "routeLength": "Distância", "shippingPrice": "Frete", "payoutsAvailable": "Pagamentos disponíveis", "payoutsExecuted": "Pagamentos realizados"}, "labels": {"calendar": "<PERSON><PERSON><PERSON><PERSON>", "dateFilter": "Filtrar por data", "delivered": "Pedidos entregues"}}, "modalReview": {"text": {"title": "Avaliação", "content": "Gostaria de nos ajudar a melhorar? Por favor, avalie nosso aplicativo. Sua opinião é valiosa para nós! "}, "button": {"cancel": "<PERSON><PERSON><PERSON> n<PERSON>.", "review": "Vou avaliar!"}}, "popupMap": {"text": {"title": "Mapas disponíveis", "message": "Abaixo está a lista dos aplicativos de mapas no seu celular"}, "button": {"cancel": "<PERSON><PERSON><PERSON>"}}, "verifyPix": {"click_here": "Clique aqui", "deliverymanText": "para cadastrar sua chave pix", "shopkeeperText": "para verificar as lojas sem chave pix", "storeMissingPixText": "Cadastre a chave pix dessa loja!"}}