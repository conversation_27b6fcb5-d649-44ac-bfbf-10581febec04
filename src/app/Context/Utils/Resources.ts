import React, { MutableRefObject } from "react";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";
import { LanguageContextProps } from "src/business/Interfaces/LanguageContext";

type IResourcesHelper = {
  get: () => LanguageContextProps;
  setLanguage: (option: LanguageOptions) => void;
  getOption: () => LanguageOptions;
};

export const LanguageContextRef = React.createRef<IResourcesHelper>() as MutableRefObject<IResourcesHelper>;

export const Resources: IResourcesHelper = {
  get: () => LanguageContextRef.current?.get(),
  setLanguage: (option: LanguageOptions) => LanguageContextRef.current?.setLanguage(option),
  getOption: () => LanguageContextRef.current?.getOption(),
};
