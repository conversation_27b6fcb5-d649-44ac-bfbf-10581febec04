import React, { MutableRefObject } from "react";
import { ThemeContextProps, ThemeNavContextProps } from "src/business/Interfaces/ThemeContext";

type IThemesAppHelper = {
  getTheme: () => ThemeContextProps;
  getThemeNav: () => ThemeNavContextProps;
  setThemeLight: () => void;
  setThemeDark: () => void;
  setThemeNavLight: () => void;
  setThemeNavDark: () => void;
};

export const ThemeAppContextRef = React.createRef<IThemesAppHelper>() as MutableRefObject<IThemesAppHelper>;

export const ThemesApp: IThemesAppHelper = {
  getTheme: () => ThemeAppContextRef.current?.getTheme(),
  getThemeNav: () => ThemeAppContextRef.current?.getThemeNav(),
  setThemeLight: () => ThemeAppContextRef.current?.setThemeLight(),
  setThemeDark: () => ThemeAppContextRef.current?.setThemeDark(),
  setThemeNavLight: () => ThemeAppContextRef.current?.setThemeNavLight(),
  setThemeNavDark: () => ThemeAppContextRef.current?.setThemeNavDark(),
};
