import * as RNLocalize from "react-native-localize";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";
import { IUserService } from "src/business/Interfaces/Services/IUser";
import AppError from "src/business/Tools/AppError";

const getInitialLanguage = async () => {
  const userService = container.get<IUserService>(TOKENS.UserService);
  const languageDB = await userService.getDeviceLanguage();
  if (languageDB instanceof AppError) {
    const { languageCode } = RNLocalize.getLocales()[0];
    const newLanguage =
      languageCode === "en" ? LanguageOptions.EN : LanguageOptions.PT;

    userService.updateDeviceLanguage(newLanguage);
    return newLanguage;
  } else {
    return languageDB;
  }
};

export default getInitialLanguage;
