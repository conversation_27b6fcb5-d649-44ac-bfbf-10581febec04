import React, {MutableRefObject} from "react";
import {ComponentsController} from "src/business/Interfaces/ComponentsController";

interface ComponentsHelper {
  showFormErrors: ComponentsController["showFormErrors"];
  showLoading: ComponentsController["showLoading"];
  askForProfile: ComponentsController["askForProfile"];
  askForAppReview: ComponentsController["askForAppReview"];
  openDrawer: ComponentsController["openDrawer"];
  closeDrawer: ComponentsController["closeDrawer"];
}

export const ComponentsContextRef =
  React.createRef<ComponentsHelper>() as MutableRefObject<ComponentsHelper>;

export const Components: ComponentsHelper = {
  showFormErrors: error => ComponentsContextRef.current?.showFormErrors(error),
  showLoading: () => ComponentsContextRef.current?.showLoading(),
  askForProfile: () => ComponentsContextRef.current?.askForProfile(),
  askForAppReview: () => ComponentsContextRef.current?.askForAppReview(),
  openDrawer: () => ComponentsContextRef.current?.openDrawer(),
  closeDrawer: () => ComponentsContextRef.current?.closeDrawer(),
};
