import {View} from "native-base";
import React, {createContext, useCallback, useMemo, useState} from "react";
import {StyleSheet} from "react-native";
import CustomDrawer from "src/app/Components/CustomDrawer";
import DisplayError from "src/app/Components/DisplayError";
import ModalProgress from "src/app/Components/ModalProgress";
import PermissionDeniedModal from "src/app/Components/Permissions/Denied";
import SelectProfileModal from "src/app/Components/SelectProfileModal";
import {ComponentsContextRef} from "src/app/Context/Utils/Components";
import saveProfileOption from "src/app/Utils/SaveProfileOption";
import {ComponentsController} from "src/business/Interfaces/ComponentsController";
import {EProfile} from "src/business/Models/Profile";
import AppError from "src/business/Tools/AppError";
import {AppReviewModal} from "../Components/AppReviewModal";
import navigateHome from "../Utils/NavigateHome";

export const ComponentsContext = createContext<ComponentsController>({
  showLoading: () => {
    return {remove: () => {}};
  },
  showFormErrors: () => {
    return {remove: () => {}};
  },
  askForProfile: () => {
    return {remove: () => {}};
  },
  askForAppReview: () => {
    return {remove: () => {}};
  },
  openDrawer: () => {},
  closeDrawer: () => {},
});

export const ComponentsProvider = ({children}: any) => {
  const [isLoading, setIsLoading] = useState(false);
  const [askForProfile, setAskForProfile] = useState(false);
  const [formErrors, setFormErrors] = useState<AppError | undefined>();
  const [askForAppReview, setAskForAppReview] = useState(false);
  const [showDrawer, setShowDrawer] = useState(false);
  const componentsController = useMemo<ComponentsController>(() => {
    return {
      showLoading: () => {
        setIsLoading(true);
        return {remove: () => setIsLoading(false)};
      },
      showFormErrors: formError => {
        setFormErrors(formError);
        return {
          remove: () => setFormErrors(undefined),
        };
      },
      askForProfile: () => {
        setAskForProfile(true);
        return {remove: () => setAskForProfile(false)};
      },
      askForAppReview: () => {
        setAskForAppReview(true);
        return {remove: () => setAskForAppReview(false)};
      },
      openDrawer: () => setShowDrawer(true),
      closeDrawer: () => setShowDrawer(false),
    };
  }, []);

  const handleClose = useCallback(() => {
    setFormErrors(undefined);
  }, []);

  const handleProfileChoose = useCallback(
    (profileType: EProfile) => {
      setAskForProfile(false);
      saveProfileOption(profileType);
      navigateHome();
      if (showDrawer) {
        setShowDrawer(false);
      }
    },
    [showDrawer],
  );

  const handleProfileClose = useCallback(() => {
    setAskForProfile(false);
  }, []);

  ComponentsContextRef.current = {
    showFormErrors: componentsController.showFormErrors,
    showLoading: componentsController.showLoading,
    askForProfile: componentsController.askForProfile,
    askForAppReview: componentsController.askForAppReview,
    openDrawer: componentsController.openDrawer,
    closeDrawer: componentsController.closeDrawer,
  };

  return (
    <ComponentsContext.Provider value={componentsController}>
      {isLoading ? <ModalProgress /> : null}

      <SelectProfileModal
        visibility={askForProfile}
        onProfileSelect={handleProfileChoose}
        onClose={handleProfileClose}
      />

      <AppReviewModal
        visibility={askForAppReview}
        onClose={() => setAskForAppReview(!askForAppReview)}
      />

      <PermissionDeniedModal />

      <View style={styles.childrenContainer}>
        {showDrawer && <CustomDrawer />}

        {children}
      </View>

      <DisplayError appError={formErrors} onClose={handleClose} />
    </ComponentsContext.Provider>
  );
};

const styles = StyleSheet.create({
  childrenContainer: {
    position: "relative",
    height: "100%",
    flexDirection: "row",
  },
});
