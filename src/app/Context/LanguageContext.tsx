import _ from "lodash";
import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import Config from "react-native-config";
import {LanguageContextRef} from "src/app/Context/Utils/Resources";
import configureCalendarLocale from "src/app/Utils/ConfigureCalendarLocale";
import getKeysDeep from "src/app/Utils/GetKeysDeep";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import en from "src/assets/Lang/en.json";
import pt from "src/assets/Lang/pt.json";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";
import {LanguageContextProps} from "src/business/Interfaces/LanguageContext";
import {ILocalStorage} from "src/business/Interfaces/Services/IStorageHelper";

export const LanguageContext = React.createContext<{
  resources: LanguageContextProps;
  updateLanguage: (language: LanguageOptions) => void;
  language: LanguageOptions;
}>({
  resources: pt,
  updateLanguage: () => {},
  language: LanguageOptions.PT,
});

type Props = {
  children: JSX.Element;
};

export const LanguageProvider: React.FC<Props> = ({children}) => {
  const localStorageService = container.get<ILocalStorage>(TOKENS.LocalStorage);
  const [resources, setResources] = useState<LanguageContextProps>(pt);
  const languageRef = useRef<LanguageOptions>(LanguageOptions.PT);
  const {setLanguageApp} = useGeneralSettings();

  const handleUpdateResources = useCallback(
    (language: LanguageOptions) => {
      languageRef.current = language;
      language === LanguageOptions.EN ? setResources(en) : setResources(pt);

      setLanguageApp(language);
      configureCalendarLocale(language);
      localStorageService.setValue(Config.LANGUAGE_OPTION!, language);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  useEffect(() => {
    // TODO Check for strings inconsistency only in development environment
    const enKeys = getKeysDeep(en);
    const ptKeys = getKeysDeep(pt);

    if (!_.isEqual(enKeys, ptKeys)) {
      console.warn(
        "i18n strings files are not equal. Please check your changes.",
      );
    }

    configureCalendarLocale(languageRef.current);

    (async () => {
      const option = (await localStorageService.getValue(
        Config.LANGUAGE_OPTION!,
      )) as unknown as LanguageOptions | null;
      if (option && option !== languageRef.current) {
        handleUpdateResources(option);
      }
    })();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  LanguageContextRef.current = {
    get: () => resources,
    setLanguage: (option: LanguageOptions) => handleUpdateResources(option),
    getOption: () => languageRef.current,
  };

  const value = useMemo(
    () => ({
      resources,
      updateLanguage: handleUpdateResources,
      language: languageRef.current,
    }),
    [resources, handleUpdateResources],
  );

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
