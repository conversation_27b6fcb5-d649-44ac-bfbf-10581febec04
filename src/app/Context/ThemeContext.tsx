import { DarkTheme, DefaultTheme } from "@react-navigation/native";
import React, { useEffect, useMemo, useRef, useState } from "react";
import getInitialTheme from "src/app/Context/Utils/GetInitialTheme";
import { ThemeAppContextRef } from "src/app/Context/Utils/ThemesApp";
import themeDark from "src/assets/Themes/DarkNB";
import theme from "src/assets/Themes/LightNB";
import EThemeApp from "src/business/Enums/Models/EThemeApp";
import { ThemeContextProps, ThemeNavContextProps } from "src/business/Interfaces/ThemeContext";

type SetThemeContextProps = React.Dispatch<React.SetStateAction<ThemeContextProps>>;
type SetThemeNavContextProps = React.Dispatch<React.SetStateAction<ThemeNavContextProps>>;

export const ThemeContext = React.createContext<{
  themeNav: ThemeNavContextProps;
  setThemeNav: SetThemeNavContextProps;
  themeApp: ThemeContextProps;
  setTheme: SetThemeContextProps;
  setThemeLight: SetThemeContextProps;
  setThemeDark: SetThemeContextProps;
  setThemeNavLight: SetThemeContextProps;
  setThemeNavDark: SetThemeContextProps;
}>({
  themeNav: DefaultTheme,
  setThemeNav: () => {},
  themeApp: theme,
  setTheme: () => {},
  setThemeLight: () => {},
  setThemeDark: () => {},
  setThemeNavLight: () => {},
  setThemeNavDark: () => {},
});

type Props = {
  children: JSX.Element;
};

export const ThemeProvider: React.FC<Props> = ({ children }) => {
  const [themeApp, setThemeApp] = useState<ThemeContextProps>(theme);
  const [themeNavApp, setThemeNavApp] = useState<ThemeNavContextProps>(DefaultTheme);
  const themeRef = useRef<EThemeApp>(EThemeApp.light);

  useEffect(() => {
    (async () => {
      const initialThemeApp = await getInitialTheme();
      themeRef.current = initialThemeApp as unknown as EThemeApp;

      initialThemeApp === EThemeApp.dark ? setThemeApp(themeDark) : setThemeApp(theme);
      initialThemeApp === EThemeApp.dark ? setThemeNavApp(DarkTheme) : setThemeNavApp(DefaultTheme);
    })();
  }, []);

  const setThemeLight = () => {
    setThemeApp(theme);
  };

  const setThemeDark = () => {
    setThemeApp(themeDark);
  };

  const setThemeNavLight = () => {
    setThemeNavApp(DefaultTheme);
  };

  const setThemeNavDark = () => {
    setThemeNavApp(DarkTheme);
  };

  ThemeAppContextRef.current = {
    getTheme: () => themeApp,
    getThemeNav: () => themeNavApp,
    setThemeLight: () => setThemeLight(),
    setThemeDark: () => setThemeDark(),
    setThemeNavLight: () => setThemeNavLight(),
    setThemeNavDark: () => setThemeNavDark(),
  };

  const contextValue = useMemo(
    () => ({
      themeNav: themeNavApp,
      setThemeNav: setThemeNavApp,
      themeApp,
      setTheme: setThemeApp,
      setThemeLight,
      setThemeDark,
      setThemeNavLight,
      setThemeNavDark,
    }),
    [themeApp, themeNavApp],
  );

  return <ThemeContext.Provider value={contextValue}>{children}</ThemeContext.Provider>;
};
