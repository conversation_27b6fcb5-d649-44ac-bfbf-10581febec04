import {
  NavigationContainer,
  NavigationContainerRef,
  NavigationContainerRefContext,
  useNavigationContainerRef,
} from "@react-navigation/native";
import React, { useMemo } from "react";
import RootNavigationContextRef from "src/app/Context/Utils/Navigation";
import { ThemeNavContextProps } from "src/business/Interfaces/ThemeContext";

export type NavigationContextProps = React.RefObject<NavigationContainerRef<ReactNavigation.RootParamList>>;

type Props = {
  children: JSX.Element;
  themeNav: ThemeNavContextProps;
};

export const NavigationProvider: React.FC<Props> = ({ themeNav, children }) => {
  const navigationRef = useNavigationContainerRef();

  RootNavigationContextRef.current = {
    navigationRef,
  };

  const contextValue = useMemo(() => navigationRef, [navigationRef]);

  return (
    <NavigationContainerRefContext.Provider value={contextValue}>
      <NavigationContainer ref={navigationRef} theme={themeNav}>
        {children}
      </NavigationContainer>
    </NavigationContainerRefContext.Provider>
  );
};
