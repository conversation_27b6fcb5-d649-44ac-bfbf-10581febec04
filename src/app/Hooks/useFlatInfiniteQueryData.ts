import {InfiniteData, UseInfiniteQueryResult} from "@tanstack/react-query";
import {useMemo} from "react";
import {IPagedResult} from "src/business/DTOs/PagedResult";

const useFlatInfiniteQueryData = <T>(
  queryObject: UseInfiniteQueryResult<
    InfiniteData<IPagedResult<T> | undefined, unknown>,
    Error
  >,
) => {
  const dataArray = useMemo(() => {
    return queryObject.data?.pages
      .map((page: any) => (page ? page.result : []))
      .flat();
  }, [queryObject.data]);

  return dataArray as T[] | undefined;
};

export default useFlatInfiniteQueryData;
