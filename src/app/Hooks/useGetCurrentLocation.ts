import {useEffect} from "react";
import Config from "react-native-config";
import GeoCoder from "react-native-geocoding";
import Geolocation from "react-native-geolocation-service";
import getFormattedAddress from "src/app/Utils/GetFormattedAddress";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import {Location} from "src/business/DTOs/Location";
import {Address} from "src/business/Models/Address/Address";

GeoCoder.init(Config.ANDROID_MAPS_KEY!);

export interface IUserPosition {
  currentLocation?: Location;
  currentAddress?: Address;
}

/**
 *  This method is used to get the current location of the user.
 *
 * @param trigger A boolean that will control if the hook will fetch the current location or not, it
 * will work as a trigger for the hook, so it must be a state variable.
 * @param onLoadLocation A callback function that will be called when the location is fetched
 *
 * @returns void
 */

export const useGetCurrentLocation = (
  trigger?: boolean,
  onLoadLocation?: (position: IUserPosition) => Promise<void>,
) => {
  const {languageApp} = useGeneralSettings();

  useEffect(() => {
    const fetchCurrentLocation = () => {
      const response: IUserPosition = {
        currentAddress: undefined,
        currentLocation: undefined,
      };

      Geolocation.getCurrentPosition(
        info => {
          response.currentLocation = info.coords;

          GeoCoder.from(info.coords.latitude, info.coords.longitude)
            .then(geocoderResponse => {
              const address = geocoderResponse.results[0];
              if (address) {
                response.currentAddress = getFormattedAddress(
                  address,
                  languageApp,
                );
              } else {
                console.log("No address found");
              }
            })
            .catch(error => {
              console.log(error.message);
            })
            .finally(() => {
              if (onLoadLocation) {
                onLoadLocation(response);
              }
            });
        },
        error => {
          console.log("GetCurrentLocation error: ", error);
        },
        {
          enableHighAccuracy: true,
          timeout: 2000,
        },
      );
    };

    if (trigger) {
      fetchCurrentLocation();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trigger]);
};
