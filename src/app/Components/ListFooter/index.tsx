import React from "react";
import {Box, Spinner} from "native-base";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

interface Props {
  isVisible: boolean;
}

const ListFooter = ({isVisible}: Props) => {
  return isVisible ? (
    <Box flex={1} alignItems="center" justifyContent="center">
      <Spinner size="sm" my={2} color={ThemesApp.getTheme().colors.spinner} />
    </Box>
  ) : null;
};

export default ListFooter;
