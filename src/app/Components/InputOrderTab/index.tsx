import React from "react";
import { FieldValues, UseFormSetFocus } from "react-hook-form";

interface IComponentProps {
  children: any;
  setFocus: UseFormSetFocus<any & FieldValues>;
}

const InputOrderTab = ({ children, setFocus }: IComponentProps) => {
  const inputsNext: any[] = [];

  // Essa função seleciona os inputs e textAreas que devem ser acionados quando
  // apertar o TabOrder do teclado
  const getInputs: any = (childrenInputs: any, inputs: any[]) => {
    React.Children.map(childrenInputs, (child) => {
      // Desconsidera se não for um componente
      if (!child) return;

      // Se o componente estiver dentro de uma estrutura
      // as children devem ser acessadas para identificar possíveis inputs e textAreas
      if (child.props?.children) {
        return getInputs(child.props.children, inputs);
      }

      // Se for input ou textArea, devem ser considerados
      if (
        child.props?.numberOfLines !== undefined ||
        child.props?.type === "text" ||
        child.props?.type === "password"
      ) {
        inputs.push(child.props.name);
      }
    });

    return inputs;
  };

  const renderChildren: any = (childrenRender: any) => {
    if (inputsNext.length === 0) {
      getInputs(childrenRender, inputsNext);
    }

    const result = React.Children.map(childrenRender, (child, index) => {
      if (!child) return;

      if (child.props?.children) {
        return React.cloneElement(child, {
          ...child.props,
          children: renderChildren(child.props?.children, index),
        });
      }

      if (
        child.props?.numberOfLines === undefined &&
        child.props?.type !== "text" &&
        child.props?.type !== "password"
      ) {
        return child;
      }

      const realIndex = inputsNext.findIndex(
        (item) => item === child.props.name
      );

      return React.cloneElement(child, {
        returnKeyType: inputsNext[realIndex + 1] ? "next" : "done",
        blurOnSubmit:
          child.props?.blurOnSubmit !== undefined
            ? child.props.blurOnSubmit
            : !inputsNext[realIndex + 1],
        onSubmitEditing: child.props?.onSubmitEditing
          ? child.props.onSubmitEditing
          : () => {
              realIndex >= 0 && inputsNext[realIndex + 1]
                ? setFocus(inputsNext[realIndex + 1])
                : null;
            },
      });
    });
    return result;
  };

  return renderChildren(children);
};
export default InputOrderTab;
