import {Box, HStack, Text, Pressable} from "native-base";
import React, {useEffect, useState} from "react";
import customStyles from "src/app/Components/Forms/MultiStepForm/Components/MultiStepFooter/styles";
import MultiStepSkip from "src/app/Components/Forms/MultiStepForm/Components/MultiStepSkip";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import useTranslation from "src/app/Hooks/useTranslation";
import {ChevronLeft, ChevronRight} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import MultiStepFormProgress from "src/business/Singletons/MultiStepFormProgress";

type Props = {
  length: number;
  onStepChange: (step: number) => void;
  handleGoBackButton: () => void;
  handleNextButton: () => Promise<boolean>;
  handleSkipStep: () => void;
  optionalSteps: boolean[];
};

const MultiStepFooter: React.FC<Props> = ({
  optionalSteps,
  onStepChange,
  handleNextButton,
  handleGoBackButton,
  length,
  handleSkipStep,
}) => {
  const styles = customStyles();
  const updatedStep = container.get<MultiStepFormProgress>(
    TOKENS.MultiStepFormProgress,
  );
  const [currentStep, setCurrentStep] = useState(updatedStep.getCurrentStep);
  const {multiStepForm: resources} = useTranslation();

  useEffect(() => {
    console.log(
      "effect multi step form",
      updatedStep.getCurrentStep,
      currentStep,
    );
    setCurrentStep(updatedStep.getCurrentStep);
  }, [currentStep, updatedStep.getCurrentStep]);

  const handleGoBack = () => {
    updatedStep.setCurrentStep = currentStep - 1;
    setCurrentStep(currentStep - 1);
    onStepChange(currentStep - 1);
    handleGoBackButton();
  };

  const handleNext = async () => {
    const isFormValid = await handleNextButton();
    if (isFormValid) {
      updatedStep.setCurrentStep = currentStep + 1;
      setCurrentStep(currentStep + 1);
      onStepChange(currentStep + 1);
    }
  };

  const handleSkip = () => {
    handleSkipStep();
    updatedStep.setCurrentStep = currentStep + 1;
    setCurrentStep(currentStep + 1);
    onStepChange(currentStep + 1);
  };

  return (
    <>
      {optionalSteps[currentStep - 1] && (
        <MultiStepSkip handleSkipStep={handleSkip} />
      )}
      <HStack {...styles.progressBox}>
        {}
        {Array(length)
          .fill(true)
          .map((_, i) => (
            <Box
              {...styles.progressStepBar}
              bgColor={
                currentStep > i
                  ? ThemesApp.getTheme().colors.spinner
                  : ThemesApp.getTheme().colors.disabled
              }
              // eslint-disable-next-line react/no-array-index-key
              key={i}
            />
          ))}
      </HStack>

      <HStack
        {...styles.hstackButtons}
        justifyContent={currentStep > 1 ? "space-between" : "center"}>
        {currentStep > 1 && (
          <Pressable onPress={handleGoBack} _pressed={{opacity: 0.5}}>
            <HStack {...styles.hstackPressable}>
              <ChevronLeft {...styles.iconPreviousBtn.style} />
              <Text {...styles.previousTxt}>{resources.go_back}</Text>
            </HStack>
          </Pressable>
        )}

        <Pressable
          {...styles.pressables}
          onPress={handleNext}
          _pressed={{opacity: 0.5}}>
          <HStack {...styles.hstackPressable}>
            <Text {...styles.nextTxt}>
              {currentStep >= length ? resources.submit : resources.next}
            </Text>
            {currentStep < length && (
              <ChevronRight {...styles.iconNextBtn.style} />
            )}
          </HStack>
        </Pressable>
      </HStack>
    </>
  );
};
export default MultiStepFooter;
