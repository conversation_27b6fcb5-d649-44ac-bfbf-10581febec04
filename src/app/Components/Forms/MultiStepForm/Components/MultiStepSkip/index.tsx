import React from "react";
import {Box, HStack, Text, Pressable} from "native-base";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Components/Forms/MultiStepForm/Components/MultiStepSkip/styles";
import {Skip} from "src/assets/Icons/Flaticon";

type Props = {
  handleSkipStep: () => void;
};

const MultiStepSkip: React.FC<Props> = ({handleSkipStep}) => {
  const styles = customStyles();
  const {multiStepForm: resources} = useTranslation();

  return (
    <Box {...styles.skipStepBox}>
      <Pressable onPress={handleSkipStep} _pressed={{opacity: 0.5}}>
        <HStack {...styles.skipStepHstack}>
          <Text {...styles.skipTxt}>{resources.skip_step}</Text>
          <Skip {...styles.iconSkipBtn.style} />
        </HStack>
      </Pressable>
    </Box>
  );
};

export default MultiStepSkip;
