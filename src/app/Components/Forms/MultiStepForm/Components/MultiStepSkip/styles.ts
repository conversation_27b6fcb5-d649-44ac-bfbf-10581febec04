import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { RFValue } from "react-native-responsive-fontsize";
import { moderateScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    keyboardAvoidingView: {
      style: {
        flex: 1,
        width: wp("93%"),
        padding: hp("1%"),
      },
    },
    flatlist: {
      style: {
        marginVertical: "1%",
        marginHorizontal: "4%",
      },
    },
    progressBox: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        marginVertical: RFValue(20),
        width: "90%",
      },
    },

    hstackButtons: {
      style: {
        width: "90%",
        height: "7%",
        alignItems: "center",
        marginBottom: "3%",
      },
    },
    pressables: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        padding: moderateScale(8),
        backgroundColor: ThemesApp.getTheme().colors.arrowIconButtom,
        borderRadius: moderateScale(5),
        width: "34%",
        height: "80%",
      },
    },
    hstackPressable: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },
    hstackNextBtn: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },
    iconPreviousBtn: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: "white",
      },
    },
    iconNextBtn: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: "white",
      },
    },
    skipStepBox: {
      style: {
        width: "90%",
        marginTop: "4%",
        marginBottom: "2%",
        marginRight: "2%",
        alignItems: "flex-end",
        justifyContent: "flex-end",
      },
    },
    iconSkipBtn: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        marginLeft: moderateScale(3),
      },
    },
    skipTxt: {
      style: {
        color: ThemesApp.getTheme().colors.arrowIconButtom,
        fontWeight: "bold",
      },
    },
    progressStepBar: {
      style: {
        height: moderateScale(6),
        width: moderateScale(32),
        borderRadius: 2,
        marginHorizontal: "2%",
      },
    },
  };
};

export default customStyles;
