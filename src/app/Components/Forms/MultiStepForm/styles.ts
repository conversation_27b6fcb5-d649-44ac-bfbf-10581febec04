import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    keyboardAvoidingView: {
      style: {
        width: wp("100%"),
      },
    },
    flatlist: {
      style: {
        marginVertical: "1%",
      },
    },
    progressBox: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        marginVertical: RFValue(20),
        width: "90%",
      },
    },

    hstackButtons: {
      style: {
        width: "90%",
        height: "7%",
        alignItems: "center",
        marginBottom: "3%",
      },
    },
    pressables: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        padding: moderateScale(8),
        backgroundColor: ThemesApp.getTheme().colors.arrowIconButtom,
        borderRadius: moderateScale(5),
        width: "34%",
        height: "80%",
      },
    },
    hstackPressable: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },
    hstackNextBtn: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },
    iconPreviousBtn: {
      size: moderateScale(30),
    },
    iconNextBtn: {
      size: moderateScale(30),
      color: "primary.400",
    },
    skipStepBox: {
      style: {
        width: "90%",
        marginTop: "4%",
        marginBottom: "2%",
        marginRight: "2%",
        alignItems: "flex-end",
        justifyContent: "flex-end",
      },
    },
    iconSkipBtn: {
      size: moderateScale(22),
      marginLeft: moderateScale(3),
    },

    progressStepBar: {
      style: {
        height: moderateScale(8),
        width: moderateScale(32),
        borderRadius: 100,
        marginHorizontal: "2%",
      },
    },

    formScrollview: {
      style: {
        marginHorizontal: wp("1%"),
        marginTop: hp("1%"),
      },
    },
  };
};

export default customStyles;
