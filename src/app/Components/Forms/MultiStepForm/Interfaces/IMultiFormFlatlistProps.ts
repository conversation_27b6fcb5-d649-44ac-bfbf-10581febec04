import { IFlatListProps } from "native-base/lib/typescript/components/basic/FlatList";
import { FileData } from "src/business/Models/File";
import { IMultiFormProps } from "src/app/Components/Forms/MultiStepForm/Interfaces/IMultiFormProps";

export interface IMultiFormFlatListProps extends Omit<IFlatListProps<IMultiFormProps>, "renderItem"> {
  data: IMultiFormProps[];
  formatFormData: (formData: any, files?: FileData[]) => any | undefined
}
