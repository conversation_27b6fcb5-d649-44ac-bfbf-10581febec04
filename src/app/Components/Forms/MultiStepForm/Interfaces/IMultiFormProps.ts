import { FormikProps } from "formik";
import { FilteredCategory } from "src/business/DTOs/FilteredCategory";
import { IFormBehavior } from "src/business/Interfaces/Components/IFormBehavior";
import { LanguageModulesScreensProps } from "src/business/Interfaces/LanguageModulesScreensProps";

export interface IMultiFormProps {
  component: React.ForwardRefExoticComponent<any & React.RefAttributes<any>>
  origin: Partial<LanguageModulesScreensProps>;
  ref?: React.RefObject<FormikProps<any> | undefined>
  behavior: IFormBehavior;
  select2LoadOptions?: (currentPage: number, pageSize: number, inputValue: string) => Promise<{
    result: FilteredCategory[];
    totalPages: number;
  }>
  initialData?: any;
  initialValues?: { id: string; data?: any };
  optionalStep: boolean;
  componentProps?: any;
}
