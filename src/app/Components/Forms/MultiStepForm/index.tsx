import {Box, FlatList as FlatListN<PERSON>, ScrollView} from "native-base";
import React, {memo, useRef} from "react";
import {FlatList, KeyboardAvoidingView} from "react-native";
import MultiStepFooter from "src/app/Components/Forms/MultiStepForm/Components/MultiStepFooter";
import {IMultiFormFlatListProps} from "src/app/Components/Forms/MultiStepForm/Interfaces/IMultiFormFlatlistProps";
import {IMultiFormProps} from "src/app/Components/Forms/MultiStepForm/Interfaces/IMultiFormProps";
import customStyles from "src/app/Components/Forms/MultiStepForm/styles";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {FileData} from "src/business/Models/File";
import MultiStepFormProgress from "src/business/Singletons/MultiStepFormProgress";

let isFormValid = false;

const MultiStepForm = memo(
  ({data, formatFormData}: IMultiFormFlatListProps) => {
    const flatlistRef = useRef<FlatList>(null);

    const styles = customStyles();

    const updatedStep = container.get<MultiStepFormProgress>(
      TOKENS.MultiStepFormProgress,
    );

    let currentStep = updatedStep.getCurrentStep;
    const finalData: any[] = [];

    const createFormdataObject = (formdata: any, files?: FileData[]) => {
      if (finalData.length <= 0) finalData.push(formdata);
      if (finalData.length > 0) finalData.splice(currentStep - 1, 1, formdata);
      if (currentStep >= data.length) {
        finalData.push(files);
        handleSubmitForm(files);
      }
    };

    const handleSubmitForm = async (files?: FileData[]) => {
      formatFormData(finalData, files);
      flatlistRef?.current?.scrollToIndex({index: 0});
    };

    const renderItem = ({item}: {item: IMultiFormProps}) => {
      const {component: Component, ...rest} = item;
      return (
        <KeyboardAvoidingView {...styles.keyboardAvoidingView}>
          <ScrollView persistentScrollbar {...styles.formScrollview}>
            <Component
              {...rest}
              formatFormData={createFormdataObject}
              onFormValidated={onFormValidated}
              initialValues={{data: finalData[currentStep - 1]}}
            />
          </ScrollView>
        </KeyboardAvoidingView>
      );
    };

    const onFormValidated = () => {
      isFormValid = true;
    };

    const handleNextStep = async () => {
      await flatlistRef.current?.props?.data?.[
        currentStep - 1
      ].ref.current.submitForm();
      if (flatlistRef.current && isFormValid && currentStep < data.length) {
        flatlistRef?.current?.scrollToIndex({index: currentStep});
        isFormValid = false;
        return true;
      }
      return false;
    };

    const handleGoBack = () => {
      flatlistRef.current?.scrollToIndex({index: currentStep - 1});
    };

    const handleSkipStep = () => {
      createFormdataObject(null);
      if (flatlistRef.current && currentStep < data.length) {
        flatlistRef?.current?.scrollToIndex({index: currentStep});
        isFormValid = false;
      }
    };

    const onStepChange = (step: number) => {
      updatedStep.setCurrentStep = step;
      currentStep = step;
    };

    return (
      <Box flex={1} alignItems="center">
        <FlatListNB
          {...styles.flatlist}
          data={data}
          ref={flatlistRef}
          renderItem={renderItem}
          scrollEnabled={false}
          horizontal
          showsHorizontalScrollIndicator={false}
        />

        <MultiStepFooter
          optionalSteps={data.map(item => item.optionalStep)}
          handleSkipStep={handleSkipStep}
          length={data.length}
          onStepChange={onStepChange}
          handleGoBackButton={handleGoBack}
          handleNextButton={handleNextStep}
        />
      </Box>
    );
  },
);

export default MultiStepForm;
