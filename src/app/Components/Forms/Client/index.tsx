import { View } from "native-base";
import React from "react";
import ProgressProfileStatus from "src/app/Components/ProgressPofileStatus";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";
import { EProfile } from "src/business/Models/Profile";

interface Props {
  currentStatus?: EProfileStatus;
}

const ClientForm = ({ currentStatus }: Props) => {
  return (
    <View>
      {currentStatus !== undefined ? (
        <ProgressProfileStatus currentStatus={currentStatus} profile={EProfile.client} />
      ) : null}
    </View>
  );
};

export default ClientForm;
