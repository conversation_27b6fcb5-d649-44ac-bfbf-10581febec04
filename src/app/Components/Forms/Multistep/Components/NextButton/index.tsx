import {Button, HStack, Text} from "native-base";
import React from "react";
import styles from "src/app/Components/Forms/Multistep/Components/NextButton/styles";
import {Resources} from "src/app/Context/Utils/Resources";
import {ChevronRight} from "src/assets/Icons/Flaticon";

interface Props {
  onPress: () => void;
  isLastStep: boolean;
  isLoading?: boolean;
}

const NextButton = ({onPress, isLastStep, isLoading}: Props) => {
  const resources = Resources.get().multiStepForm;
  return (
    <Button
      {...styles({}).pressables}
      onPress={onPress}
      isLoading={isLoading}
      _pressed={{opacity: 0.5}}>
      <HStack {...styles({isLastStep}).hstackNextBtn}>
        <Text {...styles({}).txtNext}>
          {isLastStep ? resources.submit : resources.next}
        </Text>
        {!isLastStep ? (
          <ChevronRight {...styles({}).iconNextBtn.style} />
        ) : null}
      </HStack>
    </Button>
  );
};

export default NextButton;
