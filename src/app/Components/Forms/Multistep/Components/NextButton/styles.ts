import {IStyleProps} from "src/business/Interfaces/IStyleProps";
import {moderateScale, verticalScale} from "src/app/Utils/Metrics";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {RFValue} from "react-native-responsive-fontsize";

interface IProps {
  isDisable?: boolean;
  isLastStep?: boolean;
}

const styles = ({isLastStep}: IProps) => {
  const stylesCustom: IStyleProps = {
    pressables: {
      backgroundColor: "lime.500",
      style: {
        alignItems: "center",
        justifyContent: "center",
        padding: moderateScale(8),
        borderRadius: moderateScale(5),
        width: wp("34%"),
        height: verticalScale(45),
      },
    },
    hstackNextBtn: {
      width: "100%",
      style: {
        alignItems: "center",
        justifyContent: isLastStep ? "center" : "space-between",
      },
    },
    iconNextBtn: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: "white",
      },
    },
    txtNext: {
      style: {
        fontSize: RFValue(16),
        lineHeight: RFValue(18),
        color: "white",
      },
    },
  };
  return stylesCustom;
};
export default styles;
