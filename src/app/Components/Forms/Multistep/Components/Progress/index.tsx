import React from "react";
import { Box, HStack } from "native-base";
import customStyles from "src/app/Components/Forms/Multistep/Components/Progress/styles";

interface Props {
  quantitySteps: number;
  formStep: number;
}

const MultiStepProgress = ({ quantitySteps, formStep }: Props) => {
  const styles = customStyles();
  return (
    <HStack {...styles.progressBox}>
      {[...Array(quantitySteps + 1).keys()].map((_, i) => {
        return <Box key={_} {...styles.progressStepBar} bgColor={i <= formStep ? "lime.500" : "disabled"} />;
      })}
    </HStack>
  );
};

export default MultiStepProgress;
