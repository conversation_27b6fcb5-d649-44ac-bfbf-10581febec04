import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { RFValue } from "react-native-responsive-fontsize";
import { moderateScale } from "src/app/Utils/Metrics";

const customStyles = (): IStyleProps => {
  return {
    progressBox: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        marginBottom: RFValue(20),
        marginTop: RFValue(15),
      },
    },
    progressStepBar: {
      style: {
        height: moderateScale(3),
        width: moderateScale(16),
        borderRadius: 2,
        marginHorizontal: "2%",
    
      },
    },
  };
};

export default customStyles;
