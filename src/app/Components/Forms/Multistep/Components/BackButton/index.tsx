import {HStack, Pressable, Text} from "native-base";
import React from "react";
import {Resources} from "src/app/Context/Utils/Resources";
import customStyles from "src/app/Components/Forms/Multistep/Components/BackButton/styles";
import {ChevronLeft} from "src/assets/Icons/Flaticon";

interface Props {
  onPress: () => void;
}

const BackButton = ({onPress}: Props) => {
  const styles = customStyles();
  const resources = Resources.get().multiStepForm;

  return (
    <Pressable
      {...styles.pressable}
      onPress={onPress}
      _pressed={{opacity: 0.5}}>
      <HStack space={2} {...styles.hstackPressable}>
        <ChevronLeft {...styles.iconPreviousBtn.style} />
        <Text {...styles.txtBack}>{resources.go_back}</Text>
      </HStack>
    </Pressable>
  );
};

export default BackButton;
