import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    pressable: {
      style: {
        justifyContent: "center",

        height: verticalScale(45),
      },
    },

    hstackPressable: {
      width: "95%",
      style: {
        alignItems: "center",
      },
    },
    iconPreviousBtn: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.lime[500],
      },
    },

    txtBack: {
      style: {
        color: ThemesApp.getTheme().colors.lime[500],
        fontWeight: "bold",
        fontSize: RFValue(16),
        lineHeight: RFValue(18),
      },
    },
  };
};

export default customStyles;
