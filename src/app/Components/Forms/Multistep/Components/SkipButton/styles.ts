import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    skipButton: {
      style: {
        justifyContent: "center",
      },
    },
    icon: {
      style: {
        fill: ThemesApp.getTheme().colors.heartIcon,
        width: moderateScale(12),
        height: moderateScale(12),
      },
    },
  };
};
export default customStyles;
