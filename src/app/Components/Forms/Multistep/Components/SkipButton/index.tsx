import { Button } from "native-base";
import React from "react";
import customStyles from "src/app/Components/Forms/Multistep/Components/SkipButton/styles";
import { Resources } from "src/app/Context/Utils/Resources";
import { Block } from "src/assets/Icons/Flaticon";

interface Props {
  onPress: () => void;
}

const SkipButton = ({ onPress }: Props) => {
  const styles = customStyles();
  const resources = Resources.get().multiStepForm;
  return (
    <Button variant="link" onPress={onPress} {...styles.skipButton} leftIcon={<Block {...styles.icon.style} />}>
      {resources.skip_step}
    </Button>
  );
};

export default SkipButton;
