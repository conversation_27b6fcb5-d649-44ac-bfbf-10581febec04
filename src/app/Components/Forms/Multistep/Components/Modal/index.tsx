import {Button} from "native-base";
import React from "react";
import customStyles from "src/app/Components/Forms/Multistep/Components/Modal/styles";
import ModalRN from "src/app/Components/Modal";
import {Resources} from "src/app/Context/Utils/Resources";
import {rootGoBack} from "src/app/Utils/RootNavigation";

type Props = {
  isVisible: boolean;
  onClose: () => void;
};

const Modal = ({isVisible, onClose}: Props) => {
  const styles = customStyles();
  const resources = Resources.get();

  return (
    <ModalRN
      title={resources.multiStepForm.modal.title}
      visibility={isVisible}
      onClose={() => onClose()}
      componentFooter={
        <Button.Group direction="row" {...styles.buttonGroup}>
          <Button
            onPress={() => {
              onClose();
            }}
            {...styles.btnNegative}>
            {resources.multiStepForm.modal.button.negative}
          </Button>
          <Button
            onPress={() => {
              rootGoBack();
              onClose();
            }}
            {...styles.btnPositive}>
            {resources.multiStepForm.modal.button.positive}
          </Button>
        </Button.Group>
      }>
      {resources.multiStepForm.modal.text}
    </ModalRN>
  );
};

export default Modal;
