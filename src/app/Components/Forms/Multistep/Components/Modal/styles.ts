import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    buttonGroup: {
      style: {
        justifyContent: "flex-end",
        width: wp("100%"),
      },
    },

    btnPositive: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.secondary[400],
      },
    },

    btnNegative: {
      style: {
        width: wp("30%"),
      },
    },
  };
};

export default customStyles;
