/* eslint-disable react-hooks/exhaustive-deps */
import {zodResolver} from "@hookform/resolvers/zod";
import {useFocusEffect} from "@react-navigation/native";
import {Box, HStack, View, VStack} from "native-base";
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {FormProvider, useForm} from "react-hook-form";
import {<PERSON><PERSON><PERSON><PERSON>, ScrollView as SVRN} from "react-native";
import Animated from "react-native-reanimated";
import useStepAnimation from "src/app/Components/Forms/Multistep/Animations/Hooks/useStepAnimation";
import AnimatedLoading from "src/app/Components/Forms/Multistep/Animations/Loading";
import BackButton from "src/app/Components/Forms/Multistep/Components/BackButton";
import Modal from "src/app/Components/Forms/Multistep/Components/Modal";
import NextButton from "src/app/Components/Forms/Multistep/Components/NextButton";
import MultiStepProgress from "src/app/Components/Forms/Multistep/Components/Progress";
import customStyles from "src/app/Components/Forms/Multistep/styles";
import clearForm from "src/app/Components/Forms/Multistep/Utils";
import SkipButton from "./Components/SkipButton";

export type StepList = {
  step: JSX.Element;
  isOptional: boolean;
  initialValues?: object;
};

type Props = {
  steps: StepList[];
  schema: any;
  onSubmit: (
    data: any,
    onFinish: () => void,
    onSuccess: () => void,
  ) => Promise<void>;
  hideButtons?: boolean;
};

const MultiStep = memo(
  ({steps, schema, onSubmit, hideButtons = false}: Props) => {
    const styles = customStyles();
    const [currentFormStep, setCurrentFormStep] = useState(0);
    const [formStepIsOptional, setFormStepIsOptional] = useState(
      steps[currentFormStep].isOptional,
    );
    const [isLoading, setIsLoading] = useState(false);

    useFocusEffect(
      React.useCallback(() => {
        setCurrentFormStep(0);
        setFormStepIsOptional(steps[currentFormStep].isOptional);
      }, []),
    );

    const [visible, setVisible] = useState<boolean>(false);
    const {
      slideAnimation,
      animated,
      animatedError,
      executeAnimations,
      errorAnimation,
      executeErrorAnimation,
    } = useStepAnimation();

    const scrollRef = useRef<SVRN>(null);
    const onPressTouch = () => {
      scrollRef.current?.scrollTo({
        y: 0,
        animated: false,
      });
    };

    const quantitySteps = steps.length - 1;
    const isLastStep = currentFormStep === quantitySteps;

    const defaultValues = useMemo(() => {
      const join = steps.reduce((acc, step) => {
        return {...acc, ...step.initialValues};
      }, {});
      return join;
    }, [steps]);

    const methods = useForm({
      defaultValues: {
        formStep: "0",
        ...defaultValues,
      },
      resolver: zodResolver(schema),
      mode: "onBlur",
    });

    useFocusEffect(
      React.useCallback(() => {
        methods.reset({
          formStep: "0",
          ...defaultValues,
        });
      }, [defaultValues]),
    );

    const {handleSubmit, setValue, getValues, trigger, clearErrors, reset} =
      methods;

    // FIXME Add error handling
    const handleFormSubmit = () => {
      const data = getValues();

      setIsLoading(true);

      onSubmit(
        data,
        () => {
          setIsLoading(false);
        },
        () => {
          reset();
        },
      );
    };

    const handleNextStep = useCallback(async () => {
      const formsValidated = await trigger();
      if (formsValidated) {
        executeAnimations();
        const newStep = currentFormStep + 1;
        setValue("formStep", newStep.toString() as any);
        setCurrentFormStep(newStep);
        setFormStepIsOptional(steps[newStep].isOptional);
      }
      if (!formsValidated) {
        executeErrorAnimation();
      }
    }, [
      currentFormStep,
      executeAnimations,
      executeErrorAnimation,
      setValue,
      steps,
      trigger,
    ]);

    const handlePrevStep = useCallback(() => {
      if (currentFormStep > 0) {
        clearErrors();
        const newStep = currentFormStep - 1;
        setValue("formStep", newStep.toString() as any);
        setCurrentFormStep(newStep);
        setFormStepIsOptional(steps[newStep].isOptional);
        executeAnimations();
      }
    }, [clearErrors, currentFormStep, executeAnimations, setValue, steps]);

    const handleIgnoreStep = useCallback(async () => {
      executeAnimations();
      clearForm(steps[currentFormStep].initialValues!, setValue);
      const newStep = currentFormStep + 1;
      setValue("formStep", newStep.toString() as any);
      setCurrentFormStep(newStep);
      setFormStepIsOptional(steps[newStep].isOptional);
    }, [currentFormStep, executeAnimations, executeErrorAnimation, steps]);

    useEffect(() => {
      const backAction = () => {
        setVisible(true);
        return true;
      };

      const backHandler = BackHandler.addEventListener(
        "hardwareBackPress",
        backAction,
      );

      return () => backHandler.remove();
    }, []);

    useEffect(() => {
      executeAnimations();
    }, []);

    const onPressSkip = isLastStep
      ? handleSubmit(handleFormSubmit)
      : () => {
          handleIgnoreStep();
          onPressTouch();
        };

    const handleModal = () => {
      setVisible(!visible);
    };

    return (
      <View {...styles.mainView}>
        <FormProvider {...methods}>
          <Animated.View
            style={[
              animatedError && errorAnimation,
              animated && slideAnimation,
              styles.container.style,
            ]}>
            <VStack {...styles.container}>
              <Box {...styles.formContainer}>{steps[currentFormStep].step}</Box>
              {!hideButtons ? (
                <>
                  <Box {...styles.skipButtonContainer}>
                    {formStepIsOptional ? (
                      <SkipButton onPress={() => onPressSkip()} />
                    ) : null}
                  </Box>
                  <Box {...styles.footerBox}>
                    <MultiStepProgress
                      formStep={currentFormStep}
                      quantitySteps={quantitySteps}
                    />
                  </Box>
                  <HStack
                    {...styles.buttonsContainer}
                    justifyContent={
                      currentFormStep !== 0 ? "space-between" : "center"
                    }>
                    {currentFormStep !== 0 ? (
                      <BackButton
                        onPress={() => {
                          handlePrevStep();
                          onPressTouch();
                        }}
                      />
                    ) : null}

                    <NextButton
                      onPress={
                        isLastStep
                          ? handleSubmit(handleFormSubmit)
                          : async () => {
                              handleNextStep();
                              onPressTouch();
                            }
                      }
                      isLastStep={isLastStep}
                      isLoading={isLoading}
                    />
                  </HStack>
                </>
              ) : null}
            </VStack>
          </Animated.View>
        </FormProvider>
        {animated ? <AnimatedLoading /> : null}
        <Modal isVisible={visible} onClose={handleModal} />
      </View>
    );
  },
);

export default MultiStep;
