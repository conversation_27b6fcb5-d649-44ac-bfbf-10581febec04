import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    opacity: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.opacityAnimation,
        alignItems: "center",
        justifyContent: "center",
        display: "flex",
        flexDirection: "row",
        flex: 1,
        position: "absolute",
      },
    },
  };
};

export default customStyles;
