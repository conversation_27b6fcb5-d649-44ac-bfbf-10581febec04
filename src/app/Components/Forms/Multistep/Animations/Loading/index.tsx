import { Spinner } from "native-base";
import React from "react";
import Animated from "react-native-reanimated";
import useStepAnimation from "src/app/Components/Forms/Multistep/Animations/Hooks/useStepAnimation";

const AnimatedLoading = () => {
  const { opacityAnimation } = useStepAnimation();

  return (
    <Animated.View style={opacityAnimation}>
      <Spinner size="sm" />
    </Animated.View>
  );
};

export default AnimatedLoading;
