import { useState } from "react";
import { Dimensions } from "react-native";
import { useAnimatedStyle, withSequence, withTiming } from "react-native-reanimated";
import customStyles from "src/app/Components/Forms/Multistep/Animations/styles";
import animationSettings from "src/app/Components/Forms/Multistep/Animations/Utils/settings";

const useStepAnimation = () => {
  const styles = customStyles();
  const [animated, setAnimated] = useState(true);
  const [animatedError, setAnimatedError] = useState(false);
  const { width, height } = Dimensions.get("window");

  const slideAnimation = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: withSequence(
          withTiming(0, { duration: 0 }),
          withTiming(width, { duration: 0 }),
          withTiming(0, { duration: animationSettings.slide }),
        ),
      },
    ],
  }));

  const opacityAnimation = useAnimatedStyle(() => ({
    height,
    width,
    opacity: withSequence(
      withTiming(0, { duration: 0 }),
      withTiming(1, { duration: 0 }),
      withTiming(0, { duration: animationSettings.opacity }),
    ),
    ...styles.opacity.style,
  }));

  const errorAnimation = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: withSequence(
          withTiming(0, { duration: animationSettings.error * 0.15 }),
          withTiming(20, { duration: animationSettings.error * 0.55 }),
          withTiming(-10, { duration: animationSettings.error * 0.15 }),
          withTiming(10, { duration: animationSettings.error * 0.15 }),
          withTiming(0, { duration: animationSettings.error * 0.15 }),
        ),
      },
    ],
  }));

  const executeAnimations = () => {
    setAnimated(true);
    setTimeout(() => {
      setAnimated(false);
    }, animationSettings.timeout);
  };

  const executeErrorAnimation = () => {
    setAnimatedError(true);
    setTimeout(() => {
      setAnimatedError(false);
    }, animationSettings.timeout);
  };

  return {
    animated,
    animatedError,
    setAnimated,
    setAnimatedError,
    slideAnimation,
    opacityAnimation,
    errorAnimation,
    executeErrorAnimation,
    executeAnimations,
  };
};

export default useStepAnimation;
