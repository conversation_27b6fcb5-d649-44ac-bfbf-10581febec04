import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        flex: 1,
      },
    },
    formContainer: {
      padding: moderateScale(6),
      style: {
        flex: 1,
      },
    },
    buttonsContainer: {
      style: {
        height: "7%",
        alignItems: "center",
        paddingHorizontal: RFValue(20),
        marginBottom: "3%",
      },
    },
    skipButtonContainer: {
      style: {
        alignItems: "flex-end",
        paddingHorizontal: RFValue(13),
      },
    },
    progressBox: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        marginBottom: RFValue(20),
        width: "90%",
      },
    },
    vStackStaagger: {
      style: {
        margin: wp("2%"),
        position: "absolute",
        bottom: RFValue(20),
        right: RFValue(0),
      },
    },
    textItemStagger: {
      style: {
        paddingHorizontal: "3%",
        fontSize: RFValue(16),
        fontWeight: "bold",
      },
    },

    iconStore: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.textApp,
      },
    },
    progressStepBar: {
      style: {
        height: moderateScale(3),
        width: moderateScale(16),
        borderRadius: 2,
        marginHorizontal: "2%",
      },
    },
    buttonGroup: {
      style: {
        justifyContent: "flex-end",
        width: wp("100%"),
      },
    },

    btnPositive: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.secondary[400],
      },
    },

    btnNegative: {
      style: {
        width: wp("30%"),
      },
    },
    mainView: {
      style: {
        flex: 1,
        flexDirection: "row",
        overflow: "visible",
        display: "flex",
      },
    },
  };
};

export default customStyles;
