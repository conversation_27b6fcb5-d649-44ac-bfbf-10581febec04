import { z as Zod } from "zod";

const schemaZodStep1 = Zod.object({
  formStep: Zod.literal("0"),
  firstName: Zod.string(),
});

const teste = Zod.string().min(1);

const schemaZodStep2 = Zod.object({
  formStep: Zod.literal("1"),
  phone: teste,
});

export const schemaZod = Zod.discriminatedUnion("formStep", [
  schemaZodStep1,
  schemaZodStep2,
]);

export type SchemaZodType = Zod.infer<typeof schemaZod>;

export default schemaZod;
