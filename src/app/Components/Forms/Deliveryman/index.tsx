/* eslint-disable no-unneeded-ternary */
import {Box, Button, HStack, Text, VStack} from "native-base";
import React, {useEffect, useMemo, useState} from "react";
import {useFormContext} from "react-hook-form";
import AttachmentFile from "src/app/Components/Attachment";
import customStyles from "src/app/Components/Forms/Deliveryman/styles";
import ModalSimple from "src/app/Components/ModalSimple";
import Select from "src/app/Components/Select";
import useTranslation from "src/app/Hooks/useTranslation";
import useUser from "src/app/Zustand/Store/useUser";
import {AddFile, Check, Close} from "src/assets/Icons/Flaticon";
import {IFormCreateDeliverymanDTO} from "src/business/DTOs/Forms/Create/Deliveryman";
import EFile from "src/business/Enums/Models/EFile";
import EFileExtension from "src/business/Enums/Models/EFileExtension";
import EFileType from "src/business/Enums/Models/EFileType";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";
import {FileData} from "src/business/Models/File";
import applicationSingleton from "src/business/Singletons/Application";

interface Props {
  currentStatus?: EProfileStatus;
  setAttachments: (newAttachments: FileData[]) => void;
  create?: boolean;
}

const DeliverymanForm = ({currentStatus, setAttachments, create}: Props) => {
  const [modalAttachment, setModalAttachment] = useState({
    visibility: false,
    type: EFileType.document,
  });
  const styles = customStyles({create, currentStatus});
  const {userProfilesData} = useUser();
  const id = userProfilesData?.deliveryman?.id;
  const userId = applicationSingleton.authenticationResult?.userInfo.id;

  const {
    formState: {errors},
    watch,
    setValue,
    getValues,
  } = useFormContext<IFormCreateDeliverymanDTO>();

  const resources = useTranslation();

  const {
    forms: {deliveryman: deliverymanResources},
  } = resources;

  const attachments = watch("attachments");

  const hasUploadedCNH = useMemo(
    () => attachments?.some(attachment => attachment.type === EFileType.cnh),
    [attachments],
  );
  const hasUploadedVehicleDocument = useMemo(
    () =>
      attachments?.some(
        attachment => attachment.type === EFileType.vehicleDocument,
      ),
    [attachments],
  );
  const hasUploadedCriminalRecord = useMemo(
    () =>
      attachments?.some(
        attachment => attachment.type === EFileType.criminalRecord,
      ),
    [attachments],
  );

  useEffect(() => {
    if (
      hasUploadedCNH &&
      hasUploadedVehicleDocument &&
      hasUploadedCriminalRecord
    ) {
      setValue("isAttachmentValid", true);
    } else {
      setValue("isAttachmentValid", false);
    }
  }, [
    hasUploadedCNH,
    hasUploadedVehicleDocument,
    hasUploadedCriminalRecord,
    setValue,
  ]);

  const openModalAttachment = () => {
    setModalAttachment(state => ({...state, visibility: !state.visibility}));
  };

  const openCNHModalAttachment = () => {
    setModalAttachment({visibility: true, type: EFileType.cnh});
  };

  const openVehicleDocumentModalAttachment = () => {
    setModalAttachment({visibility: true, type: EFileType.vehicleDocument});
  };

  const openCriminalRecordModalAttachment = () => {
    setModalAttachment({visibility: true, type: EFileType.criminalRecord});
  };

  const handleDeleteAttachmentFromContext = (attachmentId: string) => {
    const currentAttachments = getValues("attachments");

    setValue(
      "attachments",
      currentAttachments?.filter(attachment => attachment.id !== attachmentId),
    );
  };

  return (
    <>
      {/* {currentStatus !== undefined ? (
        <ProgressProfileStatus currentStatus={currentStatus} profile={EProfile.deliveryman} />
      ) : null} */}
      <VStack {...styles.container}>
        <Select
          name="deliveryman_type"
          label={deliverymanResources.select.type.label}
          placeholder={deliverymanResources.select.type.placeholder}
          errorMessage={errors.deliveryman_type?.message}
          optionsLabels={deliverymanResources.select.type.options}
          optionsValues={[0, 1]}
          isDisabled={currentStatus === undefined ? false : true}
        />
        <Select
          name="deliveryman_modality"
          label={deliverymanResources.select.modality.label}
          placeholder={deliverymanResources.select.modality.placeholder}
          errorMessage={errors.deliveryman_modality?.message}
          optionsLabels={deliverymanResources.select.modality.options}
          optionsValues={[0, 1, 2]}
          isDisabled={currentStatus === undefined ? false : true}
        />

        {currentStatus === EProfileStatus.review ? (
          <HStack>
            <AddFile {...styles.attachFileIcon.style} />
            <Text ml={2}>{deliverymanResources.text.documentsUnderReview}</Text>
          </HStack>
        ) : (
          <Box {...styles.fileUploadContainer}>
            <Button
              variant="outline"
              leftIcon={<AddFile {...styles.attachFileIcon.style} />}
              rightIcon={
                hasUploadedCNH ? (
                  <Check {...styles.validIcon.style} />
                ) : (
                  <Close {...styles.invalidIcon.style} />
                )
              }
              onPress={openCNHModalAttachment}
              {...(hasUploadedCNH
                ? styles.buttonAttachmentValid
                : styles.buttonAttachmentInvalid)}>
              {deliverymanResources.input.cnh.label}
            </Button>
            <Button
              variant="outline"
              leftIcon={<AddFile {...styles.attachFileIcon.style} />}
              rightIcon={
                hasUploadedVehicleDocument ? (
                  <Check {...styles.validIcon.style} />
                ) : (
                  <Close {...styles.invalidIcon.style} />
                )
              }
              onPress={openVehicleDocumentModalAttachment}
              {...(hasUploadedVehicleDocument
                ? styles.buttonAttachmentValid
                : styles.buttonAttachmentInvalid)}>
              {deliverymanResources.input.vehicleDocument.label}
            </Button>
            <Button
              variant="outline"
              leftIcon={<AddFile {...styles.attachFileIcon.style} />}
              rightIcon={
                hasUploadedCriminalRecord ? (
                  <Check {...styles.validIcon.style} />
                ) : (
                  <Close {...styles.invalidIcon.style} />
                )
              }
              onPress={openCriminalRecordModalAttachment}
              {...(hasUploadedCriminalRecord
                ? styles.buttonAttachmentValid
                : styles.buttonAttachmentInvalid)}>
              {deliverymanResources.input.criminalRecord.label}
            </Button>
          </Box>
        )}

        <ModalSimple
          titleHeader={deliverymanResources.button.addFiles}
          visibility={modalAttachment.visibility}
          onClose={openModalAttachment}
          height="85%">
          <AttachmentFile
            entity={EFile.deliveryman}
            entityId={create ? undefined : id || userId}
            type={modalAttachment.type}
            extension={EFileExtension.pdf}
            buttonClose={false}
            justOneFile={true}
            initialItems={attachments?.filter(
              attachment => attachment.type === modalAttachment.type,
            )}
            updateFormContext={setAttachments}
            deleteAttachmentFromFormContext={handleDeleteAttachmentFromContext}
            maxSize={20}
          />
        </ModalSimple>
      </VStack>
    </>
  );
};

export default DeliverymanForm;
