import {RFValue} from "react-native-responsive-fontsize";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

interface Props {
  create?: boolean;
  currentStatus?: EProfileStatus;
}

const customStyles = ({create, currentStatus}: Props): IStyleProps => {
  return {
    container: {
      style: {},
    },

    fileUploadContainer: {
      style: {
        flexDirection: "column",
        justifyContent: "center",
        gap: horizontalScale(10),
      },
    },

    attachFileIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.primary[500],
      },
    },

    validIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.checkedIcon,
      },
    },

    invalidIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.error,
      },
    },

    buttonAttachmentInvalid: {
      style: {
        height: verticalScale(30),
        borderRadius: moderateScale(99),
        borderColor: ThemesApp.getTheme().colors.error,
        display:
          create === false &&
          (currentStatus === undefined ||
            currentStatus === EProfileStatus.review ||
            currentStatus === EProfileStatus.pendingDocuments)
            ? "flex"
            : "none",
      },
    },
    buttonAttachmentValid: {
      style: {
        height: verticalScale(30),
        borderRadius: moderateScale(99),
        borderColor: ThemesApp.getTheme().colors.checkedIcon,
        display:
          create === false &&
          (currentStatus === undefined ||
            currentStatus === EProfileStatus.review ||
            currentStatus === EProfileStatus.pendingDocuments)
            ? "flex"
            : "none",
      },
    },
  };
};

export default customStyles;
