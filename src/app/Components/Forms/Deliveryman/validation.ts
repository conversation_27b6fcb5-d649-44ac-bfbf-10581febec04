import ZodStringRequired from "src/app/Utils/Zod/ZodStringRequired";
import {z} from "zod";

const getSchemaCreateDeliveryman = () => {
  const schema = {
    deliveryman_type: ZodStringRequired(),
    deliveryman_modality: ZodStringRequired(),
    terms: z.boolean().refine(value => value, {
      message: "Terms should be accepted",
    }),
    isAttachmentValid: z.boolean().refine(value => value, {
      message: "Attachment is required",
    }),
    profileList: z.string().array(),
  };

  return schema;
};

export default getSchemaCreateDeliveryman;
