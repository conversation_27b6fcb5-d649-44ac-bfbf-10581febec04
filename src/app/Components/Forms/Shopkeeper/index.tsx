import {Box, Button} from "native-base";
import React, {useState} from "react";
import {useFormContext} from "react-hook-form";
import AttachmentFile from "src/app/Components/Attachment";
import customStyles from "src/app/Components/Forms/Shopkeeper/styles";
import ModalSimple from "src/app/Components/ModalSimple";
import useTranslation from "src/app/Hooks/useTranslation";
import useUser from "src/app/Zustand/Store/useUser";
import {AddFile} from "src/assets/Icons/Flaticon";
import {IFormCreateShopkeeperDTO} from "src/business/DTOs/Forms/Create/Shopkeeper";
import EFile from "src/business/Enums/Models/EFile";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";
import EFileExtension from "src/business/Enums/Models/EFileExtension";
import EFileType from "src/business/Enums/Models/EFileType";
import {FileData} from "src/business/Models/File";
import applicationSingleton from "src/business/Singletons/Application";

// [ ] Support initial data for updating and decide if render or not the button

interface Props {
  currentStatus?: EProfileStatus;
  setAttachments: (newAttachments: FileData[]) => void;
  create: boolean;
}
const ShopkeeperForm = ({currentStatus, setAttachments, create}: Props) => {
  const styles = customStyles({create, currentStatus});
  const {userProfilesData} = useUser();
  const id = userProfilesData?.shopkeeper?.id;
  const userId = applicationSingleton.authenticationResult?.userInfo.id;

  const [modalAttachment, setModalAttachment] = useState(false);
  const {
    // formState: { errors },
    watch,
  } = useFormContext<IFormCreateShopkeeperDTO>();

  const {
    forms: {shopkeeper: shopkeeperResources},
  } = useTranslation();

  const attachments = watch("attachments");

  const openModalAttachment = () => {
    setModalAttachment(!modalAttachment);
  };

  return (
    <>
      {/* {currentStatus !== undefined ? (
        <ProgressProfileStatus currentStatus={currentStatus} profile={EProfile.shopkeeper} />
      ) : null} */}

      {/* <Text>Shopkeeper fields were not defined</Text> */}
      {/* <Button onPress={handleSubmit(onSubmit)} {...styles.button}>
        {shopkeeperResources.button.signIn}
      </Button> */}

      <Box {...styles.boxFab}>
        <Button
          shadow={2}
          startIcon={<AddFile {...styles.iconPlus.style} />}
          onPress={openModalAttachment}
          _pressed={{opacity: 0.7}}
          _text={{...styles.textAdd}}
          {...styles.buttonAttachment}>
          {shopkeeperResources.button.addFiles}
        </Button>
      </Box>

      <ModalSimple
        titleHeader={shopkeeperResources.button.addFiles}
        visibility={modalAttachment}
        onClose={openModalAttachment}
        height="85%">
        <AttachmentFile
          entity={EFile.shopkeeper}
          entityId={create ? undefined : id || userId}
          type={EFileType.document}
          extension={EFileExtension.pdf}
          buttonClose={false}
          justOneFile={false}
          initialItems={attachments}
          updateFormContext={setAttachments}
          maxSize={20}
        />
      </ModalSimple>
    </>
  );
};

export default ShopkeeperForm;
