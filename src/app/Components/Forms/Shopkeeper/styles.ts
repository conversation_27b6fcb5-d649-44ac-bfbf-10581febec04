import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

interface Props {
  create?: boolean;
  currentStatus?: EProfileStatus;
}

const customStyles = ({ create, currentStatus }: Props): IStyleProps => {
  return {
    button: {
      style: {
        marginVertical: RFValue(5),
      },
    },

    boxFab: {
      style: {
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
      },
    },

    textAdd: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.white,
        marginLeft: horizontalScale(5),
        display:
          create === false &&
          (currentStatus === undefined ||
            currentStatus === EProfileStatus.review ||
            currentStatus === EProfileStatus.pendingDocuments)
            ? "flex"
            : "none",
      },
    },

    iconPlus: {
      style: {
        width: moderateScale(24),
        height: moderateScale(24),
        fill: ThemesApp.getTheme().colors.white,
      },
    },

    buttonAttachment: {
      padding: 5,
      style: {
        height: verticalScale(50),
        elevation: 5,
        borderRadius: moderateScale(99),
        backgroundColor: ThemesApp.getTheme().colors.headerSettingsHome,
        display:
          create === false &&
          (currentStatus === undefined ||
            currentStatus === EProfileStatus.review ||
            currentStatus === EProfileStatus.pendingDocuments)
            ? "flex"
            : "none",
      },
    },
  };
};

export default customStyles;
