import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "native-base";
import React from "react";
import customStyles from "src/app/Components/VerificationEmailModal/styles";
import { Resources } from "src/app/Context/Utils/Resources";
import { Close, WarningTriangle } from "src/assets/Icons/Flaticon";

interface ModalProps {
  visibility: boolean;
  onClose: any;
  onPressContinue: any;
}
const VerifyEmailModal: React.FC<React.PropsWithChildren<ModalProps>> = ({ visibility, onClose, onPressContinue }) => {
  const styles = customStyles();
  const resouces = Resources.get().settings;
  return (
    <Modal isOpen={visibility} onClose={onClose} closeOnOverlayClick={false}>
      <Modal.Content {...styles.modalContent}>
        <Modal.CloseButton icon={<Close />} _icon={{ style: styles.modalCloseButton.style }} {...styles.modalClose} />
        <Modal.Header {...styles.modalHeader}>
          <Text ellipsizeMode="tail" numberOfLines={3} {...styles.modalTextHeader}>
            {resouces.verifyEmail.text.verify_email_modal_tittle}
          </Text>
          <WarningTriangle {...styles.icon.style} />
        </Modal.Header>

        <Modal.Body {...styles.modalBody}>
          <Text {...styles.modalText}>{resouces.verifyEmail.text.verify_email_modal}</Text>
        </Modal.Body>
        <Modal.Footer {...styles.modalFooter}>
          <HStack>
            <Button colorScheme="secondary" marginRight={2} onPress={() => onClose(false)}>
              {resouces.verifyEmail.button.cancel}
            </Button>
            <Button onPress={() => onPressContinue()}>{resouces.verifyEmail.button.validate_email}</Button>
          </HStack>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export default VerifyEmailModal;
