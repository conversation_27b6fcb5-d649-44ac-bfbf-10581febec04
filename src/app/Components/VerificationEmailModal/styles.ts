import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { RFValue } from "react-native-responsive-fontsize";
import { moderateScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    modalContent: {
      style: {
        width: wp("90%"),
        maxWidth: wp("100%"),
      },
    },

    modalTextHeader: {
      style: {
        fontSize: RFValue(15),
        lineHeight: RFValue(17),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.red[600],
      },
    },

    modalText: {
      style: {
        textAlign: "justify",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    icon: {
      style: {
        marginLeft: wp(2),
        fill: ThemesApp.getTheme().colors.red[600],
      },
    },

    modalHeader: {
      py: 2,
      flexDirection: "row",
      alignItems: "center",
    },

    modalClose: {
      alignItems: "center",
      p: 0,
    },

    modalBody: {
      py: 2,
    },

    modalFooter: {
      py: 2,
    },

    modalCloseButton: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
      },
    },
  };
};

export default customStyles;
