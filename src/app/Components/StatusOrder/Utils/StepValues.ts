import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {
  Check,
  Close,
  CreditCard,
  FastFood,
  HatChefOutline,
  Motorcycle,
  PeopleCarryBox,
  Route,
} from "src/assets/Icons/Flaticon";
import {OrderStatusStep} from "src/business/DTOs/OrdersStatusList";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {OrderStatusList} from "src/business/Models/List/OrderStatus";
import {EProfile} from "src/business/Models/Profile";

const color = ThemesApp.getTheme().colors;

export const getStepsByUserType = (
  userType: string,
  labels: OrderStatusList[],
) => {
  const userTypeLabels =
    userType === EProfile.client
      ? labels
      : userType === EProfile.shopkeeper
      ? labels.filter(item => item.value !== EOrderStatusValue.placed_order)
      : labels.filter(item =>
          [
            EOrderStatusValue.waiting_for_the_delivery_person,
            EOrderStatusValue.on_route_to_store,
            EOrderStatusValue.on_delivery_route,
            EOrderStatusValue.delivered,
            EOrderStatusValue.canceled_delivery,
          ].includes(item.value),
        );
  return userTypeLabels;
};

export const getCurrentStep = (stepList: OrderStatusStep[]) => {
  const currentStep = stepList[stepList.length - 1];
  return currentStep;
};

export const getIcon = (value: EOrderStatusValue) => {
  const result =
    value === EOrderStatusValue.placed_order ||
    value === EOrderStatusValue.pending_payment
      ? FastFood
      : value === EOrderStatusValue.payment_made
      ? CreditCard
      : value === EOrderStatusValue.preparing
      ? HatChefOutline
      : value === EOrderStatusValue.waiting_for_the_delivery_person
      ? PeopleCarryBox
      : value === EOrderStatusValue.on_route_to_store
      ? Motorcycle
      : value === EOrderStatusValue.on_delivery_route
      ? Route
      : value === EOrderStatusValue.rejected ||
        value === EOrderStatusValue.canceled ||
        value === EOrderStatusValue.canceled_delivery ||
        value === EOrderStatusValue.canceled_payment_failure
      ? Close
      : Check;
  return result;
};

export const getPrimaryColor = (
  value: EOrderStatusValue,
  currentValue: boolean,
) => {
  return value === EOrderStatusValue.rejected ||
    value === EOrderStatusValue.canceled ||
    value === EOrderStatusValue.canceled_delivery ||
    value === EOrderStatusValue.canceled_payment_failure
    ? color.canceledStatus
    : currentValue
    ? color.currentStatusPrimary
    : color.notCurrentStatusPrimary;
};

export const getSecondaryColor = (
  value: EOrderStatusValue,
  currentValue: boolean,
) => {
  return value === EOrderStatusValue.rejected ||
    value === EOrderStatusValue.canceled ||
    value === EOrderStatusValue.canceled_delivery ||
    value === EOrderStatusValue.canceled_payment_failure
    ? color.red[300]
    : currentValue
    ? color.green[300]
    : color.muted[50];
};
