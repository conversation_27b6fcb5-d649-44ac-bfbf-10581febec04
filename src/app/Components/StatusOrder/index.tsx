import {Box, Divider, IStackProps, Pressable, Stack} from "native-base";
import React, {useState} from "react";
import {widthPercentageToDP} from "react-native-responsive-screen";
import StatusOrderStep from "src/app/Components/StatusOrder/Components/StatusStep";
import customStyles from "src/app/Components/StatusOrder/styles";
import {getStepsByUserType} from "src/app/Components/StatusOrder/Utils/StepValues";
import TrackingOrderComponent from "src/app/Components/TrackingOrder";
import {verticalScale} from "src/app/Utils/Metrics";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {ExpandArrows} from "src/assets/Icons/Flaticon";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {OrderStatusList} from "src/business/Models/List/OrderStatus";

interface IComponentProps extends IStackProps {
  userType: string;
  orderId: string;
  status: OrderStatusList[];
  currentStatus: EOrderStatusValue;
  createdAt: Date;
  detailedStatus: boolean;
}

const StatusOrder = ({
  userType,
  status,
  currentStatus,
  createdAt,
  orderId,
  detailedStatus = true,
  ...rest
}: IComponentProps) => {
  const styles = customStyles();
  const [showMap, setShowMap] = useState(false);
  const userTypeStatus = getStepsByUserType(userType, status);

  return (
    <Stack {...rest}>
      {showMap && (
        <Box {...styles.boxMap}>
          <TrackingOrderComponent
            orderId={orderId}
            dimensionsMap={{
              width: widthPercentageToDP("89%"),
              height: verticalScale(180),
            }}
            zoomLevel={{min: 5, max: 16}}
            closeMap={() => setShowMap(false)}
            showsMyLocationButton={false}
          />

          <Pressable
            onPress={() =>
              rootNavigation("MainApp", {
                screen: "TrackingOrder",
                params: {orderId, status: currentStatus},
              })
            }
            {...styles.expandBtn}>
            <ExpandArrows {...styles.expandIcon.style} />
          </Pressable>
        </Box>
      )}
      {detailedStatus && userTypeStatus.length > 0 ? (
        <Box>
          {userTypeStatus.map((currentUserStatus, index) => {
            return (
              <Box key={currentUserStatus.id}>
                <StatusOrderStep
                  status={currentUserStatus.value}
                  createdAt={currentUserStatus.createdAt}
                  isHighlighted={currentUserStatus.current}
                  openMap={() => setShowMap(true)}
                  visibilityButton={showMap}
                />
                {index + 1 < userTypeStatus.length ? (
                  <Divider orientation="vertical" {...styles.divider} />
                ) : null}
              </Box>
            );
          })}
        </Box>
      ) : (
        <Box>
          <StatusOrderStep
            status={currentStatus}
            createdAt={createdAt}
            isHighlighted={true}
            openMap={() => setShowMap(true)}
            visibilityButton={showMap}
          />
        </Box>
      )}
    </Stack>
  );
};

export default StatusOrder;
