import {Badge, Box, Pressable, Text} from "native-base";
import React from "react";
import StepIcon from "src/app/Components/StatusOrder/Components/StatusIcon";
import customStyles from "src/app/Components/StatusOrder/Components/StatusStep/styles";
import {
  getPrimaryColor,
  getSecondaryColor,
} from "src/app/Components/StatusOrder/Utils/StepValues";
import useTranslation from "src/app/Hooks/useTranslation";
import getDateByLocale from "src/app/Utils/GetDateByLocale";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {EProfile} from "src/business/Models/Profile";

interface IComponentProps {
  status: EOrderStatusValue;
  createdAt: Date;
  isHighlighted?: boolean;
  openMap?: () => void;
  visibilityButton?: boolean;
}

const StatusOrderStep = ({
  openMap,
  status,
  createdAt,
  isHighlighted = false,
  visibilityButton,
}: IComponentProps) => {
  const styles = customStyles();
  const {orders: resources} = useTranslation();
  const {selectedProfile} = useGeneralSettings();
  const isDeliveryman = selectedProfile === EProfile.deliveryman;
  return (
    <Box {...styles.mainBox}>
      <Box {...styles.iconBox}>
        <Box
          bgColor={getSecondaryColor(status, isHighlighted)}
          borderColor={getPrimaryColor(status, isHighlighted)}
          borderWidth={isHighlighted ? 2 : 1}
          borderRadius="full"
          {...styles.iconContainer}>
          <StepIcon value={status} currentValue={isHighlighted} />
        </Box>
      </Box>
      <Box {...styles.textContainer}>
        <Box {...styles.textPosition}>
          <Box {...styles.textFormat}>
            <Text
              color={getPrimaryColor(status, isHighlighted)}
              fontWeight={isHighlighted ? "bold" : "normal"}
              {...styles.text}>
              {resources.status[status]}
            </Text>
            <Text {...styles.textHour}>
              {setResourceParameters(
                resources.label.at,
                getDateByLocale({onlyTime: false, date: createdAt}),
              )}
            </Text>
          </Box>

          {(status === EOrderStatusValue.on_delivery_route ||
            (isDeliveryman &&
              status === EOrderStatusValue.on_route_to_store)) &&
            isHighlighted &&
            !visibilityButton && (
              <Pressable onPress={openMap} _pressed={{opacity: 0.5}}>
                <Badge
                  variant="solid"
                  _text={{...styles.textBadgeButton}}
                  {...styles.badge}>
                  {resources.placeholder.tracking}
                </Badge>
              </Pressable>
            )}
        </Box>
      </Box>
    </Box>
  );
};

export default StatusOrderStep;
