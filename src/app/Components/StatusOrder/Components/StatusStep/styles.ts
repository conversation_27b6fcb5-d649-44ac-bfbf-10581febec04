import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainBox: {
      style: {
        flexDirection: "row",
      },
    },
    iconBox: {
      style: {
        width: wp("10%"),
        alignItems: "center",
      },
    },
    iconContainer: {
      style: {
        height: moderateScale(30),
        width: moderateScale(30),
        alignItems: "center",
        justifyContent: "center",
        borderStyle: "solid",
      },
    },
    textContainer: {
      style: {
        flex: 1,
        justifyContent: "center",
        marginLeft: 8,
      },
    },
    textPosition: {
      style: {
        justifyContent: "space-between",
        flexDirection: "row",
      },
    },
    textFormat: {
      style: {
        alignItems: "flex-start",
      },
    },

    text: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(12),
      },
    },

    textHour: {
      style: {
        color: ThemesApp.getTheme().colors.muted[500],

        fontSize: RFValue(10),
        lineHeight: RFValue(12),
      },
    },

    badge: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.primary[400],
        borderRadius: moderateScale(5),
      },
    },

    textBadgeButton: {
      style: {
        color: ThemesApp.getTheme().colors.white,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
  };
};

export default customStyles;
