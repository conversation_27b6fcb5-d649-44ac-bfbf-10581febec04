import React from "react";
import customStyles from "src/app/Components/StatusOrder/Components/StatusIcon/styles";

import { getIcon, getPrimaryColor } from "src/app/Components/StatusOrder/Utils/StepValues";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";

interface IComponentProps {
  value: EOrderStatusValue;
  currentValue: boolean;
}

const StepIcon = ({ value, currentValue }: IComponentProps) => {
  const styles = customStyles();
  const name = getIcon(value);
  const IconSvg = name;
  return value ? <IconSvg fill={getPrimaryColor(value, currentValue)} {...styles.iconSvg.style} /> : null;
};
export default StepIcon;
