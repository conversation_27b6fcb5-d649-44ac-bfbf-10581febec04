import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    iconBox: {
      style: {
        marginHorizontal: 10,
        width: "5%",
        height: "5%",
      },
    },
    iconPosition: {
      style: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        marginBottom: verticalScale(10),
      },
    },
    icon: {
      style: {
        width: moderateScale(14),
        height: moderateScale(14),
      },
    },
    divider: {
      style: {
        height: moderateScale(15),
        marginLeft: moderateScale(16),
        backgroundColor: ThemesApp.getTheme().colors.green[700],
        width: moderateScale(2),
      },
    },

    statusText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),

        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.muted[700],
      },
    },

    boxMap: {
      style: {
        borderColor: ThemesApp.getTheme().colors.muted[400],
        borderRadius: moderateScale(5),
        marginBottom: verticalScale(10),
        borderWidth: moderateScale(1),
        alignItems: "center",
      },
    },

    expandBtn: {
      bgColor: "gray.300",
      borderColor: "gray.500",
      style: {
        borderRadius: moderateScale(2),
        borderWidth: moderateScale(1),

        position: "absolute",
        bottom: 0,
        right: 0,
        margin: moderateScale(10),
        padding: moderateScale(5),
        zIndex: 5,
      },
    },

    expandIcon: {
      style: {
        fill: "black",
        width: moderateScale(14),
        height: moderateScale(14),
      },
    },
  };
};

export default customStyles;
