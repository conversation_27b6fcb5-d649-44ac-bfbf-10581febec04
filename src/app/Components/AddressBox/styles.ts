import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      alignItems: "center",
    },

    vStackContainer: {
      flex: 1,
    },

    text: {
      style: {
        color: ThemesApp.getTheme().colors.addressBoxText,
        fontSize: RFValue(14),
        flexWrap: "wrap",
        textAlign: "left",
      },
    },
    icon: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
        marginRight: moderateScale(10),
        fill: ThemesApp.getTheme().colors.secondary[600],
      },
    },
  };
};

export default customStyles;
