/* eslint-disable react/jsx-no-useless-fragment */
import React from "react";
import {VStack, Text, IStackProps, HStack} from "native-base";
import {Address} from "src/business/Models/Address/Address";

import {Pin} from "src/assets/Icons/Flaticon";
import customStyles from "src/app/Components/AddressBox/styles";

type Props = IStackProps & {
  data?: Partial<Address>;
};

const AddressBox: React.FC<Props> = ({data, ...rest}) => {
  const styles = customStyles();
  return (
    <>
      {data ? (
        <HStack {...rest} {...styles.container}>
          <Pin {...styles.icon.style} />
          <VStack {...styles.vStackContainer}>
            <Text {...styles.text}>{`${data.street} ${
              data.number || ""
            }`}</Text>
            <Text {...styles.text}>{data.district}</Text>
            <Text
              {...styles.text}>{`${data.city}, ${data.state}, ${data.postcode}`}</Text>
          </VStack>
        </HStack>
      ) : (
        <Text>Endereço não está cadastrado</Text>
      )}
    </>
  );
};

export default AddressBox;
