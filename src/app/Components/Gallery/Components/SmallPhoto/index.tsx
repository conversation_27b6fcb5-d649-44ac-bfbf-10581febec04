import { Pressable } from "native-base";
import React, { useCallback } from "react";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Extrapolate,
  interpolate,
} from "react-native-reanimated";
import { IAnimationProps } from "src/app/Components/Gallery/Interfaces/IAnimationProps";
import customStyles from "src/app/Components/Gallery/Components/SmallPhoto/styles";
import { ImageProps } from "react-native";
import VideoPlayer from "react-native-video-player";
import isVideoType from "../../Utils/GalleryFunctions";

const SmallerPhoto: React.FC<IAnimationProps> = (props) => {
  const styles = customStyles();
  const { animationValue, label, onPress, type } = props;

  const translateY = useSharedValue(0);

  const containerStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animationValue.value, [-1, 0, 1], [0.5, 1, 0.5], Extrapolate.CLAMP),
    };
  }, [animationValue]);

  const onPressIn = useCallback(() => {
    translateY.value = withTiming(-8, { duration: 250 });
  }, [translateY]);

  const onPressOut = useCallback(() => {
    translateY.value = withTiming(0, { duration: 250 });
  }, [translateY]);

  return (
    <Pressable onPress={onPress} onPressIn={onPressIn} onPressOut={onPressOut} {...styles.pressable}>
      {isVideoType(type) ? (
        <Animated.View style={[containerStyle]} {...styles.animatedView}>
          <VideoPlayer
            video={{
              uri: label,
            }}
            controls={true}
            hideControlsOnStart={true}
            loop={true}
            pauseOnPress={false}
            style={{ ...(styles.smallPhoto.style as ImageProps) }}
          />
        </Animated.View>
      ) : (
        <Animated.View style={[containerStyle]} {...styles.animatedView}>
          <Animated.Image source={{ uri: label }} style={{ ...(styles.smallPhoto.style as ImageProps) }} />
        </Animated.View>
      )}
    </Pressable>
  );
};

export default SmallerPhoto;
