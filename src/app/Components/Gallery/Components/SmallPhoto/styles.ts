import { horizontalScale, moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    smallPhoto: {
      style: {
        width: "100%",
        height: "100%",
        borderRadius: moderateScale(5),
        resizeMode: "contain",
      },
    },
    pressable: {
      marginRight: horizontalScale(5),
    },
  };
};

export default customStyles;
