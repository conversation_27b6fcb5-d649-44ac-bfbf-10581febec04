import React from "react";
import {PinchGestureHandler} from "react-native-gesture-handler";
import {IAnimationProps} from "src/app/Components/Gallery/Interfaces/IAnimationProps";
import customStyles from "src/app/Components/Gallery/Components/BiggerImage/styles";
import {Animated, ImageStyle} from "react-native";
import animatedConfig from "src/app/Components/Gallery/Components/BiggerImage/animatedConfig";
import VideoPlayer from "react-native-media-console";
import isVideoType from "src/app/Components/Gallery/Utils/GalleryFunctions";

const BiggerImage: React.FC<IAnimationProps> = props => {
  const styles = customStyles();
  const {label, type} = props;
  const scale = React.useRef(new Animated.Value(1)).current;

  const onPinchEvent = Animated.event(
    [
      {
        nativeEvent: {scale},
      },
    ],
    {useNativeDriver: true},
  );

  const onPinchStateChange = () => {
    Animated.spring(scale, animatedConfig).start();
  };
  return (
    <PinchGestureHandler
      onGestureEvent={onPinchEvent}
      onHandlerStateChange={onPinchStateChange}>
      {isVideoType(type) ? (
        <Animated.View
          style={{
            ...(styles.video.style as ImageStyle),
          }}>
          <VideoPlayer
            source={{
              uri: label,
            }}
            showDuration={true}
            disableSeekButtons={true}
            disableBack={true}
            disableFullscreen={true}
            onEnterFullscreen={() => {}}
            toggleResizeModeOnFullscreen={true}
            fullscreenOrientation="all"
            fullscreenAutorotate={true}
            style={{
              ...(styles.video.style as any),
            }}
          />
        </Animated.View>
      ) : (
        <Animated.Image
          source={{
            uri: label,
          }}
          style={{
            ...(styles.pinchGesture.style as ImageStyle),
            transform: [{scale}],
          }}
        />
      )}
    </PinchGestureHandler>
  );
};

export default BiggerImage;
