import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    pinchGesture: {
      style: {
        width: "100%",
        height: "100%",
        borderRadius: moderateScale(5),
        resizeMode: "cover",
      },
    },
    video: {
      style: {
        width: "100%",
        height: "100%",
        overflow: "visible",
      },
    },
  };
};

export default customStyles;
