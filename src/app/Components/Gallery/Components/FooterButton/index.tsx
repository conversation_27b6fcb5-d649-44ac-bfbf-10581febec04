import { Pressable, View } from "native-base";
import React from "react";
import { IFooterButtonProps } from "src/app/Components/Gallery/Interfaces/IFooterButton";
import customStyles from "src/app/Components/Gallery/Components/FooterButton/styles";

// const window: ScaledSize = Dimensions.get("window");

// const widthBiggerPhoto = window.width * 0.9;

const FooterButton = ({ icon, onPress, ...rest }: IFooterButtonProps) => {
  const styles = customStyles();
  return (
    <View {...styles.container}>
      <Pressable {...styles.iconButtonBox} onPress={onPress} _pressed={{ ...styles.pressableIcon }} {...rest}>
        {icon}
      </Pressable>
    </View>
  );
};

export default FooterButton;
