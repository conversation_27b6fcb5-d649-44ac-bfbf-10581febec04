import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },

    iconButtonBox: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.white,
        elevation: 4,
        zIndex: 100,
        borderRadius: moderateScale(999),
        alignItems: "center",
        justifyContent: "center",
        padding: moderateScale(8),
        opacity: 0.8,
      },
    },
    pressableIcon: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.muted[200],
        opacity: 0.2,
      },
    },
  };
};

export default customStyles;
