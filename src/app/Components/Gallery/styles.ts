import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      marginBottom: verticalScale(8),
    },

    carouselItem: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },

    buttonBox: {
      style: {
        marginTop: verticalScale(10),
        flexDirection: "row",
        justifyContent: "space-evenly",
      },
    },
    biggerImage: {
      style: {
        alignItems: "center",
        overflow: "visible",
      },
    },
    carouselContainer: {
      style: {
        width: "100%",
        justifyContent: "center",
        alignItems: "center",
        overflow: "visible",
      },
    },

    viewSmallCarousel: {
      style: {
        marginBottom: verticalScale(10),
      },
    },

    biggerImageContainer: {
      style: {
        justifyContent: "center",
        alignSelf: "center",
      },
    },
    animatedView: {
      style: {
        height: "100%",
      },
    },
    animatedImage: {
      style: {
        width: "100%",
        height: "100%",
      },
    },

    iconFooter: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.muted[900],
      },
    },

    iconFooterDisabled: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.muted[400],
      },
    },

    hStackFooterButton: {
      position: "absolute",
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      justifyContent: "space-between",
    },
  };
};

export default customStyles;
