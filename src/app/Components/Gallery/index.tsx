import {HStack, View} from "native-base";
import * as React from "react";
import {useState} from "react";
import {Dimensions, ScaledSize} from "react-native";
import {GestureHandlerRootView} from "react-native-gesture-handler";
import Carousel, {ICarouselInstance} from "react-native-reanimated-carousel";
import {CarouselRenderItemInfo} from "react-native-reanimated-carousel/lib/typescript/types";
import BiggerImage from "src/app/Components/Gallery/Components/BiggerImage";
import FooterButton from "src/app/Components/Gallery/Components/FooterButton";
import SmallerPhoto from "src/app/Components/Gallery/Components/SmallPhoto";
import customStyles from "src/app/Components/Gallery/styles";
import {ChevronLeft, ChevronRight} from "src/assets/Icons/Flaticon";

const window: ScaledSize = Dimensions.get("window");

const widthBiggerPhoto = window.width * 0.9;
const heightBiggerPhoto = widthBiggerPhoto;
const widthSmallPhoto = widthBiggerPhoto * 0.24;
const heightSmallPhoto = heightBiggerPhoto * 0.24;

export function useToggleButton(opts: {
  defaultValue: boolean;
  buttonTitle: string;
}) {
  const {defaultValue = false} = opts;
  const [status, setStatus] = React.useState(defaultValue);

  const button = React.useMemo(() => {
    return <FooterButton onPress={() => setStatus(!status)} />;
  }, [status]);

  return {
    status,
    button,
  };
}

type ICarouselProps = {
  photos: IObjectGalery[];
  showCarousel?: boolean;
};

export type IObjectGalery = {
  url: string;
  type?: string;
};

const Gallery = ({photos, showCarousel = true}: ICarouselProps) => {
  const styles = customStyles();
  const carouselRef = React.useRef<ICarouselInstance>(null);
  const loop = false;
  const biggerPhotoRef = React.useRef<ICarouselInstance>(null);
  const [currentIndex, setCurrentIndex] = useState<number | undefined>(0);

  const renderSmallCarousel = ({
    item,
    animationValue,
  }: CarouselRenderItemInfo<IObjectGalery>) => {
    return (
      <SmallerPhoto
        animationValue={animationValue}
        label={item.url}
        type={item.type}
        onPress={() => {
          carouselRef.current?.scrollTo({
            count: animationValue.value,
            animated: true,
          });
          biggerPhotoRef.current?.scrollTo({
            count: animationValue.value,
            animated: true,
          });
          setCurrentIndex(carouselRef.current?.getCurrentIndex());
        }}
      />
    );
  };

  const renderBigCarousel = ({
    item,
    animationValue,
  }: CarouselRenderItemInfo<IObjectGalery>) => {
    return (
      <GestureHandlerRootView {...styles.biggerImage}>
        <BiggerImage
          animationValue={animationValue}
          label={item.url}
          type={item.type}
        />
      </GestureHandlerRootView>
    );
  };

  return (
    <View {...styles.container}>
      {showCarousel ? (
        <View {...styles.viewSmallCarousel}>
          <Carousel
            key={`${loop}`}
            ref={carouselRef}
            loop={loop}
            {...styles.carouselContainer}
            width={widthSmallPhoto}
            height={heightSmallPhoto}
            data={photos}
            enabled={false}
            renderItem={renderSmallCarousel}
          />
        </View>
      ) : null}
      <View>
        <Carousel
          ref={biggerPhotoRef}
          {...styles.biggerImageContainer}
          width={widthBiggerPhoto}
          height={heightBiggerPhoto}
          loop={false}
          data={photos}
          enabled={false}
          renderItem={renderBigCarousel}
        />

        <HStack {...styles.hStackFooterButton}>
          <FooterButton
            onPress={() => {
              carouselRef.current?.prev();
              biggerPhotoRef.current?.prev();
              showCarousel
                ? setCurrentIndex(carouselRef.current?.getCurrentIndex())
                : setCurrentIndex(biggerPhotoRef.current?.getCurrentIndex());
            }}
            icon={
              currentIndex === 0 ? (
                <ChevronLeft {...styles.iconFooterDisabled.style} />
              ) : (
                <ChevronLeft {...styles.iconFooter.style} />
              )
            }
            isDisabled={currentIndex === 0}
            _disabled={{backgroundColor: "transparent"}}
          />

          <FooterButton
            onPress={() => {
              carouselRef.current?.next();
              biggerPhotoRef.current?.next();
              showCarousel
                ? setCurrentIndex(carouselRef.current?.getCurrentIndex())
                : setCurrentIndex(biggerPhotoRef.current?.getCurrentIndex());
            }}
            icon={
              currentIndex === photos.length - 1 ? (
                <ChevronRight {...styles.iconFooterDisabled.style} />
              ) : (
                <ChevronRight {...styles.iconFooter.style} />
              )
            }
            isDisabled={currentIndex === photos.length - 1}
            _disabled={{backgroundColor: "transparent"}}
          />
        </HStack>
      </View>
    </View>
  );
};

export default Gallery;
