import {HStack, IPressableProps, Pressable, Text} from "native-base";
import React from "react";

import customStyles from "src/app/Components/DrawerItem/styles";

type Props = IPressableProps & {
  label: string;
  children: React.ReactNode;
};

const DrawerItem: React.FC<Props> = ({label, children, ...rest}) => {
  const styles = customStyles();
  return (
    <Pressable {...rest} _pressed={{opacity: 0.5}}>
      <HStack {...styles.drawerItem}>
        {children}
        <Text {...styles.drawerLabel}>{label}</Text>
      </HStack>
    </Pressable>
  );
};

export default DrawerItem;
