import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { horizontalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    drawerItem: {
      style: {
        marginLeft: horizontalScale(5),
        alignItems: "center",
        margin: wp("2%"),
      },
    },

    drawerLabel: {
      style: {
        marginLeft: horizontalScale(10),
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
  };
};

export default customStyles;
