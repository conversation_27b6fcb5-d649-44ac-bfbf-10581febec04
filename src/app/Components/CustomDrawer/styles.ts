import {Dimensions} from "react-native";
import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale, verticalScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  const screenDimensions = Dimensions.get("screen");

  return {
    container: {
      style: {
        position: "absolute",
        height: "100%",
        top: 0,
        left: 0,
        zIndex: 100,
      },
    },
    contentContainer: {
      style: {
        flex: 1,
        zIndex: 51,
        backgroundColor: ThemesApp.getTheme().colors.drawerBackground,
        borderTopRightRadius: moderateScale(10),
      },
    },
    drawerIcon: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.drawerItemIcon,
      },
    },
    scrollViewContentContainer: {
      style: {
        paddingTop: hp("0%"),
      },
    },
    boxItems: {
      style: {
        flex: 1,
        backgroundColor: ThemesApp.getTheme().colors.drawerBackground,
      },
    },
    boxDrawerItems: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.drawerItemBackground,
        position: "absolute",
        top: RFValue(0),
        bottom: RFValue(0),
        left: RFValue(0),
        right: RFValue(0),
        borderTopLeftRadius: moderateScale(15),
      },
    },
    drawerItems: {
      style: {
        paddingTop: hp("2%"),
        paddingHorizontal: wp("2%"),
      },
    },
    dividerDetails: {
      style: {
        height: verticalScale(2),
        marginVertical: hp("1%"),
      },
    },
    drawerFooter: {
      backgroundColor: "drawerBackground",
      style: {},
    },
    textButtonLogout: {
      style: {
        color: ThemesApp.getTheme().colors.drawerItemText,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    boxLogoSetre: {
      backgroundColor: "drawerBackground",
      alignItems: "center",
      minHeight: "8%",
      maxHeight: "15%",
      position: "relative",
    },
    imageLogoSetre: {
      w: "70%",
      h: "full",
      overflow: "visible",
      marginTop: verticalScale(10),
      style: {
        backgroundColor: ThemesApp.getTheme().colors.logo,
      },
    },
    emptyContainer: {
      style: {
        width: screenDimensions.width,
        height: screenDimensions.height,
        position: "absolute",
      },
    },
    emptyPressable: {
      style: {
        backgroundColor: "black",
        width: screenDimensions.width,
        height: screenDimensions.height,
        position: "absolute",
        opacity: 0.5,
      },
    },
  };
};

export default customStyles;
