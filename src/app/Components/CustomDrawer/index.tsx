import {
  Box,
  Button,
  Divider,
  Image,
  Pressable,
  ScrollView,
  View,
} from "native-base";
import {InterfaceImageProps} from "native-base/lib/typescript/components/primitives/Image/types";
import React, {useMemo, useState} from "react";
import Animated, {
  FadeIn,
  FadeInLeft,
  FadeOut,
  FadeOutLeft,
} from "react-native-reanimated";
import EBottomTabItems from "src/app/Components/BottomTab/EBottomTabItems";
import LogoutModal from "src/app/Components/CustomDrawer/Components/LogoutModal";
import ProfileInfo from "src/app/Components/CustomDrawer/Components/ProfileInfo";
import customStyles from "src/app/Components/CustomDrawer/styles";
import DrawerItem from "src/app/Components/DrawerItem";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import {Components} from "src/app/Context/Utils/Components";
import {Resources} from "src/app/Context/Utils/Resources";
import navigateHome from "src/app/Utils/NavigateHome";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useBottomTabs from "src/app/Zustand/Store/useBottomTabs";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import {Heart, Home, Store} from "src/assets/Icons/Flaticon";
import {Power} from "src/assets/Icons/Flaticon/Simple/Power";
import logo from "src/assets/Images/SETRE_Logo_Vertical.png";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {IAuthService} from "src/business/Interfaces/Services/IAuth";
import {EProfile} from "src/business/Models/Profile";

const CustomDrawer = () => {
  const styles = customStyles();
  const [visible, setVisible] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const {pushTabsStack} = useBottomTabs();

  const resources = Resources.get();

  const authService = container.get<IAuthService>(TOKENS.AuthService);

  const handleLogout = async () => {
    setIsLoading(true);
    await authService.logout();

    rootNavigation("AuthApp", {screen: "SignIn"});
    showToastSuccess(resources.settings.home.logout.toasts.logout);
    setIsLoading(false);
    handleCloseDrawer();
  };
  const {selectedProfile} = useGeneralSettings();

  const handleCloseDrawer = () => {
    Components.closeDrawer();
  };

  const drawerOptions = useMemo(
    () => [
      {
        shouldRender: true,
        item: (
          <DrawerItem
            key={1}
            onPress={() => {
              handleCloseDrawer();
              navigateHome();
            }}
            label={resources.routes.DrawerHome}>
            <Home {...styles.drawerIcon.style} />
          </DrawerItem>
        ),
      },
      {
        shouldRender: selectedProfile === EProfile.shopkeeper,
        item: (
          <DrawerItem
            key={2}
            onPress={() => {
              handleCloseDrawer();
              rootNavigation("MainApp", {screen: "StoreHome"});
              pushTabsStack(EBottomTabItems.STORES);
            }}
            label={resources.routes.StoreHome}>
            <Store {...styles.drawerIcon.style} />
          </DrawerItem>
        ),
      },
      {
        shouldRender: selectedProfile === EProfile.client,
        item: (
          <DrawerItem
            key={3}
            onPress={() => {
              handleCloseDrawer();
              rootNavigation("MainApp", {screen: "FavoriteProducts"});
              pushTabsStack(EBottomTabItems.HOME);
            }}
            label={resources.routes.FavoriteProducts}>
            <Heart {...styles.drawerIcon.style} />
          </DrawerItem>
        ),
      },
    ],
    [
      pushTabsStack,
      resources.routes.DrawerHome,
      resources.routes.FavoriteProducts,
      resources.routes.StoreHome,
      selectedProfile,
      styles.drawerIcon.style,
    ],
  );

  const handleModal = () => {
    setVisible(!visible);
  };

  return (
    <View style={styles.container.style}>
      <Animated.View
        style={styles.contentContainer.style}
        entering={FadeInLeft}
        exiting={FadeOutLeft}>
        <ScrollView
          contentContainerStyle={styles.scrollViewContentContainer.style}>
          <ProfileInfo />
          <Box {...styles.boxItems}>
            <Box {...styles.boxDrawerItems} />
            <Box {...styles.drawerItems}>
              {drawerOptions.map(option => {
                if (option.shouldRender) {
                  return option.item;
                }

                return null;
              })}
            </Box>
          </Box>
        </ScrollView>

        <Box {...styles.drawerFooter}>
          <Divider {...styles.dividerDetails} />
          <Button
            leftIcon={<Power {...styles.drawerIcon.style} />}
            onPress={() => handleModal()}
            variant="ghost"
            colorScheme="primary"
            _text={{...styles.textButtonLogout}}>
            {resources.settings.home.logout.button.logout}
          </Button>
        </Box>

        <Box {...styles.boxLogoSetre}>
          <Image
            source={logo}
            alt="Logo SETRE"
            resizeMode="contain"
            {...(styles.imageLogoSetre as InterfaceImageProps)}
          />
        </Box>
      </Animated.View>
      <Animated.View
        entering={FadeIn}
        exiting={FadeOut}
        style={styles.emptyContainer.style}>
        <Pressable
          onPress={handleCloseDrawer}
          style={styles.emptyPressable.style}
        />
      </Animated.View>
      <LogoutModal
        isLoading={isLoading}
        isVisible={visible}
        onClose={handleModal}
        handleLogout={handleLogout}
      />
    </View>
  );
};

export default CustomDrawer;
