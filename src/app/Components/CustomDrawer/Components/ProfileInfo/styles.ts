import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale, verticalScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainBox: {
      style: {
        borderBottomRightRadius: moderateScale(20),
        borderTopRightRadius: moderateScale(10),
        backgroundColor: ThemesApp.getTheme().colors.drawerHeaderBackground,
      },
    },
    customDrawer: {
      style: {
        alignItems: "center",
        paddingTop: hp("2%"),
        paddingBottom: hp("2%"),
        paddingHorizontal: wp("2%"),
      },
    },
    avatar: {
      size: moderateScale(70),
      style: {
        backgroundColor: "white",
      },
    },
    avatarDefaultIcon: {
      style: {
        width: moderateScale(70),
        height: moderateScale(70),
        fill: ThemesApp.getTheme().colors.drawerItemIcon,
      },
    },
    titleContainer: {
      style: {
        paddingTop: hp("1%"),
        alignItems: "center",
      },
    },
    textTitle: {
      style: {
        color: ThemesApp.getTheme().colors.drawerItemText,
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "bold",
      },
    },
    textSubtitle: {
      style: {
        color: ThemesApp.getTheme().colors.drawerItemText,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    stackSelectUser: {
      marginTop: verticalScale(15),
      alignItems: "center",
    },
    textUserProfile: {
      style: {
        color: ThemesApp.getTheme().colors.drawerItemText,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    shuffleIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.drawerItemIcon,
        width: moderateScale(16),
        height: moderateScale(16),
      },
    },
  };
};

export default customStyles;
