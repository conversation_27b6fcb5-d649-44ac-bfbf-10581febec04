import {Avatar, Box, Flex, HStack, Pressable, Text, VStack} from "native-base";
import React from "react";
import customStyles from "src/app/Components/CustomDrawer/Components/ProfileInfo/styles";
import {Components} from "src/app/Context/Utils/Components";
import {Resources} from "src/app/Context/Utils/Resources";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useUser from "src/app/Zustand/Store/useUser";
import {UserCircle} from "src/assets/Icons/Flaticon";
import Shuffle from "src/assets/Icons/Flaticon/Simple/Shuffle";
import {EProfile} from "src/business/Models/Profile";

const ProfileInfo = () => {
  const styles = customStyles();
  const {user} = useUser();
  const {profileNames} = Resources.get().forms.selectProfile;
  const {userProfiles} = useUser();
  const {selectedProfile} = useGeneralSettings();

  return (
    <Box {...styles.mainBox}>
      <VStack {...styles.customDrawer}>
        <Avatar
          source={{
            uri: user?.profilePictureUrl ? user?.profilePictureUrl : undefined,
          }}
          {...styles.avatar}>
          <UserCircle {...styles.avatarDefaultIcon.style} />
        </Avatar>
        <Flex {...styles.titleContainer}>
          <Text ellipsizeMode="tail" numberOfLines={1} {...styles.textTitle}>
            {user?.firstName}
          </Text>
          <Text ellipsizeMode="tail" numberOfLines={1} {...styles.textSubtitle}>
            {user?.email}
          </Text>
          {selectedProfile ? (
            <Pressable
              _pressed={{opacity: 0.5}}
              onPress={() => {
                Components.askForProfile();
              }}
              disabled={!userProfiles || userProfiles.length <= 1}>
              <HStack space={1} {...styles.stackSelectUser}>
                <Text {...styles.textUserProfile}>
                  {
                    profileNames[
                      EProfile[selectedProfile] as keyof typeof profileNames
                    ]
                  }
                </Text>
                {userProfiles && userProfiles.length > 1 && (
                  <Shuffle {...styles.shuffleIcon.style} />
                )}
              </HStack>
            </Pressable>
          ) : null}
        </Flex>
      </VStack>
    </Box>
  );
};

export default ProfileInfo;
