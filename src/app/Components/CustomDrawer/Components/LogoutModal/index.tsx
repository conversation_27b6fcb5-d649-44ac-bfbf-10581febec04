import {Button} from "native-base";
import React from "react";
import customStyles from "src/app/Components/CustomDrawer/Components/LogoutModal/styles";
import ModalRN from "src/app/Components/Modal";
import {Resources} from "src/app/Context/Utils/Resources";

type Props = {
  isVisible: boolean;
  isLoading: boolean;
  onClose: () => void;
  handleLogout: () => void;
};

const LogoutModal = ({isVisible, isLoading, onClose, handleLogout}: Props) => {
  const styles = customStyles();
  const resources = Resources.get();

  return (
    <ModalRN
      title={resources.settings.home.logout.modal.title}
      visibility={isVisible}
      onClose={() => onClose()}
      componentFooter={
        <Button.Group direction="row" {...styles.buttonGroup}>
          <Button
            onPress={() => {
              onClose();
            }}
            {...styles.btnNegative}>
            {resources.settings.home.logout.modal.btnNegative}
          </Button>
          <Button
            isLoading={isLoading}
            onPress={handleLogout}
            {...styles.btnPositive}>
            {resources.settings.home.logout.modal.btnPositive}
          </Button>
        </Button.Group>
      }>
      {resources.settings.home.logout.modal.text}
    </ModalRN>
  );
};

export default LogoutModal;
