import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    hStack: {
      style: {
        alignItems: "center",
        justifyContent: "space-between",
      },
    },
    icon: {
      style: {
        fill: ThemesApp.getTheme().colors.secondary[200],
        height: verticalScale(20),
        width: horizontalScale(20),
      },
    },
    title: {
      style: {
        fontSize: RFValue(16),
        lineHeight: RFValue(18),
        fontWeight: "bold",
        textAlign: "justify",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    description: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        flexWrap: "wrap",
        textAlign: "justify",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    collapse: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },
    closeIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.muted[700],
        height: verticalScale(22),
        width: horizontalScale(22),
      },
    },
    descriptionBox: {
      style: {
        width: wp("78%"),
      },
    },

    header: {
      position: "absolute",
      top: 2,
      zIndex: 99,
      w: "full",
    },

    pressable: {
      style: {
        alignSelf: "flex-end",
      },
    },

    pressableText: {
      style: {
        fontWeight: "bold",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.primary[600],
      },
    },
  };
};

export default customStyles;
