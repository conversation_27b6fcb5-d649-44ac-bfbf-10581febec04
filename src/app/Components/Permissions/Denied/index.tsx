import { rest } from "lodash";
import { <PERSON><PERSON>, Box, Collapse, HStack, Text, View, VStack } from "native-base";
import React, { useEffect, useState } from "react";
import { Pressable } from "react-native";
import { openSettings, RESULTS } from "react-native-permissions";
import customStyles from "src/app/Components/Permissions/Denied/styles";
import useTranslation from "src/app/Hooks/useTranslation";
import { handleInitialAppPermission } from "src/app/Utils/Permissions/Generic/handleInitialAppPermission";
import { handleOneAppPermission } from "src/app/Utils/Permissions/handleOneAppPermission";
import { Close, WarningTriangle } from "src/assets/Icons/Flaticon";
import { AppPermissionsDto } from "src/business/DTOs/Permission/AppPermission";

const PermissionDeniedModal = ({
  title,
  description,
  permission,
}: {
  title?: string;
  description?: string;
  permission?: AppPermissionsDto;
}) => {
  const styles = customStyles();
  const [visibility, setVisibility] = useState(false);
  const { permission: resources } = useTranslation();

  useEffect(() => {
    (async () => {
      if (permission && permission.android) {
        const data = await handleOneAppPermission(permission);
        setVisibility(!!(data === RESULTS.BLOCKED));
      }
      if (!permission) {
        const data = await handleInitialAppPermission();
        setVisibility(data);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return visibility ? (
    <View {...styles.header}>
      <Collapse isOpen={visibility} duration={300} {...styles.collapse}>
        <Alert status="warning" variant="left-accent" {...rest}>
          <VStack space={2}>
            <HStack space={2} {...styles.hStack}>
              <WarningTriangle {...styles.icon.style} />
              <Text {...styles.title}>{title || resources.label.title}</Text>
              <Close onPress={() => setVisibility(false)} {...styles.closeIcon.style} />
            </HStack>
            <Box {...styles.descriptionBox}>
              <Text {...styles.description}>{description || resources.text.description}</Text>
            </Box>
            <Pressable
              onPress={() => {
                setVisibility(false);
                openSettings();
              }}
              {...styles.pressable}
            >
              <Text {...styles.pressableText}>{resources.label.settings}</Text>
            </Pressable>
          </VStack>
        </Alert>
      </Collapse>
    </View>
  ) : null;
};

export default PermissionDeniedModal;
