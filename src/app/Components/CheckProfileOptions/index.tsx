import {Avatar, Box, HStack, Icon<PERSON>utton, Pressable, Text} from "native-base";
import React from "react";
import customStyles from "src/app/Components/CheckProfileOptions/styles";
import CheckSelectedProfiles from "src/app/Utils/CheckSelectedProfiles";
import {moderateScale} from "src/app/Utils/Metrics";
import {CheckCircle, Close, Warning} from "src/assets/Icons/Flaticon";
import {EProfile} from "src/business/Models/Profile";
import CheckProfileOptionsService from "src/app/Components/CheckProfileOptions/CheckProfileOptionsService";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";

interface Props {
  title?: string;
  selectedList: EProfile[];
  handleOptionChange: (profileType: number) => void;
  handleOptionUncheck: (profileTypeList: EProfile) => void;
}

const CheckProfileOptions = ({
  title,
  selectedList,
  handleOptionChange,
}: Props) => {
  const checkProfileOptionsService = CheckProfileOptionsService({
    handleOptionChange,
  });
  const styles = customStyles();
  const FormStepToEnum = (step: number) => {
    if (step === 4) return EProfile.client;
    if (step === 3) return EProfile.deliveryman;
    if (step === 2) return EProfile.shopkeeper;
    return EProfile.client;
  };

  return (
    <>
      {title ? <Text {...styles.select}>{title}</Text> : null}
      <HStack {...styles.optionsContainer}>
        {checkProfileOptionsService.profileOptions.length > 0 ? (
          checkProfileOptionsService.profileOptions.map(
            ({iconSvg: Icon, iconSize, name, type, status}) => {
              const {
                optionContainer,
                iconContainer,
                optionName,
                iconColor,
                sizeIncreaseCoefficient,
              } = checkProfileOptionsService.getStyles(type, status);

              return (
                <Box {...optionContainer} key={type}>
                  <Pressable
                    {...iconContainer}
                    _pressed={{opacity: 0.5}}
                    onPress={() =>
                      checkProfileOptionsService.handleOptionPress(type)
                    }>
                    <Avatar {...styles.avatar}>
                      <Icon
                        width={moderateScale(
                          iconSize * sizeIncreaseCoefficient,
                        )}
                        height={moderateScale(
                          iconSize * sizeIncreaseCoefficient,
                        )}
                        {...iconColor}
                      />
                      {CheckSelectedProfiles(
                        selectedList,
                        FormStepToEnum(type),
                      ) ? (
                        <Avatar.Badge {...styles.avatarBadge}>
                          <IconButton
                            icon={<CheckCircle {...styles.checkedIcon.style} />}
                            {...styles.iconButton}
                          />
                        </Avatar.Badge>
                      ) : null}
                      {status === EProfileStatus.pendingDocuments ? (
                        <Avatar.Badge {...styles.avatarBadge}>
                          <Warning {...styles.warningIcon.style} />
                        </Avatar.Badge>
                      ) : null}
                      {status === EProfileStatus.disapproved ? (
                        <Avatar.Badge {...styles.avatarBadge}>
                          <Close {...styles.iconDisapprovedStatus.style} />
                        </Avatar.Badge>
                      ) : null}
                    </Avatar>
                  </Pressable>
                  <Text {...optionName}>{name}</Text>
                </Box>
              );
            },
          )
        ) : (
          <Text>{checkProfileOptionsService.resources.noOptions}</Text>
        )}
      </HStack>
    </>
  );
};

export default CheckProfileOptions;
