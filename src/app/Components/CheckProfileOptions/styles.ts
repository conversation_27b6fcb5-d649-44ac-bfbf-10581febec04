import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    title: {
      style: {
        color: ThemesApp.getTheme().colors.textApp,
        fontSize: RFValue(30),
        paddingLeft: horizontalScale(10),
        paddingTop: verticalScale(20),
        fontWeight: "bold",
      },
    },

    select: {
      style: {
        textAlign: "center",
        marginTop: verticalScale(20),
      },
    },
    optionsContainer: {
      style: {
        justifyContent: "center",
        alignItems: "center",
        marginVertical: verticalScale(20),
      },
    },
    optionContainer: {
      style: {
        justifyContent: "center",
        alignItems: "center",
        width: wp("30%"),
        opacity: 0.3,
      },
    },
    optionContainerSelected: {
      style: {
        justifyContent: "center",
        alignItems: "center",
        width: wp("30%"),
      },
    },
    iconContainer: {
      style: {
        borderColor: ThemesApp.getTheme().colors.iconSelectProfile,
        width: horizontalScale(60),
        height: horizontalScale(60),
        borderRadius: moderateScale(999),
        borderWidth: moderateScale(4),
        justifyContent: "center",
        alignItems: "center",
      },
    },
    iconContainerSelected: {
      style: {
        borderColor: ThemesApp.getTheme().colors.iconHelp,
        width: horizontalScale(70),
        height: horizontalScale(70),
        borderRadius: moderateScale(999),
        borderWidth: moderateScale(4),
        justifyContent: "center",
        alignItems: "center",
      },
    },
    iconSelected: {
      style: {
        fill: ThemesApp.getTheme().colors.iconHelp,
      },
    },
    icon: {
      style: {
        fill: ThemesApp.getTheme().colors.iconSelectProfile,
      },
    },
    optionName: {
      style: {
        color: ThemesApp.getTheme().colors.textApp,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        marginTop: verticalScale(10),
        fontWeight: "bold",
      },
    },
    optionNameSelected: {
      style: {
        color: ThemesApp.getTheme().colors.textApp,
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        marginTop: verticalScale(10),
        fontWeight: "bold",
      },
    },

    iconContainerOwnProfile: {
      style: {
        borderColor: ThemesApp.getTheme().colors.iconContainerOwnProfile,
        width: horizontalScale(60),
        height: horizontalScale(60),
        borderRadius: moderateScale(999),
        borderWidth: moderateScale(4),
        justifyContent: "center",
        alignItems: "center",
      },
    },

    iconContainerDisapproved: {
      style: {
        borderColor: ThemesApp.getTheme().colors.red[400],
        width: horizontalScale(60),
        height: horizontalScale(60),
        borderRadius: moderateScale(999),
        borderWidth: moderateScale(4),
        justifyContent: "center",
        alignItems: "center",
      },
    },
    avatarBadge: {
      size: moderateScale(25),
      position: "absolute",
      right: moderateScale(-6),
      bottom: moderateScale(-6),
      style: {
        backgroundColor: ThemesApp.getTheme().colors.white,
        alignItems: "baseline",
        justifyContent: "center",
      },
    },
    cancelBadge: {
      size: moderateScale(20),
      position: "absolute",
      right: moderateScale(-6),
      bottom: moderateScale(40),
      style: {
        backgroundColor: ThemesApp.getTheme().colors.white,
        alignItems: "baseline",
        justifyContent: "center",
      },
    },
    avatar: {
      size: "full",
      style: {
        backgroundColor: ThemesApp.getTheme().colors.avatarSelectProfile,
      },
    },

    iconButton: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        borderRadius: moderateScale(999),
      },
    },
    cancelIcon: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.muted[800],
      },
    },
    checkedIcon: {
      style: {
        width: moderateScale(22),
        height: moderateScale(22),
        fill: ThemesApp.getTheme().colors.checkedIcon,
      },
    },

    iconOwnProfile: {
      style: {
        fill: ThemesApp.getTheme().colors.iconContainerOwnProfile,
      },
    },

    badgerBox: {
      style: {
        position: "absolute",
        marginBottom: verticalScale(-6),
        marginRight: horizontalScale(-4),
        alignSelf: "flex-end",
      },
    },
    badgerBoxSelected: {
      style: {
        position: "absolute",
        marginBottom: verticalScale(-6),
        marginRight: horizontalScale(-4),
        alignSelf: "flex-end",
      },
    },
    generalBox: {
      style: {
        alignItems: "center",
      },
    },

    warningIcon: {
      style: {
        width: moderateScale(22),
        height: moderateScale(22),
        fill: ThemesApp.getTheme().colors.warning[600],
      },
    },

    iconContainerPendingReview: {
      style: {
        borderColor: ThemesApp.getTheme().colors.warning[600],
        width: horizontalScale(60),
        height: horizontalScale(60),
        borderRadius: moderateScale(999),
        borderWidth: moderateScale(4),
        justifyContent: "center",
        alignItems: "center",
      },
    },

    iconPendingReview: {
      style: {
        fill: ThemesApp.getTheme().colors.warning[600],
      },
    },

    iconDisapprovedStatus: {
      style: {
        alignSelf: "center",
        fill: ThemesApp.getTheme().colors.red[600],
      },
    },
  };
};

export default customStyles;
