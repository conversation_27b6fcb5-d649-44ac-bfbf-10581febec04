import {useLayoutEffect, useMemo, useState} from "react";
import customStyles from "src/app/Components/CheckProfileOptions/styles";
import {Resources} from "src/app/Context/Utils/Resources";
import CheckSelectedProfiles from "src/app/Utils/CheckSelectedProfiles";
import useUser from "src/app/Zustand/Store/useUser";
import {Motorcycle, ShoppingBag, Store} from "src/assets/Icons/Flaticon";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";
import {EProfile} from "src/business/Models/Profile";

interface Props {
  handleOptionChange: (profileType: number) => void;
}

const CheckProfileOptionsService = ({handleOptionChange}: Props) => {
  const {userProfilesData, getUserProfileList} = useUser();
  const userProfiles = getUserProfileList();
  const styles = customStyles();

  const resources = Resources.get().forms.selectProfile;

  const FormStepToEnum = (step: number) => {
    if (step === 4) return EProfile.client;
    if (step === 3) return EProfile.deliveryman;
    if (step === 2) return EProfile.shopkeeper;
    return EProfile.client;
  };

  const EnumToNumber = (profile: EProfile) => {
    if (profile === EProfile.client) return 4;
    if (profile === EProfile.deliveryman) return 3;
    if (profile === EProfile.shopkeeper) return 2;
    return 4;
  };

  const profileOptions = useMemo(() => {
    return [
      {
        type: 3, // EProfile.deliveryman
        name: resources.profileNames.deliveryman,
        iconSize: 26,
        iconSvg: Motorcycle,
        status: userProfilesData?.deliveryman?.status,
      },
      {
        type: 4, // EProfile.client
        name: resources.profileNames.client,
        iconSize: 24,
        iconSvg: ShoppingBag,
        status: undefined,
      },
      {
        type: 2, // EProfile.shopkeeper
        name: resources.profileNames.shopkeeper,
        iconSize: 20,
        iconSvg: Store,
        status: userProfilesData?.shopkeeper?.status,
      },
    ];
  }, [
    resources.profileNames.client,
    resources.profileNames.deliveryman,
    resources.profileNames.shopkeeper,
    userProfilesData?.deliveryman?.status,
    userProfilesData?.shopkeeper?.status,
  ]);

  const [selected, setSelected] = useState<EProfile>(EProfile.client);

  const makeSureCorrectSchemaWillBeUsed = () => {
    if (handleOptionChange && selected) {
      handleOptionChange(EnumToNumber(selected));
    }
  };

  useLayoutEffect(() => {
    makeSureCorrectSchemaWillBeUsed();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOptionPress = (formStep: number) => {
    setSelected(FormStepToEnum(formStep));
    if (handleOptionChange) handleOptionChange(formStep);
  };

  const getStyles = (type: number, status?: EProfileStatus) => {
    let optionContainer;
    let iconContainer;
    let optionName;
    let iconColor;
    let sizeIncreaseCoefficient = 1;
    if (FormStepToEnum(type) === selected) {
      optionContainer = styles.optionContainerSelected;
      iconContainer = !CheckSelectedProfiles(userProfiles, FormStepToEnum(type))
        ? styles.iconContainerSelected
        : status === EProfileStatus.pendingDocuments ||
          status === EProfileStatus.review
        ? styles.iconContainerPendingReview
        : status === EProfileStatus.disapproved
        ? styles.iconContainerDisapproved
        : styles.iconContainerOwnProfile;
      optionName = styles.optionNameSelected;
      iconColor = !CheckSelectedProfiles(userProfiles, FormStepToEnum(type))
        ? styles.iconSelected.style
        : status === EProfileStatus.pendingDocuments ||
          status === EProfileStatus.review
        ? styles.iconPendingReview.style
        : status === EProfileStatus.disapproved
        ? styles.iconDisapprovedStatus.style
        : styles.iconOwnProfile.style;
      sizeIncreaseCoefficient = 1.3;
    } else {
      optionContainer = styles.optionContainer;
      iconContainer = !CheckSelectedProfiles(userProfiles, FormStepToEnum(type))
        ? styles.iconContainer
        : status === EProfileStatus.pendingDocuments ||
          status === EProfileStatus.review
        ? styles.iconContainerPendingReview
        : status === EProfileStatus.disapproved
        ? styles.iconContainerDisapproved
        : styles.iconContainerOwnProfile;
      optionName = styles.optionName;
      iconColor = !CheckSelectedProfiles(userProfiles, FormStepToEnum(type))
        ? styles.icon.style
        : status === EProfileStatus.pendingDocuments ||
          status === EProfileStatus.review
        ? styles.iconPendingReview.style
        : status === EProfileStatus.disapproved
        ? styles.iconDisapprovedStatus.style
        : styles.iconOwnProfile.style;
    }
    return {
      optionContainer,
      iconContainer,
      optionName,
      iconColor,
      sizeIncreaseCoefficient,
    };
  };

  return {
    handleOptionPress,
    getStyles,
    profileOptions,
    resources,
    userProfiles,
    selected,
  };
};

export default CheckProfileOptionsService;
