import React, {memo} from "react";
import {Center, Text, Box, IconButton, HStack, VStack} from "native-base";
import {Image} from "react-native-image-crop-picker";
import {now} from "moment";

import useTranslation from "src/app/Hooks/useTranslation";
import EFile from "src/business/Enums/Models/EFile";
import {SaveFile} from "src/business/DTOs/SaveFile";
import {FileData} from "src/business/Models/File";
import customStyles from "src/app/Components/ImagePicker/styles";
import EFileType from "src/business/Enums/Models/EFileType";
import {
  handleCamera,
  handleGallery,
  handleRemoveFile,
  handleUploadFile,
  ImageResult,
} from "src/app/Components/ImagePicker/handleImage";

import {Camera, Folder, Trash} from "src/assets/Icons/Flaticon";
import showToastError from "src/app/Components/Toast/toastError";

export interface FileDataExt extends FileData {
  url_local?: string;
}
interface ImagePickerProps {
  title?: string;
  entity: EFile;
  entityId: string;
  fileId?: string;
  setUploadFile: (fd: FileDataExt | null) => void;
  onClose: () => void;
}

const ImagePicker = memo(
  ({
    title,
    entity,
    entityId,
    fileId,
    setUploadFile,
    onClose,
  }: ImagePickerProps) => {
    const styles = customStyles();

    const {imagePicker: resources} = useTranslation();

    const handleUpload = async (image: Image) => {
      const key = now();

      const data: SaveFile = {
        entity,
        entityId,
        key,
        size: image.size,
        type: EFileType.profilePhoto,
        master: true,
      };

      const uploadResult = await handleUploadFile(image, data);

      if (!uploadResult.error && uploadResult.fileData) {
        if (fileId) {
          await handleRemoveFile(fileId);
        }
        setUploadFile({...uploadResult.fileData, url_local: image.path});
      } else {
        showToastError(resources.toasts.upload_error, {placement: "top"});
      }
      onClose();
    };

    const handleRemove = async (id: string) => {
      const result = await handleRemoveFile(id);
      if (!result) {
        showToastError(resources.errors.remove_file, {placement: "top"});
      } else {
        setUploadFile(null);
      }
      onClose();
    };

    const handleResult = (imageResult: ImageResult) => {
      if (imageResult.error) {
        showToastError(resources.errors.did_cancel, {placement: "top"});
      } else if (!imageResult.image) {
        showToastError(resources.errors.picker_failed, {placement: "top"});
      } else {
        handleUpload(imageResult.image);
      }
    };

    const handlePhotoCamera = async () => {
      handleResult(await handleCamera());
    };

    const handlePhotoGallery = async () => {
      handleResult(await handleGallery());
    };

    return (
      <Center {...styles.mainCenter}>
        <Box>{title && <Text>{title}</Text>}</Box>
        <Box {...styles.boxButtons}>
          <HStack {...styles.hstack}>
            <Box {...styles.boxPhoto}>
              <VStack space={1} {...styles.vstack}>
                <IconButton
                  onPress={() => {
                    handlePhotoCamera();
                  }}
                  _pressed={{opacity: 0.8}}
                  icon={<Camera {...styles.icon.style} />}
                  {...styles.iconButton}
                />
                <Text {...styles.textIcon}>{resources.button.camera}</Text>
              </VStack>

              <VStack space={1} {...styles.vstack}>
                <IconButton
                  onPress={() => {
                    handlePhotoGallery();
                  }}
                  _pressed={{opacity: 0.8}}
                  icon={<Folder {...styles.icon.style} />}
                  {...styles.iconButton}
                />
                <Text {...styles.textIcon}>{resources.button.gallery}</Text>
              </VStack>
            </Box>
            <Box {...styles.boxRemove}>
              <VStack space={1} {...styles.vstack}>
                <IconButton
                  onPress={() => {
                    handleRemove(fileId!);
                  }}
                  isDisabled={!fileId}
                  _pressed={{opacity: 0.8}}
                  icon={<Trash {...styles.icon.style} />}
                  {...styles.iconButtonRemove}
                />
                <Text {...styles.textIcon}>{resources.button.delete}</Text>
              </VStack>
            </Box>
          </HStack>
        </Box>
      </Center>
    );
  },
);

export default ImagePicker;
