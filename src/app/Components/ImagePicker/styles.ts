import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    mainCenter: {
      style: {
        flex: 1,
        justifyContent: "flex-start",
        marginTop: verticalScale(20),
      },
    },

    iconButton: {
      size: moderateScale(40),
      borderRadius: "full",
      style: {
        backgroundColor: ThemesApp.getTheme().colors.iconUpload,
      },
    },

    iconButtonRemove: {
      size: moderateScale(40),
      borderRadius: "full",
      style: {
        backgroundColor: ThemesApp.getTheme().colors.heartIcon,
      },
    },

    hstack: {
      style: {
        width: wp("80%"),
        justifyContent: "space-between",
      },
    },

    vstack: {
      style: {
        alignItems: "center",
      },
    },

    icon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.white,
      },
    },

    textIcon: {
      style: {
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    boxPhoto: {
      flexDirection: "row",
      style: {
        width: wp("30%"),
        marginHorizontal: horizontalScale(5),
        justifyContent: "space-between",
      },
    },

    boxRemove: {
      style: {
        marginHorizontal: horizontalScale(10),
      },
    },

    boxButtons: {
      style: {
        alignItems: "center",
      },
    },
  };
};

export default customStyles;
