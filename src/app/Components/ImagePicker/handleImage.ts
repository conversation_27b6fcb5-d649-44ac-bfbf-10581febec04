import ImagePicker, {Image, Options} from "react-native-image-crop-picker";
import container from "src/business/Config/Inversify/InversifyConfig";
import {IFileService} from "src/business/Interfaces/Services/IFile";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import {SaveFile} from "src/business/DTOs/SaveFile";
import {FileData} from "src/business/Models/File";
import {DocumentPickerResponse} from "react-native-document-picker";
import AppError from "src/business/Tools/AppError";

export interface ImageResult {
  image?: Image;
  error?: string;
}

export interface UploadResult {
  fileData?: FileData;
  error?: string;
}

const fileSevice = container.get<IFileService>(TOKENS.FileService);

const handleCamera = async () => {
  const options: Options = {
    mediaType: "photo",
    width: 300,
    height: 400,
    cropping: true,
    cropperCircleOverlay: true,
    useFrontCamera: true,
  };

  const result: ImageResult = {image: undefined, error: ""};

  try {
    result.image = await ImagePicker.openCamera(options);
  } catch (error: any) {
    result.error = error;
  }

  return result;
};

const handleGallery = async () => {
  const options: Options = {
    mediaType: "photo",
    width: 300,
    height: 400,
    cropping: true,
    cropperCircleOverlay: true,
  };

  const result: ImageResult = {image: undefined, error: ""};

  try {
    result.image = await ImagePicker.openPicker(options);
  } catch (error: any) {
    result.error = error;
  }

  return result;
};

const handleUploadFile = async (image: Image, data: SaveFile) => {
  const result: UploadResult = {fileData: undefined, error: ""};

  const file: DocumentPickerResponse = {
    uri: image.path,
    name: `${data.entityId} - ${data.key}`,
    fileCopyUri: null,
    size: image.size,
    type: image.mime,
  };

  const response = await fileSevice.save(file, data);

  if (response instanceof AppError) {
    result.error = response.message;
  } else {
    result.fileData = response;
  }

  return result;
};

const handleRemoveFile = async (FileId: string) => {
  try {
    const response = await fileSevice.delete(FileId);
    return response;
  } catch (error) {
    return false;
  }
};

const handleGetPhotoProfile = async (FileId: string) => {
  const response = await fileSevice.getById(FileId);

  if (response instanceof AppError) {
    return undefined;
  } else {
    return response;
  }
};

export {
  handleCamera,
  handleGallery,
  handleUploadFile,
  handleRemoveFile,
  handleGetPhotoProfile,
};
