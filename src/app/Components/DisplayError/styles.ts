import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { moderateScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    boxContainer: {
      style: {
        margin: wp("5%"),
      },
    },

    textErrorMessage: {
      style: {
        color: ThemesApp.getTheme().colors.gray[500],
        fontWeight: "bold",
        marginLeft: wp("2%"),
      },
    },

    iconError: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.red[500],
      },
    },

    hStackError: {
      style: {
        marginBottom: hp("1%"),
        alignItems: "center",
      },
    },
  };
};

export default customStyles;
