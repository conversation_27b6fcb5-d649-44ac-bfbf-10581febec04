import React, { useEffect } from "react";
import { Box, ScrollView, Text, HStack } from "native-base";
import customStyles from "src/app/Components/DisplayError/styles";
import useTranslation from "src/app/Hooks/useTranslation";
import AppError from "src/business/Tools/AppError";
import ModalSimple from "src/app/Components/ModalSimple";
import { WarningOutline } from "src/assets/Icons/Flaticon";
import showToastError from "src/app/Components/Toast/toastError";

interface Props {
  appError?: AppError;
  onClose: () => void;
}

function resolve(path: string, obj: { [key: string]: any }) {
  return path.split(".").reduce(function (prev: any, curr) {
    return prev ? prev[curr] : null;
  }, obj);
}

const handleKeyMessage = (errorMessages: { [key: string]: string } | null, key: string) => {
  return errorMessages ? errorMessages[key] : "";
};

const DisplayError = ({ appError, onClose }: Props) => {
  const styles = customStyles();
  const resources = useTranslation();

  let entries: [string, string[]][] = [];
  const error = appError?.error;
  const message = appError?.message;

  useEffect(() => {
    showToastError(message, { placement: "top" });
  }, [message]);

  // if (message) {
  //   showToastError(message, { placement: "top" });
  //   // eslint-disable-next-line react/jsx-no-useless-fragment
  //   return <></>;
  // }

  if (!error?.generic && !error?.fields && appError?.status === 400) {
    showToastError(resources.generic.errors.label, { placement: "bottom" });
    // eslint-disable-next-line react/jsx-no-useless-fragment
    return <></>;
  }

  if (error) {
    if (error.generic && Object.keys(error.generic).length > 0) {
      entries = entries.concat(Object.entries(error.generic));
    }

    if (error.fields && Object.keys(error.fields).length > 0) {
      entries = entries.concat(Object.entries(error.fields));
    }
  }

  return (
    <ModalSimple onClose={onClose} visibility={!!error} titleHeader={resources.generic.errors.label}>
      <Box {...styles.boxContainer}>
        <ScrollView>
          {entries.map((errorObject) => {
            return errorObject[1].map((key) => {
              const messageError = handleKeyMessage(resolve(errorObject[0], resources), key);
              return (
                messageError && (
                  <HStack {...styles.hStackError} key={errorObject[0] + key}>
                    <WarningOutline {...styles.iconError.style} />
                    <Text {...styles.textErrorMessage}>{messageError}</Text>
                  </HStack>
                )
              );
            });
          })}
        </ScrollView>
      </Box>
    </ModalSimple>
  );
};

export default DisplayError;
