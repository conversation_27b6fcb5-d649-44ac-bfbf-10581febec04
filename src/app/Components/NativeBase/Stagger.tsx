import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> as <PERSON>aggerN<PERSON>, useDisclose } from "native-base";
import React, { useState } from "react";
import { Animated } from "react-native";
import customStyles from "src/app/Components/NativeBase/styles";
import { Plus } from "src/assets/Icons/Flaticon";

const Stagger = ({ children }: any) => {
  const styles = customStyles();
  const { isOpen, onToggle } = useDisclose();
  const [rotateAnimation] = useState(new Animated.Value(0));
  const handleAnimation = () => {
    Animated.timing(rotateAnimation, {
      toValue: isOpen ? 0 : 1,
      duration: 500,
      useNativeDriver: false,
    }).start(() => {});
  };

  const interpolateRotating = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "225deg"],
  });

  const animatedStyle = {
    transform: [
      {
        rotate: interpolateRotating,
      },
    ],
  };

  return (
    <Box {...styles.mainBoxStagger}>
      <Box {...styles.secondBoxStagger}>
        <StaggerNb
          visible={isOpen}
          initial={{
            opacity: 0,
            scale: 0,
            translateY: 34,
          }}
          animate={{
            translateY: 0,
            scale: 1,
            opacity: 1,
            transition: {
              type: "spring",
              duration: 500,
              mass: 0.8,
              stagger: {
                offset: 30,
                reverse: true,
              },
            },
          }}
          exit={{
            translateY: 34,
            scale: 0.5,
            opacity: 0,
            transition: {
              duration: 500,
              stagger: {
                offset: 30,
                reverse: true,
              },
            },
          }}
        >
          {children}
        </StaggerNb>
      </Box>
      <HStack {...styles.hStackStagger}>
        <Animated.View style={animatedStyle}>
          <IconButton
            icon={<Plus {...styles.iconPlusStagger.style} />}
            onPress={() => {
              onToggle();
              handleAnimation();
            }}
            {...styles.iconButtonStagger}
          />
        </Animated.View>
      </HStack>
    </Box>
  );
};

export default Stagger;
