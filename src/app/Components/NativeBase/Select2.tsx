import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, FlatList, FormControl, HStack, Input, Modal, Pressable, Text } from "native-base";
import React, { memo, useEffect, useState } from "react";
import { UseFormSetValue } from "react-hook-form";
import customStyles from "src/app/Components/NativeBase/styles";
import { Check, Close, Plus, Search, WarningOutline } from "src/assets/Icons/Flaticon";

export interface DataType {
  id: string;
  name: string;
  checked: boolean;
}

interface SelectProps {
  name: string;
  label: string;
  placeholder: string;
  loadOptions(
    currentPage: number,
    pageSize: number,
    inputValue: string,
  ): Promise<{
    result: DataType[];
    totalPages: number;
  }>;
  pageSize: number;
  cancelButtonText: string;
  saveButtonText: string;
  setFieldValue: UseFormSetValue<any>;
  isRequired?: boolean;
  errorMessage?: string;
  initialValues?: DataType[];
}

let currentPage = 1;
let totalPages: number | undefined;

const Select2 = ({
  name,
  label,
  placeholder,
  loadOptions,
  pageSize,
  cancelButtonText,
  saveButtonText,
  isRequired = true,
  errorMessage,
  setFieldValue,
  initialValues,
}: SelectProps) => {
  const styles = customStyles();
  const [modalVisibility, setModalVisibility] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState<DataType[]>([]);

  useEffect(() => {
    (async () => {
      currentPage = 1;
      const data = await loadOptions(currentPage, pageSize, "");

      totalPages = data.totalPages;

      if (initialValues) {
        data.result = data.result.map((item) => {
          const index = initialValues.findIndex((value) => value.id === item.id);
          if (index !== -1) {
            item.checked = initialValues[index].checked;
          }
          return item;
        });
      }
      setOptions(data.result);
    })();
  }, [initialValues, loadOptions, pageSize]);

  const setSelectedCategories = () => {
    setModalVisibility(false);
    setInputValue("");
    setFieldValue(
      name,
      options.filter((option) => option.checked),
    );
  };

  const handleSelectOption = (item: DataType) => {
    const optionsChecked = options?.map((option) => {
      if (option.id === item.id) {
        option.checked = !item.checked;
      }

      return option;
    });

    setOptions(optionsChecked);
  };

  const handleModalClose = () => {
    setSelectedCategories();
  };

  const handleChangeText = async (text: string) => {
    setInputValue(text);
    currentPage = 1;

    const filteredData = await loadOptions(currentPage, pageSize, text);
    totalPages = filteredData.totalPages;

    setOptions(filteredData.result);
  };

  const handlePaginate = async () => {
    console.log("Pagination fired", { currentPage, totalPages });

    if (totalPages && currentPage < totalPages) {
      currentPage++;

      const data = await loadOptions(currentPage, pageSize, inputValue);

      totalPages = data.totalPages;

      setOptions((currentValue) => {
        return currentValue.concat(data.result);
      });
    }
  };

  const renderItem = ({ item }: { item: DataType }) => {
    return (
      <>
        <Pressable onPress={() => handleSelectOption(item)} {...styles.pressable}>
          <HStack {...styles.hStack}>
            <Text {...styles.textCategory}>{item.name}</Text>
            {item.checked && <Check {...styles.iconCheckSelect.style} />}
          </HStack>
        </Pressable>
        <Divider />
      </>
    );
  };

  const optionsFiltered = inputValue.length
    ? options?.filter((option) => option.name.toLowerCase().includes(inputValue.toLowerCase()))
    : options;

  const optionsSelected = options?.length ? options?.filter((item) => item.checked) : [];

  return (
    <FormControl isRequired={isRequired} isInvalid={!!errorMessage}>
      <FormControl.Label _text={{ ...styles.formControlLabel }} _astrick={{ ...styles.formControlLabel }}>
        {label}
      </FormControl.Label>

      <Pressable onPress={() => setModalVisibility(!modalVisibility)} {...styles.pressableCategory}>
        <HStack {...styles.stackBadge}>
          {optionsSelected && optionsSelected.length > 0 ? (
            optionsSelected.map((option) => {
              return (
                <Badge
                  key={option.id}
                  _text={{ ...styles.textBadgeCategory }}
                  rightIcon={
                    <Close
                      onPress={() => {
                        handleSelectOption(option);
                        setSelectedCategories();
                      }}
                      {...styles.closeBadgeIcon.style}
                    />
                  }
                  {...styles.badgeCategory}
                >
                  {option.name}
                </Badge>
              );
            })
          ) : (
            <Text {...styles.textSelectedCategory}>{placeholder}</Text>
          )}
        </HStack>
        <Plus {...styles.iconPlus.style} />
      </Pressable>

      {optionsSelected.length <= 0 ? (
        <FormControl.ErrorMessage
          _text={{ fontSize: 10, lineHeight: 12 }}
          leftIcon={<WarningOutline {...styles.warningOutlineIcon.style} />}
          {...styles.formControlErrorMessage}
        >
          {errorMessage}
        </FormControl.ErrorMessage>
      ) : null}

      <Modal isOpen={modalVisibility} onClose={handleModalClose} {...styles.modal}>
        <Modal.Content {...styles.modalContent}>
          <Modal.CloseButton
            icon={<Close />}
            _icon={{ style: styles.modalCloseButton.style }}
            onPress={handleModalClose}
          />
          <Modal.Header {...styles.modalHeader} _text={{ ...styles.modalHeaderText }}>
            {placeholder}
          </Modal.Header>
          <Box {...styles.boxModal}>
            <FormControl>
              <Input
                placeholder={placeholder}
                value={inputValue}
                _input={{ maxLength: 50 }}
                onChangeText={handleChangeText}
                InputRightElement={<Search {...styles.iconSearch.style} />}
                {...styles.inputSearch}
              />
            </FormControl>

            <FlatList
              data={optionsFiltered}
              renderItem={renderItem}
              onEndReached={handlePaginate}
              onEndReachedThreshold={0.3}
              keyExtractor={(item: DataType) => item.id}
              {...styles.flatlist}
            />
          </Box>

          <Box {...styles.boxButtons}>
            <Button.Group space={4}>
              <Button
                variant="outline"
                onPress={() => {
                  setModalVisibility(false);
                  setInputValue("");
                }}
                {...styles.buttonBottom}
              >
                {cancelButtonText}
              </Button>
              <Button onPress={setSelectedCategories} {...styles.buttonBottom}>
                {saveButtonText}
              </Button>
            </Button.Group>
          </Box>
        </Modal.Content>
      </Modal>
    </FormControl>
  );
};

export default memo(Select2);
