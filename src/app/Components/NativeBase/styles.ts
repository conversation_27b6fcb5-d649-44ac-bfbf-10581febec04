import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    endIconSelect: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.checkedIcon,
        position: "absolute",
        right: RFValue(0),
      },
    },

    select: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.bgSelect,
        borderRadius: moderateScale(5),
      },
    },

    selectText: {
      style: {
        fontSize: RFValue(18),
        lineHeight: RFValue(18),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    boxSelect2: {
      style: {
        width: wp("100%"),
      },
    },

    pressable: {
      style: {
        flex: 1,
        height: wp("14%"),
        justifyContent: "space-evenly",
      },
    },

    hStack: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
        marginHorizontal: horizontalScale(10),
      },
    },

    textCategory: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },

    textBadgeCategory: {
      style: {
        color: ThemesApp.getTheme().colors.muted[100],
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
      },
    },

    pressableCategory: {
      style: {
        flexDirection: "row",
        borderWidth: moderateScale(1),
        borderColor: ThemesApp.getTheme().colors.muted[700],
        borderRadius: moderateScale(5),
        justifyContent: "space-between",
        alignItems: "center",
        paddingHorizontal: horizontalScale(5),
        paddingTop: horizontalScale(8),
        paddingBottom: horizontalScale(5),
      },
    },

    textSelectedCategory: {
      style: {
        color: ThemesApp.getTheme().colors.muted[500],
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        marginLeft: horizontalScale(10),
      },
    },

    iconPlus: {
      style: {
        width: moderateScale(24),
        height: moderateScale(24),
        fill: ThemesApp.getTheme().colors.muted[600],
      },
    },

    hStack2: {
      style: {
        borderBottomWidth: moderateScale(2),
        borderColor: ThemesApp.getTheme().colors.gray[300],
        borderRadius: moderateScale(5),
        width: wp("88%"),
        paddingVertical: wp("3%"),
        justifyContent: "space-between",
      },
    },

    modal: {
      size: "full",
      style: {
        justifyContent: "flex-end",
      },
    },

    modalContent: {
      style: {
        height: hp("67%"),
        borderTopLeftRadius: moderateScale(15),
        borderTopRightRadius: moderateScale(15),
      },
    },

    modalCloseButton: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
      },
    },

    modalHeader: {
      style: { alignSelf: "center" },
    },

    modalHeaderText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    inputSearch: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    boxModal: {
      style: {
        margin: wp("5%"),
      },
    },

    iconSearch: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.muted[700],
        marginRight: verticalScale(10),
      },
    },

    flatlist: {
      style: {
        marginTop: hp("1%"),
        height: hp("40%"),
      },
    },

    boxButtons: {
      style: {
        position: "absolute",
        bottom: RFValue(0),
        alignItems: "center",
        marginBottom: RFValue(8),
        width: wp("100%"),
      },
    },

    buttonBottom: {
      style: {
        width: wp("45%"),
      },
    },

    mainBoxStagger: {
      style: {
        alignItems: "flex-end",
        marginRight: wp("3%"),
        marginBottom: hp("1%"),
      },
    },

    secondBoxStagger: {
      style: {
        alignItems: "flex-end",
      },
    },

    hStackStagger: {
      style: {
        alignItems: "flex-end",
        marginTop: verticalScale(8),
      },
    },

    iconButtonStagger: {
      size: moderateScale(40),
      style: {
        elevation: 4,
        borderRadius: moderateScale(999),
        backgroundColor: ThemesApp.getTheme().colors.secondary[600],
      },
    },

    iconPlusStagger: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
        fill: ThemesApp.getTheme().colors.white,
      },
    },

    textArea: {
      style: {
        fontSize: RFValue(18),
      },
    },

    textAreaError: {
      style: {
        justifyContent: "space-between",
      },
    },

    iconButtonClock: {
      style: {
        borderRadius: moderateScale(999),
      },
    },

    iconClock: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.lime[600],
      },
    },

    iconCheckSelect: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        marginRight: horizontalScale(3),
        fill: ThemesApp.getTheme().colors.green[600],
      },
    },

    inputTimePickerClock: {
      style: {
        paddingLeft: 5,
        paddingRight: 5,
      },
    },

    badgeCategory: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.fuchsia[900],
        borderRadius: moderateScale(5),
        marginRight: horizontalScale(3),
        marginBottom: horizontalScale(3),
        alignItems: "center",
      },
    },

    stackBadge: {
      flex: 1,
      flexWrap: "wrap",
      justifyContent: "flex-start",
      alignSelf: "flex-start",
    },

    closeBadgeIcon: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.fuchsia[100],
      },
    },

    warningOutlineIcon: {
      style: {
        width: moderateScale(13),
        height: moderateScale(13),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },

    formControlErrorMessage: {
      mt: 1,
    },

    formControlLabel: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },
  };
};

export default customStyles;
