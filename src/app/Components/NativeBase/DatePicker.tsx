import {format} from "date-fns";
import {Input} from "input-mask-native-base-rhf";
import {Box, IconButton} from "native-base";
import React, {memo, useCallback, useState} from "react";
import {useFormContext} from "react-hook-form";
import DatePicker from "react-native-date-picker";
import customStyles from "src/app/Components/NativeBase/styles";
import useTranslation from "src/app/Hooks/useTranslation";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import {Calendar} from "src/assets/Icons/Flaticon";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";

export interface TimePickerProps {
  name: string;
  label: string;
  errorMessage?: string;
}

const DatePickerCustom = memo(
  ({name, label, errorMessage}: TimePickerProps) => {
    const styles = customStyles();
    const {languageApp} = useGeneralSettings();
    const resources = useTranslation();
    const {
      setValue,
      watch,
      formState: {errors},
    } = useFormContext();

    const [date, setDate] = useState(new Date());
    const [show, setShow] = useState(false);

    const formatDate = useCallback(
      (dateParam: Date) => {
        return languageApp === LanguageOptions.EN
          ? format(dateParam, "MM/dd/yyyy")
          : format(dateParam, "dd/MM/yyyy");
      },
      [languageApp],
    );

    const value = watch(name) ? watch(name) : undefined;

    const onChange = (selectedDate: Date) => {
      setValue(name, selectedDate.toISOString());
      setShow(false);
    };

    return (
      <Box flex={1}>
        <Input
          {...styles.inputTimePickerClock}
          name={name}
          label={label}
          errorMessage={errorMessage || errors[name]?.message?.toString()}
          value={value && formatDate(value)}
          type="text"
          InputRightElement={
            <IconButton
              icon={<Calendar {...styles.iconClock.style} />}
              _pressed={{opacity: 0.5}}
              onPress={() => {
                setShow(true);
                setDate(value ? new Date(value) : new Date());
              }}
              {...styles.iconButtonClock}
            />
          }
          isReadOnly
          height={60}
          isRequired={true}
          size="md"
          borderRadius="lg"
          backgroundColor="gray.100"
          padding={3}
          fontSize="md"
          borderWidth= "2"
          _focus={{
            borderColor: "#95c11f",
            backgroundColor: "gray.200",
          }}
        />
        <DatePicker
          modal
          open={show}
          testID="dateTimePicker"
          date={date}
          mode="date"
          onConfirm={onChange}
          onCancel={() => {
            setShow(false);
          }}
          confirmText={resources.users.edit.components.datePicker.button.save}
          cancelText={resources.users.edit.components.datePicker.button.cancel}
          title={resources.users.edit.components.datePicker.text.dateSelection}
          locale={languageApp === LanguageOptions.PT ? "pt" : "en"}
          is24hourSource="locale"
        />
      </Box>
    );
  },
);

export default DatePickerCustom;
