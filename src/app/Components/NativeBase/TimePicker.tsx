import {Input} from "input-mask-native-base-rhf";
import moment from "moment";
import {Box, IconButton} from "native-base";
import React, {memo, useState} from "react";
import {useFormContext} from "react-hook-form";
import DatePicker from "react-native-date-picker";
import customStyles from "src/app/Components/NativeBase/styles";
import useTranslation from "src/app/Hooks/useTranslation";
import dateToString from "src/app/Utils/DateToString";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import {ClockOutline} from "src/assets/Icons/Flaticon";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";

export interface TimePickerProps {
  name: string;
  label: string;
  errorMessage?: string;
}

const TimePicker = ({name, label, errorMessage}: TimePickerProps) => {
  const styles = customStyles();
  const {languageApp} = useGeneralSettings();
  const resources = useTranslation();
  const {
    setValue,
    watch,
    formState: {errors},
  } = useFormContext();

  const {
    stores: {create: stores},
  } = resources;

  const [date, setDate] = useState(new Date());
  const [show, setShow] = useState(false);

  const value = watch(name) ? watch(name) : "00:00";

  const onChange = (selectedDate: any) => {
    const currentDate = selectedDate;
    setValue(name, dateToString(currentDate));
    setDate(currentDate);
    setShow(false);
  };

  return (
    <Box flex={1}>
      <Input
        {...styles.inputTimePickerClock}
        name={name}
        label={label}
        errorMessage={errorMessage || errors[name]?.message?.toString()}
        value={
          languageApp === LanguageOptions.EN
            ? moment(value, "HH:mm").format("hh:mm A")
            : moment(value, "HH:mm:ss").format("HH:mm")
        }
        type="text"
        InputRightElement={
          <IconButton
            icon={<ClockOutline {...styles.iconClock.style} />}
            _pressed={{opacity: 0.5}}
            onPress={() => setShow(true)}
            {...styles.iconButtonClock}
          />
        }
        isReadOnly
      />
      <DatePicker
        modal
        open={show}
        testID="dateTimePicker"
        date={date}
        mode="time"
        onConfirm={onChange}
        onCancel={() => {
          setShow(false);
        }}
        confirmText={stores.button.save}
        cancelText={stores.button.cancel}
        title={stores.text.timeSelection}
        locale={languageApp == LanguageOptions.PT ? "pt" : "en"}
        is24hourSource="locale"
      />
    </Box>
  );
};

export default memo(TimePicker);
