import { FormikErrors } from "formik";
import { FormControl, IRadioGroupProps, Radio } from "native-base";
import React from "react";
import customStyles from "src/app/Components/NativeBase/styles";
import { WarningOutline } from "src/assets/Icons/Flaticon";

interface RadioProps extends IRadioGroupProps {
  name: string;
  label: string;
  isRequired?: boolean;
  handleChange<T = string | React.ChangeEvent<any>>(
    field: T,
  ): T extends React.ChangeEvent<any> ? void : (e: string | React.ChangeEvent<any>) => void;
  radios: {
    key: string;
    value: string;
    label: string;
  }[];
  errors: FormikErrors<any>;
}

const RadioGroup = ({ name, label, isRequired = true, handleChange, radios, errors, value, ...rest }: RadioProps) => {
  const styles = customStyles();
  const error = name in errors ? errors[name] : undefined;
  return (
    <FormControl isRequired={isRequired} isInvalid={!!error}>
      <FormControl.Label>{label}</FormControl.Label>
      <Radio.Group name={name} defaultValue={value} onChange={handleChange(name)} {...rest}>
        {radios.map((item: { key: string; value: string; label: string }) => {
          return (
            <Radio key={item.key} value={item.value.toString()}>
              {item.label}
            </Radio>
          );
        })}
      </Radio.Group>
      {error ? (
        <FormControl.ErrorMessage leftIcon={<WarningOutline {...styles.warningOutlineIcon.style} />}>
          {error}
        </FormControl.ErrorMessage>
      ) : (
        <FormControl.HelperText>&nbsp;</FormControl.HelperText>
      )}
    </FormControl>
  );
};

export default RadioGroup;
