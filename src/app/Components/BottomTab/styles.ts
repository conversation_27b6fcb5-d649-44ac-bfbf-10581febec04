import {RFValue} from "react-native-responsive-fontsize";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      flex: 1,
      style: {
        backgroundColor: ThemesApp.getTheme().colors.background,
        justifyContent: "space-evenly",
        alignItems: "center",
        flexDirection: "row",
        position: "relative",
        bottom: verticalScale(0),
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
      },
    },

    vStackButtons: {
      justifyContent: "center",
      alignItems: "center",
    },

    boxButton: {
      w: "50%",
      h: "full",
      style: {
        justifyContent: "center",
        alignItems: "center",
      },
    },

    pressableContainer: {
      style: {
        justifyContent: "center",
        alignItems: "center",
      },
    },

    badge: {
      backgroundColor: ThemesApp.getTheme().colors.secondary[600],
      position: "absolute",
      rounded: "full",
      right: horizontalScale(5),
      top: verticalScale(0),
      zIndex: 1,
    },

    activeTabName: {
      fontSize: RFValue(8),
      lineHeight: RFValue(10),
      color: ThemesApp.getTheme().colors.primary[500],
      style: {
        textAlignVertical: "bottom",
      },
    },
    inactiveTabName: {
      fontSize: RFValue(8),
      lineHeight: RFValue(10),
      color: ThemesApp.getTheme().colors.gray[400],
      style: {
        textAlignVertical: "bottom",
      },
    },
    activeIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.primary[500],
      },
    },
    inactiveIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.gray[400],
      },
    },

    textBadge: {
      lineHeight: RFValue(14),
      fontWeight: "bold",
      color: "white",
    },
  };
};

export default customStyles;
