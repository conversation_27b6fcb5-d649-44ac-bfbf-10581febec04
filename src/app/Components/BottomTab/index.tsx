import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {Badge, HStack, Pressable, Text, VStack} from "native-base";
import React, {useEffect, useMemo, useState} from "react";
import {Keyboard} from "react-native";
import {RFValue} from "react-native-responsive-fontsize";
import EBottomTabItems from "src/app/Components/BottomTab/EBottomTabItems";
import customStyles from "src/app/Components/BottomTab/styles";
import {TabItems} from "src/app/Components/BottomTab/types";
import useTranslation from "src/app/Hooks/useTranslation";
import AddProfileService from "src/app/Modules/Main/Settings/Presentation/AddProfile/AddProfileService";
import useGetUserQuantityNotification from "src/app/Modules/Main/Users/<USER>/useGetUserQuantityNotification";
import {RootStackParamList} from "src/app/routes";
import navigateHome from "src/app/Utils/NavigateHome";
import useBottomTabs from "src/app/Zustand/Store/useBottomTabs";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useOrder from "src/app/Zustand/Store/useOrder";
import {
  Home,
  HomeOutline,
  Notification,
  NotificationOutline,
  Receipt,
  ReceiptOutline,
  Search,
  SearchFull,
  Settings,
  SettingsOutline,
  ShoppingCart,
  ShoppingCartOutline,
  Store,
  StoreOutline,
} from "src/assets/Icons/Flaticon";
import {SalesIcon} from "src/assets/Icons/Flaticon/Simple/SalesIcon";
import {SalesOutlineIcon} from "src/assets/Icons/Flaticon/Simple/SalesIconOutline";
import {EProfile} from "src/business/Models/Profile";

type Props = NativeStackScreenProps<RootStackParamList, "MainApp">;

const BottomTab: React.FC<Props> = ({navigation}) => {
  const styles = customStyles();
  const {selectedProfile} = useGeneralSettings();
  const {tabsStack, pushTabsStack} = useBottomTabs();
  const activeTab = tabsStack[tabsStack.length - 1];
  const [keyboardIsVisible, setKeyboardIsVisible] = useState(false);
  const {cartList} = useOrder();
  const {routes: resources} = useTranslation();

  // const userQuantityNotificationQuery =
  //   useGetUserQuantityNotification(selectedProfile);

  const quantityItems = cartList?.reduce(
    (accumulator, currentValue) =>
      accumulator + currentValue.orderItem.quantity,
    0,
  );

  const {handleUserProfilesData} = AddProfileService();

  useEffect(() => {
    const showListener = Keyboard.addListener("keyboardDidShow", () => {
      setKeyboardIsVisible(true);
    });
    const hideListener = Keyboard.addListener("keyboardDidHide", () => {
      setKeyboardIsVisible(false);
      Keyboard.dismiss();
    });

    return () => {
      showListener.remove();
      hideListener.remove();
    };
  }, []);

  const tabItems = useMemo<TabItems[]>(
    () => [
      {
        identifier: EBottomTabItems.HOME,
        icon: Home,
        iconOutline: HomeOutline,
        onPress: () => {
          navigateHome();
          pushTabsStack(EBottomTabItems.HOME);
        },
        shouldRender: true,
        resources: resources.DrawerHome,
      },
      {
        identifier: EBottomTabItems.ORDERS,
        icon: Receipt,
        iconOutline: ReceiptOutline,
        onPress: () => {
          navigation.navigate("MainApp", {
            screen: "OrdersHome",
          });
          pushTabsStack(EBottomTabItems.ORDERS);
        },
        shouldRender:
          selectedProfile === EProfile.client || selectedProfile === undefined,
        resources: resources.Orders,
      },
      {
        identifier: EBottomTabItems.PRODUCTS,
        icon: SearchFull,
        iconOutline: Search,
        onPress: () => {
          navigation.navigate("MainApp", {
            screen: "ProductSearch",
          });
          pushTabsStack(EBottomTabItems.PRODUCTS);
        },
        shouldRender:
          selectedProfile === EProfile.client || selectedProfile === undefined,
        resources: resources.ProductHome,
      },
      {
        identifier: EBottomTabItems.STORES,
        icon: Store,
        iconOutline: StoreOutline,
        onPress: () => {
          navigation.navigate("MainApp", {
            screen: "StoreHome",
          });
          pushTabsStack(EBottomTabItems.STORES);
        },
        shouldRender: selectedProfile === EProfile.shopkeeper,
        resources: resources.StoreHome,
      },
      // {
      //   identifier: EBottomTabItems.CART,
      //   icon: ShoppingCart,
      //   iconOutline: ShoppingCartOutline,
      //   onPress: () => {
      //     if (activeTab !== EBottomTabItems.CART) {
      //       navigation.push("Cart" as any); // Push Cart direcly to not reset the stack
      //       pushTabsStack(EBottomTabItems.CART);
      //     }
      //   },
      //   shouldRender:
      //     selectedProfile === EProfile.client || selectedProfile === undefined,
      //   resources: resources.Cart,
      //   badge: {
      //     value: quantityItems && quantityItems > 0 ? quantityItems : 0,
      //     variant: "solid",
      //     _text: {
      //       fontSize: RFValue(9),
      //     },
      //   },
      // },
      {
        identifier: EBottomTabItems.SALES,
        icon: SalesIcon,
        iconOutline: SalesOutlineIcon,
        onPress: () => {
          navigation.navigate("MainApp", {
            screen: "SalesHome",
          });
          pushTabsStack(EBottomTabItems.SALES);
        },
        shouldRender:
          selectedProfile === EProfile.deliveryman ||
          selectedProfile === EProfile.shopkeeper,
        resources: resources.SalesHome,
      },
      // {
      //   identifier: EBottomTabItems.NOTIFICATION,
      //   icon: Notification,
      //   iconOutline: NotificationOutline,
      //   onPress: () => {
      //     navigation.navigate("MainApp", {
      //       screen: "UserNotifications",
      //     });
      //     pushTabsStack(EBottomTabItems.NOTIFICATION);
      //   },
      //   shouldRender: true,
      //   resources: resources.UserNotifications,
      //   badge: {
      //     value: userQuantityNotificationQuery.data || 0,
      //     variant: "solid",
      //     _text: {
      //       fontSize: RFValue(9),
      //     },
      //   },
      // },
      {
        identifier: EBottomTabItems.SETTINGS,
        icon: Settings,
        iconOutline: SettingsOutline,
        onPress: () => {
          navigation.navigate("MainApp", {
            screen: "SettingsHome",
          });
          pushTabsStack(EBottomTabItems.SETTINGS);
          handleUserProfilesData();
        },
        shouldRender: true,
        resources: resources.Settings,
      },
    ],
    [
      activeTab,
      handleUserProfilesData,
      navigation,
      pushTabsStack,
      quantityItems,
      // resources.Cart,
      resources.DrawerHome,
      resources.Orders,
      resources.ProductHome,
      resources.SalesHome,
      resources.Settings,
      resources.StoreHome,
      // resources.UserNotifications,
      selectedProfile,
      // userQuantityNotificationQuery.data,
    ],
  );

  return !keyboardIsVisible ? (
    <HStack {...styles.container}>
      {tabItems.map(item => {
        if (item.shouldRender) {
          return (
            <VStack key={item.resources} {...styles.vStackButtons}>
              <HStack {...styles.boxButton}>
                {item.badge && item.badge.value > 0 && (
                  <Badge
                    {...styles.badge}
                    variant={item.badge.variant}
                    _text={{
                      fontSize: item.badge._text.fontSize,
                      ...styles.textBadge,
                    }}>
                    {item.badge.value}
                  </Badge>
                )}
                <Pressable
                  _pressed={{opacity: 0.5}}
                  onPress={item.onPress}
                  {...styles.pressableContainer}>
                  <VStack space={1} {...styles.pressableContainer}>
                    {item.identifier === activeTab ? (
                      <>
                        <item.icon {...styles.activeIcon.style} />
                        <Text {...styles.activeTabName}>{item.resources}</Text>
                      </>
                    ) : (
                      <>
                        <item.iconOutline {...styles.inactiveIcon.style} />
                        <Text {...styles.inactiveTabName}>
                          {item.resources}
                        </Text>
                      </>
                    )}
                  </VStack>
                </Pressable>
              </HStack>
            </VStack>
          );
        }

        return null;
      })}
    </HStack>
  ) : null;
};

export default BottomTab;
