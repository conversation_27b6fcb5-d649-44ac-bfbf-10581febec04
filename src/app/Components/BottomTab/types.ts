import { SvgProps } from "react-native-svg";
import EBottomTabItems from "src/app/Components/BottomTab/EBottomTabItems";

export interface TabItems {
  identifier: EBottomTabItems;
  icon: (props: SvgProps) => JSX.Element;
  iconOutline: (props: SvgProps) => JSX.Element;
  onPress: () => void;
  resources: string;
  shouldRender: boolean;
  badge?: {
    value: number;
    variant: string;
    _text: {
      fontSize: number;
    };
  };
}
