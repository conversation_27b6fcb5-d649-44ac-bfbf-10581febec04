import { KeyboardAvoidingView, Modal } from "native-base";
import React from "react";
import { Platform } from "react-native";

import customStyles from "src/app/Components/ModalSimple/styles";
import ModalContent from "src/app/Components/ModalSimple/Components/ModalContent";

type Props = {
  children: React.ReactNode;
  visibility: boolean;
  onClose: () => void;
  height?: string;
  titleHeader?: string;
  removeCloseButton?: boolean;
  useKeyboard?: boolean;
  closeOnOverlayClick?: boolean;
};

const ModalSimple = ({
  children,
  visibility,
  onClose,
  titleHeader,
  removeCloseButton = false,
  height,
  useKeyboard,
  closeOnOverlayClick = true,
}: Props) => {
  const styles = customStyles();
  return (
    <Modal {...styles.modal} isOpen={visibility} onClose={onClose} closeOnOverlayClick={closeOnOverlayClick}>
      {useKeyboard ? (
        <KeyboardAvoidingView w="full" behavior={Platform.OS === "ios" ? "padding" : "position"}>
          <ModalContent
            onClose={onClose}
            titleHeader={titleHeader}
            height={height}
            removeCloseButton={removeCloseButton}
          >
            {children}
          </ModalContent>
        </KeyboardAvoidingView>
      ) : (
        <ModalContent onClose={onClose} titleHeader={titleHeader} height={height} removeCloseButton={removeCloseButton}>
          {children}
        </ModalContent>
      )}
    </Modal>
  );
};

export default ModalSimple;
