import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    modalContent: {
      borderTopRadius: moderateScale(15),
      style: {
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
      },
    },
    modalCloseButton: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
      },
    },
    modalHeader: {
      style: {
        alignSelf: "center",
      },
    },
    modalHeaderText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },
  };
};

export default customStyles;
