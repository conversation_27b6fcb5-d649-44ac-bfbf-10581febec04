import { Modal } from "native-base";
import React from "react";

import { Close } from "src/assets/Icons/Flaticon";
import customStyles from "src/app/Components/ModalSimple/Components/ModalContent/styles";

type Props = {
  children: React.ReactNode;

  onClose: () => void;
  titleHeader?: string;
  removeCloseButton?: boolean;
  height?: string;
};

const ModalContent = ({ children, onClose, titleHeader, removeCloseButton = false, height }: Props) => {
  const styles = customStyles();
  return (
    <Modal.Content {...styles.modalContent} h={height}>
      {removeCloseButton ? null : (
        <Modal.CloseButton icon={<Close />} _icon={{ style: styles.modalCloseButton.style }} onPress={onClose} />
      )}

      <Modal.Header _text={styles.modalHeaderText} {...styles.modalHeader}>
        {titleHeader}
      </Modal.Header>
      {children}
    </Modal.Content>
  );
};

export default ModalContent;
