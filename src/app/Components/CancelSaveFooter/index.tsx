import React from "react";
import {<PERSON>, Button} from "native-base";
import customStyles from "src/app/Components/CancelSaveFooter/styles";

interface CancelSaveFooterProps {
  cancelText: string;
  saveText: string;
  onCancel: () => void;
  onSave: () => void;
  isLoading?: boolean;
}

const CancelSaveFooter = ({
  cancelText,
  saveText,
  onCancel,
  onSave,
  isLoading,
}: CancelSaveFooterProps) => {
  const styles = customStyles();
  return (
    <Box {...styles.boxButtons}>
      <Button.Group space={4}>
        <Button variant="outline" onPress={onCancel} {...styles.buttonBottom}>
          {cancelText}
        </Button>
        <Button onPress={onSave} {...styles.buttonBottom} isLoading={isLoading}>
          {saveText}
        </Button>
      </Button.Group>
    </Box>
  );
};

export default CancelSaveFooter;
