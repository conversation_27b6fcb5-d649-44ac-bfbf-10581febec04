import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxButtons: {
      style: {
        position: "absolute",
        bottom: 0,
        alignItems: "center",
        marginBottom: verticalScale(8),
        width: wp("100%"),
        backgroundColor: ThemesApp.getTheme().colors.background,
      },
    },

    buttonBottom: {
      style: {
        width: wp("45%"),
      },
    },
  };
};

export default customStyles;
