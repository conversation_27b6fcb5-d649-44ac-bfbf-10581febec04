import { Box, Spinner } from "native-base";
import React from "react";
import customStyles from "src/app/Components/styles";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const ModalProgress = () => {
  const styles = customStyles();
  return (
    <Box {...styles.mainBox}>
      <Spinner size="sm" color={ThemesApp.getTheme().colors.spinner} />
    </Box>
  );
};
export default ModalProgress;
