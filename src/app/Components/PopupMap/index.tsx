import React from "react";
import { Popup } from "react-native-map-link";
import { styles } from "src/app/Components/PopupMap/styles";
import useTranslation from "src/app/Hooks/useTranslation";

type Props = {
  visibility: boolean,
  setIsVisible: (visibility: boolean) => void,
  onCancelPressed: () => void,
  onAppPressed: () => void,
  latitude?: string | number,
  longitude?: string | number
}

const PopupMap = ({ latitude, longitude, visibility, setIsVisible, onAppPressed, onCancelPressed }: Props) => {

  const {
    popupMap: resources
  } = useTranslation();

  return (

    <Popup
      isVisible={visibility}
      setIsVisible={(v) => setIsVisible(v)}
      onCancelPressed={onCancelPressed}
      onAppPressed={onAppPressed}
      modalProps={{
        animationType: "slide",
      }}
      style={styles}
      options={{
        latitude,
        longitude,
        googleForceLatLon: false,
        alwaysIncludeGoogle: true,
        dialogTitle: resources.text.title,
        cancelText: resources.button.cancel,
        dialogMessage: resources.text.message,
        directionsMode: "car",

      }}
    />

  );

};

export default PopupMap;
