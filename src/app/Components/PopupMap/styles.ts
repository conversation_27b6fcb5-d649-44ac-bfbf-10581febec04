/* eslint-disable import/prefer-default-export */
import {ImageStyle, TextStyle, ViewStyle} from "react-native/types";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

export const styles: {
  container?: ViewStyle;
  modalView?: ViewStyle;
  itemContainer?: ViewStyle;
  image?: ImageStyle;
  itemText?: TextStyle;
  headerContainer?: ViewStyle;
  titleText?: TextStyle;
  subtitleText?: TextStyle;
  cancelButtonContainer?: ViewStyle;
  cancelButtonText?: TextStyle;
  separatorStyle?: ViewStyle;
  activityIndicatorContainer?: ViewStyle;
} = {
  titleText: {
    fontSize: 20,
  },
  subtitleText: {
    fontSize: 16,
    color: "#95a5a6",
  },
  cancelButtonText: {
    color: ThemesApp.getTheme().colors.white,
    fontSize: 16,
    fontWeight: "600",
  },
  cancelButtonContainer: {
    backgroundColor: ThemesApp.getTheme().colors.error,
    width: wp("22%"),
    height: hp("4%"),
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
  },
  separatorStyle: {},
  itemContainer: {},
  container: {},
  modalView: {},
  itemText: {},
  activityIndicatorContainer: {},
  headerContainer: {},
};
