import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    buttonGroup: {
      style: {
        justifyContent: "flex-end",
        width: wp("100%"),
      },
    },

    btnPositive: {
      style: {
        width: wp("40%"),
      },
    },

    btnNegative: {
      style: {
        width: wp("40%"),
        backgroundColor: ThemesApp.getTheme().colors.secondary[400],
      },
    },
  };
};

export default customStyles;
