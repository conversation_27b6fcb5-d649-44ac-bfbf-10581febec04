/* eslint-disable import/prefer-default-export */
import React from "react";
import InAppReview from "react-native-in-app-review";
import { Button } from "native-base";
import customStyles from "src/app/Components/AppReviewModal/styles";
import useTranslation from "src/app/Hooks/useTranslation";
import ModalRN from "src/app/Components/Modal";
import { rootNavigation } from "src/app/Utils/RootNavigation";

interface Props {
  visibility: boolean;
  onClose: () => void;
}

export const AppReviewModal = ({ onClose, visibility }: Props) => {
  const styles = customStyles();
  const { modalReview: resources } = useTranslation();

  const handleInAppReview = async () => {
    try {
      const hasFlowFinishedSuccessfully =
        await InAppReview.RequestInAppReview();

    } catch (error) {
      console.log("InAppReviewError:", error);
    }

    onClose();
  };

  return (
    <ModalRN
      title={resources.text.title}
      visibility={visibility}
      onClose={onClose}
      componentFooter={
        <Button.Group direction="row" {...styles.buttonGroup}>
          <Button onPress={onClose} {...styles.btnNegative}>
            {resources.button.cancel}
          </Button>
          <Button onPress={handleInAppReview} {...styles.btnPositive}>
            {resources.button.review}
          </Button>
        </Button.Group>
      }
    >
      {resources.text.content}
    </ModalRN>
  );
};
