import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainBox: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        width: wp("100%"),
        marginVertical: wp("4%"),
      },
    },
    stack: {
      style: {
        alignItems: "center",
        width: wp("60%"),
      },
    },
    text: {
      style: {
        fontWeight: "bold",
      },
    },
    title: {
      style: {
        fontWeight: "bold",
        fontSize: RFValue(18),
        lineHeight: RFValue(20),
        marginTop: hp("2%"),
        marginBottom: hp("1%"),
      },
    },
    helpIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.warning[600],
        width: moderateScale(16),
        height: moderateScale(16),
      },
    },
    titleStack: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },
  };
};

export default customStyles;
