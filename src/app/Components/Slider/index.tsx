import { Mask, maskField } from "input-mask-native-base-rhf";
import { Text, HStack, Stack, VStack } from "native-base";
import React, { useState } from "react";
import customStyles from "src/app/Components/Slider/styles";
import Slider from "@react-native-community/slider";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { HelpCircle, HelpCircleOutline } from "src/assets/Icons/Flaticon";

interface ISliderProps {
  minValue: number;
  maxValue: number;
  onSave: (currentValue: number) => void;
  mask?: Mask;
  suffix?: string;
  initialValue?: number;
}

const SliderNB: React.FC<ISliderProps> = (props) => {
  const { maxValue, minValue, onSave, mask, suffix, initialValue } = props;
  const [selectedValue, setSelectedValue] = useState(initialValue || maxValue);
  const styles = customStyles();
  return (
    <VStack {...styles.mainBox}>
      <HStack {...styles.mainBox} space={4}>
        <Text {...styles.text}>
          {mask ? maskField({ mask, value: minValue }).masked : minValue} {suffix}
        </Text>
        <Stack space={4} {...styles.stack}>
          <Slider
            {...styles.stack}
            minimumValue={minValue}
            maximumValue={maxValue}
            minimumTrackTintColor={ThemesApp.getTheme().colors.primary[600]}
            maximumTrackTintColor={ThemesApp.getTheme().colors.black}
            onValueChange={(value) => {
              if (value) {
                setSelectedValue(Math.floor(value));
                onSave(Math.floor(value));
              }
            }}
            value={selectedValue}
            thumbTintColor={ThemesApp.getTheme().colors.primary[600]}
          />
        </Stack>
        <Text {...styles.text}>
          {mask ? maskField({ mask, value: maxValue }).masked : maxValue} {suffix}
        </Text>
      </HStack>
    </VStack>
  );
};

export default SliderNB;
