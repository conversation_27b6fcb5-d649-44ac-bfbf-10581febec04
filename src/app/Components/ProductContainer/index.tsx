import {useQueryClient} from "@tanstack/react-query";
import {Box, HStack, Image, Pressable} from "native-base";
import React, {useEffect, useState} from "react";
import ModalItensCart from "src/app/Components/ModalItensCart";
import BoxInfo from "src/app/Components/ProductContainer/Components/BoxInfo";
import customStyles from "src/app/Components/ProductContainer/style";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useOrder from "src/app/Zustand/Store/useOrder";
import {ProductPhoto} from "src/assets/Icons/Flaticon";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {IProductByStore} from "src/business/DTOs/Product/ProductByStore";
import EFileType from "src/business/Enums/Models/EFileType";
import {OrderItem} from "src/business/Models/OrderItem";

type Props = {
  product: IProductByStore;
  storeReview?: number;
};

const ProductContainer: React.FC<Props> = ({product, storeReview}) => {
  const styles = customStyles();
  const [favorite, setFavorite] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const {cartList, eraseStoreOrders} = useOrder();
  const queryClient = useQueryClient();

  useEffect(() => {
    product?.isFavorite && setFavorite(true);
  }, [product]);

  const handleOpenOrderScreen = () => {
    eraseStoreOrder();

    // Invalidate store availability query before navigate to order screen
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.GET_STORE_AVAILABILITY, product.storeId],
    });

    if (storeReview) {
      rootNavigation("MainApp", {
        screen: "OrdersCreate",
        params: {
          id: product.id,
          data: {
            orderItem: {} as OrderItem,
            orderProduct: product,
          },
          storeReview,
        },
      });
    } else {
      rootNavigation("MainApp", {
        screen: "OrdersCreate",
        params: {
          id: product.id,
          data: {
            orderItem: {} as OrderItem,
            orderProduct: product,
          },
        },
      });
    }
  };

  const isProductDifferentInCart = () => {
    if (
      cartList &&
      cartList.findIndex(
        item => item.orderProduct.storeId !== product.storeId,
      ) >= 0
    ) {
      return true;
    }
    return false;
  };

  const eraseStoreOrder = () => {
    isProductDifferentInCart() && eraseStoreOrders();
  };

  const handleConfirm = () => {
    eraseStoreOrder();
    handleOpenOrderScreen();
  };
  const productIcon =
    product.files?.find(file => file.type === EFileType.photo)?.url ||
    undefined;

  return (
    <>
      <Pressable
        _pressed={{opacity: 0.5}}
        {...styles.pressableContainer}
        onPress={() => {
          isProductDifferentInCart()
            ? setVisible(true)
            : handleOpenOrderScreen();
        }}>
        <Box {...styles.mainHStack}>
          <HStack>
            {productIcon ? (
              <Image
                source={{
                  uri: productIcon,
                }}
                alt="Product food"
                style={styles.imageStore.style}
              />
            ) : (
              <Box {...styles.avatarIconBox}>
                <ProductPhoto {...styles.avatarIcon.style} />
              </Box>
            )}

            <BoxInfo
              product={product}
              favorite={favorite}
              setFavorite={setFavorite}
            />
          </HStack>
        </Box>
      </Pressable>
      <ModalItensCart
        visibility={visible}
        onClose={() => setVisible(!visible)}
        onConfirm={handleConfirm}
      />
    </>
  );
};

export default ProductContainer;
