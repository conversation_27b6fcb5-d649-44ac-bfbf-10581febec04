import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    pressableContainer: {
      style: {
      
        elevation: 1,
        borderRadius: moderateScale(16),
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        padding: wp("2%"),
        height: 165,
      },
    },

    mainHStack: {
      style: {
        alignItems: "center",
        flexDirection: "row",
        overflow: "hidden",
        flex: 1,
      },
    },

    imageStore: {
      style: {
        borderTopLeftRadius: moderateScale(10),
        borderBottomLeftRadius: moderateScale(10),
        overflow: "hidden",
        height: hp("15%"),
        width: wp("20%"),
      },
    },

    vStackStore: {
      style: {
        marginLeft: wp("3%"),
        marginRight: wp("1%"),
        flex: 1,
      },
    },

    hStackProductHeader: {
      style: {
        justifyContent: "space-between",
      },
    },

    hStackTitleHeader: {
      style: {
        alignItems: "center",
        flex: 1,
      },
    },

    textTitle: {
      style: {
        flexWrap: "wrap",
        fontWeight: "bold",
        textTransform: "uppercase",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    textMenu: {
      color: ThemesApp.getTheme().colors.deleteAccountText,
    },

    hStackProductDetails: {
      style: {
        alignItems: "center",
        overflow: "hidden",
      },
    },

    textDetails: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        textTransform: "uppercase",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    hStackDescription: {
      style: {
        marginVertical: hp("1%"),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    textDescription: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        textAlign: "left",
        flex: 1,
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    infoIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.iconHelp,
      },
    },

    textPrice: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontWeight: "bold",
        textTransform: "capitalize",
        fontSize: RFValue(14),
      },
    },

    heartIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },

    textModalHeader: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        textAlign: "justify",
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    textModalBody: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        textAlign: "justify",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    buttonGroup: {
      w: "full",
      style: {
        justifyContent: "flex-end",
      },
    },

    btnPositive: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.error,
      },
    },

    btnNegative: {
      style: {
        width: wp("30%"),
      },
    },

    productFooterHStack: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },

    VStackDescription: {
      style: {
        justifyContent: "space-between",
        flexGrow: 1,
      },
    },

    hStackPrepTime: {
      alignItems: "center",
    },

    textPrepTime: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    timerIcon: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.muted[500],
      },
    },

    avatarIcon: {
      style: {
        height: moderateScale(50),
        width: moderateScale(50),
        fill: ThemesApp.getTheme().colors.muted[400],
      },
    },
    avatarIconBox: {
      style: {
        borderTopLeftRadius: moderateScale(10),
        borderBottomLeftRadius: moderateScale(10),
        overflow: "hidden",
        height: hp("21%"),
        width: wp("20%"),
        backgroundColor: ThemesApp.getTheme().colors.muted[200],
        alignItems: "center",
        justifyContent: "center",
      },
    },

    pressableInfo: {
      ml: 1,
    },

    salePriceText: {
      style: {
        color: ThemesApp.getTheme().colors.iconContainerOwnProfile,
        flexWrap: "wrap",
        fontWeight: "bold",
        textTransform: "uppercase",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },

    oldPriceText: {
      textDecorationLine: "line-through",
      style: {
        color: ThemesApp.getTheme().colors.accordionStatusText,
        fontSize: RFValue(10),
        lineHeight: RFValue(14),
        textDecorationStyle: "solid",
      },
    },
  };
};

export default customStyles;
