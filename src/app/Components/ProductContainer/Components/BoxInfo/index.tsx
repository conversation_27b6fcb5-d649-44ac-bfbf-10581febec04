import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {HStack, Pressable, Text, VStack} from "native-base";
import React from "react";
import customStyles from "src/app/Components/ProductContainer/Components/BoxInfo/style";
import formatPrepTime from "src/app/Modules/Main/Product/Utils/formatPrepTime";
import useDeleteFavoriteProduct from "src/app/Modules/Main/Users/<USER>/useDeleteFavoriteProduct";
import useRelateFavoriteProduct from "src/app/Modules/Main/Users/<USER>/useRelateFavoriteProduct";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {Heart, HeartOutline, Info, Timer} from "src/assets/Icons/Flaticon";
import {IProductByStore} from "src/business/DTOs/Product/ProductByStore";

type Props = {
  product: IProductByStore;
  favorite: boolean;
  setFavorite: (value: React.SetStateAction<boolean>) => void;
};

const BoxInfo = ({product, favorite, setFavorite}: Props) => {
  const styles = customStyles();

  const useRelateFavoriteProductMutation = useRelateFavoriteProduct();
  const useDeleteFavoriteProductMutation = useDeleteFavoriteProduct();

  const handleFavorite = () => {
    setFavorite(!favorite);

    // TODO Handle error
    if (!favorite) {
      useRelateFavoriteProductMutation.mutate(product.id);
    } else {
      useDeleteFavoriteProductMutation.mutate(product.id);
    }
  };

  const navigateToProductDetails = () => {
    rootNavigation("MainApp", {
      screen: "ProductDetails",
      params: {id: product.id},
    });
  };

  return (
    <VStack space={1} {...styles.vStackStore}>
      <HStack {...styles.hStackProductHeader}>
        <HStack {...styles.hStackTitleHeader}>
          <Text ellipsizeMode="tail" numberOfLines={2} {...styles.textTitle}>
            {product.name}
          </Text>
        </HStack>
        <Pressable onPress={navigateToProductDetails} {...styles.pressableInfo}>
          <Info {...styles.infoIcon.style} />
        </Pressable>
      </HStack>
      <VStack space={1} {...styles.VStackDescription}>
        <HStack {...styles.hStackDescription}>
          <Text
            ellipsizeMode="tail"
            numberOfLines={2}
            {...styles.textDescription}>
            {product.shortDescription}
          </Text>
        </HStack>

        <HStack {...styles.productFooterHStack}>
          {product.salePrice < product.price ? (
            <HStack space={1}>
              <Text {...styles.salePriceText}>
                {
                  maskField({
                    value: product.salePrice,
                    mask: BRL_CURRENCY_DECIMAL(12),
                  }).masked
                }
              </Text>
              <Text {...styles.oldPriceText}>
                {
                  maskField({
                    value: product.price,
                    mask: BRL_CURRENCY_DECIMAL(12),
                  }).masked
                }
              </Text>
            </HStack>
          ) : (
            <Text {...styles.textPrice}>
              {
                maskField({
                  value: product.salePrice,
                  mask: BRL_CURRENCY_DECIMAL(12),
                }).masked
              }
            </Text>
          )}
          {product.preparationTime && product.preparationTime > 0 ? (
            <HStack space={1} {...styles.hStackPrepTime}>
              <Timer {...styles.timerIcon.style} />
              <Text {...styles.textPrepTime}>
                {formatPrepTime(product.preparationTime)}
              </Text>
            </HStack>
          ) : null}

          <Pressable onPress={() => handleFavorite()}>
            {favorite ? (
              <Heart {...styles.heartIcon.style} />
            ) : (
              <HeartOutline {...styles.heartIcon.style} />
            )}
          </Pressable>
        </HStack>
      </VStack>
    </VStack>
  );
};

export default BoxInfo;
