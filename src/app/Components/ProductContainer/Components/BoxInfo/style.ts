import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    vStackStore: {
      style: {
        height: 110,
        marginLeft: wp("3%"),
        marginRight: wp("3%"),
        flex: 1,
      },
    },
    hStackProductHeader: {
      style: {
        justifyContent: "space-between",
      },
    },
    hStackTitleHeader: {
      style: {
        alignItems: "center",
        flex: 1,
      },
    },
    textTitle: {
      style: {
        flexWrap: "wrap",
        fontWeight: "bold",
        textTransform: "uppercase",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
        marginTop: wp("6%"),
      },
    },
    pressableInfo: {
      ml: 1,
    },
    infoIcon: {
      style: {
        marginTop: wp("6%"),
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.iconHelp,
      },
    },
    VStackDescription: {
      style: {
        justifyContent: "space-between",
        flexGrow: 1,
      },
    },
    hStackPrepTime: {
      alignItems: "center",
    },
    textPrepTime: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    timerIcon: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.muted[500],
      },
    },
    hStackDescription: {
      style: {
        marginVertical: hp("1%"),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    textDescription: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        textAlign: "left",
        flex: 1,
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    productFooterHStack: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    salePriceText: {
      style: {
        color: ThemesApp.getTheme().colors.iconContainerOwnProfile,
        flexWrap: "wrap",
        fontWeight: "bold",
        textTransform: "uppercase",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },
    oldPriceText: {
      textDecorationLine: "line-through",
      style: {
        color: ThemesApp.getTheme().colors.accordionStatusText,
        fontSize: RFValue(10),
        lineHeight: RFValue(14),
        textDecorationStyle: "solid",
      },
    },
    textPrice: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontWeight: "bold",
        textTransform: "capitalize",
        fontSize: RFValue(14),
      },
    },
    heartIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },
  };
};

export default customStyles;
