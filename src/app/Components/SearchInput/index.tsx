import React from "react";
import { Button, Input, IInputProps } from "native-base";
import { Plus, Search } from "src/assets/Icons/Flaticon";
import customStyles from "src/app/Components/SearchInput/styles";

interface SearchAttributeInputProps extends IInputProps {
  onCreatePress: () => void;
}

const SearchInput = ({ onCreatePress, ...rest }: SearchAttributeInputProps) => {
  const styles = customStyles();
  return (
    <Input
      {...rest}
      InputRightElement={
        <Button onPress={onCreatePress} {...styles.rightInputButton}>
          <Plus {...styles.iconWhite.style} />
        </Button>
      }
      InputLeftElement={
        <Button {...styles.leftInputButton}>
          <Search {...styles.iconWhite.style} />
        </Button>
      }
    />

  );
};

export default SearchInput;
