import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      flex: 1,
    },
    iconWhite: {
      style: {
        fill: ThemesApp.getTheme().colors.gray[100],
      },
    },
    rightInputButton: {
      roundedBottomLeft: "none",
      roundedTopLeft: "none",
      h: "full",
      
    },
    leftInputButton: {
      roundedBottomRight: "none",
      roundedTopRight: "none",
      h: "full",
    },
  };
};

export default customStyles;
