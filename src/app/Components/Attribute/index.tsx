import {Box, HStack, Pressable, Text} from "native-base";
import React from "react";
import {Checkbox, HelpCircleOutline, Square} from "src/assets/Icons/Flaticon";
import {AttributeList} from "src/business/DTOs/AttributeList";
import useAttribute from "src/app/Components/Attribute/useAttribute";

type Props = {
  data: AttributeList;
  hasCheckBox?: boolean;
  onPress: (attribute: AttributeList) => void;
};

const AttributeProduct = ({data, hasCheckBox = false, onPress}: Props) => {
  const {handleHelpAttribute, styles} = useAttribute();

  return (
    <Box {...styles.boxSelect}>
      <HStack space={2} {...styles.mainStack}>
        <Pressable
          onPress={() => onPress(data)}
          {...styles.pressable}
          _pressed={{opacity: 0.5}}>
          <HStack space={2} {...styles.hStack}>
            {hasCheckBox ? (
              data.checked ? (
                <Checkbox {...styles.checkbox.style} />
              ) : (
                <Square {...styles.square.style} />
              )
            ) : null}
            <Text ellipsizeMode="tail" numberOfLines={2} {...styles.textOption}>
              {data.name}
            </Text>
          </HStack>
        </Pressable>
        <HelpCircleOutline
          onPress={() => handleHelpAttribute(data.shortDescription)}
          {...styles.iconHelp.style}
        />
      </HStack>
    </Box>
  );
};

export default AttributeProduct;
