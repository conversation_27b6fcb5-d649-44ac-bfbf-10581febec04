import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxSelect: {
      style: {
        marginLeft: horizontalScale(1),
        flex: 1,
      },
    },

    pressable: {
      style: {
        height: verticalScale(48),
        justifyContent: "space-evenly",
      },
    },

    mainStack: {
      alignItems: "center",
    },

    hStack: {
      style: {
        justifyContent: "flex-start",
        alignItems: "center",
        marginLeft: horizontalScale(10),
      },
    },

    textOption: {
      style: {
        color: ThemesApp.getTheme().colors.gray[500],
        flexWrap: "wrap",
        fontSize: RFValue(14),
        lineHeight: RFValue(18),
        fontWeight: "bold",
      },
    },

    iconCheckSelect: {
      size: moderateScale(20),
      style: {
        marginRight: horizontalScale(3),
      },
    },

    iconHelp: {
      style: {
        width: moderateScale(17),
        height: moderateScale(17),
        fill: ThemesApp.getTheme().colors.iconHelp,
      },
    },

    checkbox: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.checkedIcon,
      },
    },

    square: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.squareIcon,
      },
    },
  };
};

export default customStyles;
