import {RFValue} from "react-native-responsive-fontsize";
import {heightPercentageToDP as hp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    menu: {
      py: 0,
    },
    container: {
      style: {},
    },

    menuOptionGroup: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        textAlign: "center",
      },
    },

    menuItemOption: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    iconButton: {
      style: {
        borderRadius: RFValue(999),
      },
    },

    icon: {
      style: {
        width: RFValue(20),
        height: RFValue(20),
        fill: ThemesApp.getTheme().colors.primary[500],
      },
    },

    iconCheck: {
      size: moderateScale(16),
    },
  };
};

export default customStyles;
