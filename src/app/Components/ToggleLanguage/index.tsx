/* eslint-disable react/no-unstable-nested-components */
import {Box, IconButton, Menu} from "native-base";
import React, {useEffect, useState} from "react";
import Config from "react-native-config";
import customStyles from "src/app/Components/ToggleLanguage/styles";
import useGetLanguage from "src/app/Hooks/useGetLanguage";
import useSetLanguage from "src/app/Hooks/useSetTranslation";
import useTranslation from "src/app/Hooks/useTranslation";
import configureCalendarLocale from "src/app/Utils/ConfigureCalendarLocale";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import {Globe} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";
import {ILocalStorage} from "src/business/Interfaces/Services/IStorageHelper";
import {IUserService} from "src/business/Interfaces/Services/IUser";

const ToggleLanguage = () => {
  const languageApp = useGetLanguage();
  const [service, setService] = useState(languageApp);
  const styles = customStyles();
  const {
    users: {config},
  } = useTranslation();
  const {setLanguageApp} = useGeneralSettings();
  const setLanguage = useSetLanguage();

  const userService = container.get<IUserService>(TOKENS.UserService);
  const localStorageService = container.get<ILocalStorage>(TOKENS.LocalStorage);

  const onChange = async (itemValue: LanguageOptions) => {
    await userService.updateDeviceLanguage(itemValue);
    await localStorageService.setValue(Config.LANGUAGE_OPTION!, itemValue);

    setLanguage(itemValue);
    configureCalendarLocale(itemValue);
    setLanguageApp(itemValue);
  };

  useEffect(() => {
    setService(languageApp);
  }, [languageApp]);

  return (
    <Box {...styles.container}>
      <Menu
        trigger={triggerProps => {
          return (
            <IconButton
              {...triggerProps}
              variant="unstyled"
              icon={<Globe {...styles.icon.style} />}
              {...styles.iconButton}
            />
          );
        }}
        {...styles.menu}>
        <Menu.OptionGroup
          defaultValue={service}
          onChange={itemValue => {
            setService(itemValue);
            onChange(itemValue);
          }}
          title={config.placeholder.selectorName}
          type="radio"
          _title={{style: styles.menuOptionGroup.style}}>
          <Menu.ItemOption
            value={LanguageOptions.EN}
            _text={{style: styles.menuItemOption.style}}
            _icon={{size: styles.iconCheck.size}}>
            {config.placeholder.english}
          </Menu.ItemOption>
          <Menu.ItemOption
            value={LanguageOptions.PT}
            _text={{style: styles.menuItemOption.style}}
            _icon={{size: styles.iconCheck.size}}>
            {config.placeholder.portuguese}
          </Menu.ItemOption>
        </Menu.OptionGroup>
      </Menu>
    </Box>
  );
};
export default ToggleLanguage;
