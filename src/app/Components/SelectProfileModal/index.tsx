import React from "react";
import ModalSimple from "src/app/Components/ModalSimple";
import SelectProfileOptions from "src/app/Components/SelectProfileOptions";
import useTranslation from "src/app/Hooks/useTranslation";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useUser from "src/app/Zustand/Store/useUser";
import {EProfile} from "src/business/Models/Profile";

interface Props {
  visibility: boolean;
  onClose: () => void;
  onProfileSelect: (profileType: EProfile) => void;
}

const SelectProfileModal = ({visibility, onClose, onProfileSelect}: Props) => {
  const {userProfiles} = useUser();
  const {selectedProfile} = useGeneralSettings();
  const {
    forms: {selectProfile},
  } = useTranslation();

  return (
    <ModalSimple
      titleHeader={selectProfile.select}
      onClose={onClose}
      visibility={visibility}
      height="30%"
      removeCloseButton={!selectedProfile}
      closeOnOverlayClick={!!selectedProfile}>
      <SelectProfileOptions
        showOptions={userProfiles}
        handleProfileSelect={onProfileSelect}
      />
    </ModalSimple>
  );
};

export default SelectProfileModal;
