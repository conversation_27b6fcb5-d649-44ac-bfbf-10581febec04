import { ItemListFile } from "src/app/Components/Attachment/types";
import showToastError from "src/app/Components/Toast/toastError";

interface Props {
  item: ItemListFile;
  resources: any;
}

const handleCancellation = ({ item, resources }: Props) => {
  try {
    item.abortController?.abort();
    item.canceled = true;
  } catch (error) {
    showToastError(resources.errors.cancellation, {
      placement: "top",
    });
  }
};

export default handleCancellation;
