import EFileExtension from "src/business/Enums/Models/EFileExtension";
import DocumentPicker from "react-native-document-picker";

const handleTypeFiles = (type: EFileExtension) => {
  switch (type) {
    case EFileExtension.image: {
      return [DocumentPicker.types.images];
    }
    case EFileExtension.video: {
      return [DocumentPicker.types.video];
    }
    case EFileExtension.pdf: {
      return [DocumentPicker.types.pdf];
    }
    case EFileExtension.text: {
      return [DocumentPicker.types.plainText];
    }
    case EFileExtension.other: {
      return [DocumentPicker.types.allFiles];
    }
    case EFileExtension.imageAndVideo: {
      return [DocumentPicker.types.video, DocumentPicker.types.images];
    }
  }
};

export default handleTypeFiles;
