import { ItemListFile } from "src/app/Components/Attachment/types";
import showToastInfo from "src/app/Components/Toast/toastInfo";

interface Props {
  justOneFile: boolean;
  listUpload: ItemListFile[] | undefined;
  resources: any;
  handleDocumentSelection: () => Promise<void>;
}
const handleDocument = ({
  justOneFile,
  listUpload,
  resources,
  handleDocumentSelection,
}: Props) => {
  if (justOneFile && listUpload?.length === 1) {
    showToastInfo(resources.toasts.just_one_document, {
      placement: "top",
    });
  } else {
    handleDocumentSelection();
  }
};

export default handleDocument;
