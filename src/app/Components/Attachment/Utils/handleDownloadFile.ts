import RNFS from "react-native-fs";
import { PERMISSIONS, RESULTS } from "react-native-permissions";
import showToastError from "src/app/Components/Toast/toastError";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import { Resources } from "src/app/Context/Utils/Resources";
import { handleOneAppPermission } from "src/app/Utils/Permissions";

interface Props {
  url: string;
  fileName: string;
}

const handleDownloadFile = async ({ url, fileName }: Props) => {
  const { toasts } = Resources.get().attachmentFile;
  const filePath = `${RNFS.DownloadDirectoryPath}/${fileName}`;
  console.log("handleDownloadFile", filePath, url);
  try {
    const granted = await handleOneAppPermission({
      ios: PERMISSIONS.IOS.MEDIA_LIBRARY,
      android: PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
    });

    if (granted === RESULTS.GRANTED) {
      RNFS.downloadFile({
        fromUrl: url,
        toFile: filePath,
      })
        .promise.then(() => {
          showToastSuccess(toasts.download_success, {
            placement: "top",
          });
        })
        .catch((error) => {
          console.log("error", error);
          showToastError(toasts.download_error, {
            placement: "top",
          });
        });
    }
  } catch (error) {
    console.log("error", error);
    showToastError(toasts.download_error, {
      placement: "top",
    });
  }
};

export default handleDownloadFile;
