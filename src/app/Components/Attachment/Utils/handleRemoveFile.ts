import showToastError from "src/app/Components/Toast/toastError";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import { IFileService } from "src/business/Interfaces/Services/IFile";

interface Props {
  id: string;
  resources: any;
}

const handleRemoveFile = async ({ id, resources }: Props) => {
  const fileService = container.get<IFileService>(TOKENS.FileService);

  try {
    await fileService.delete(id);
  } catch (error) {
    showToastError(resources.errors.remove_file, {
      placement: "top",
    });
  }
};

export default handleRemoveFile;
