import { theme } from "native-base";
import { RFValue } from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainCenter: {
      style: {
        flex: 1,
        justifyContent: "flex-start",
        marginTop: wp("4%"),
        borderColor: ThemesApp.getTheme().colors.muted[300],
        borderStyle: "dashed",
      },
    },

    mainCenterWithMargin: {
      style: {
        flex: 1,
        justifyContent: "flex-start",
        margin: wp("4%"),
        borderColor: ThemesApp.getTheme().colors.muted[300],
        borderStyle: "dashed",
      },
    },

    filesCenter: {
      style: {
        flex: 1,
        margin: wp("2%"),
      },
    },

    button: {
      style: {
        marginTop: hp("5%"),
      },
    },

    flatlist: {
      style: {
        marginTop: verticalScale(6),
        height: hp("55%"),
      },
    },

    flatListContent: {
      style: {
        flexGrow: 1,
      },
    },

    textNote: {
      style: {
        fontSize: RFValue(12),
        opacity: 0.5,
      },
    },
    textChooseFiles: {
      style: {
        fontSize: RFValue(12),
        fontWeight: "bold",
        textTransform: "uppercase",
      },
    },

    boxUpload: {
      style: {
        flex: 1,
        alignItems: "center",
        justifyContent: "space-evenly",
        marginVertical: wp("6%"),
      },
    },

    iconUpload: {
      style: {
        fill: ThemesApp.getTheme().colors.iconUpload,
        width: moderateScale(70),
        height: moderateScale(70),
      },
    },

    mainBoxFlatlist: {
      height: "100%",
    },

    boxAdd: {
      style: {
        width: wp("100%"),
        alignItems: "center",
        justifyContent: "space-between",
      },
    },

    pressableButtons: {
      padding: wp("1%"),
      marginTop: hp("2%"),
      marginBottom: hp("2%"),
      marginLeft: wp("2%"),
      borderColor: ThemesApp.getTheme().colors.iconUpload,
      borderWidth: moderateScale(2),
      borderRadius: moderateScale(5),
    },

    buttonContainer: {
      style: {
        flexDirection: "row",
        alignItems: "center",
        width: wp("50%"),
        justifyContent: "center",
      },
    },

    iconButtonAdd: {
      style: {
        fill: ThemesApp.getTheme().colors.iconUpload,
        width: moderateScale(20),
        height: moderateScale(20),
      },
    },

    textButton: {
      style: {
        fontSize: RFValue(12),
        color: theme.colors.primary["900"],
        textTransform: "uppercase",
      },
    },

    centerGallery: {
      style: {
        paddingHorizontal: wp("2%"),
        justifyContent: "center",
        alignItems: "center",
      },
    },

    productImage: {
      style: {
        marginTop: 30,
        width: wp("100%"),
        height: hp("20%"),
      },
    },
  };
};

export default customStyles;
