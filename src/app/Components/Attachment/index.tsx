import {now} from "moment";
import {
  Box,
  Button,
  Center,
  Divider,
  FlatList,
  HStack,
  Pressable,
  Text,
  View,
} from "native-base";
import React, {memo, useCallback, useMemo, useRef, useState} from "react";
import DocumentPicker, {
  DocumentPickerResponse,
} from "react-native-document-picker";
import AddFile from "src/app/Components/Attachment/Components/AddFile";
import ItemUpload from "src/app/Components/Attachment/Components/ItemUpload/index";
import ListEmptyLoading from "src/app/Components/Attachment/Components/ListEmptyLoading";
import NoUploads from "src/app/Components/Attachment/Components/NoUploads";
import customStyles from "src/app/Components/Attachment/styles";
import {ItemListFile} from "src/app/Components/Attachment/types";
import handleDocument from "src/app/Components/Attachment/Utils/handleDocument";
import handleTypeFiles from "src/app/Components/Attachment/Utils/handleTypeFiles";
import Gallery, {IObjectGalery} from "src/app/Components/Gallery";
import ModalSimple from "src/app/Components/ModalSimple";
import showToastError from "src/app/Components/Toast/toastError";
import useTranslation from "src/app/Hooks/useTranslation";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import {File, Picture, Plus, UploadCloud} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {LocalFileData} from "src/business/DTOs/LocalFileData";
import {SaveFile} from "src/business/DTOs/SaveFile";
import EFile from "src/business/Enums/Models/EFile";
import EFileExtension from "src/business/Enums/Models/EFileExtension";
import EFileType from "src/business/Enums/Models/EFileType";
import {IFileService} from "src/business/Interfaces/Services/IFile";
import AppError from "src/business/Tools/AppError";
import {MAX_UPLOAD_PROGRESS} from "src/infrastructure/constants";

export interface AttachmentFileProps {
  entity: EFile;
  entityId?: string;
  type: EFileType;
  extension?: EFileExtension;
  buttonClose?: boolean;
  justOneFile?: boolean;
  maxSize?: number;
  initialItems?: LocalFileData[];
  readOnly?: boolean;
  updateFormContext?: (fileList: LocalFileData[]) => void;
  deleteAttachmentFromFormContext?: (id: string) => void;
}

const AttachmentFile = memo(
  ({
    entity,
    entityId,
    type,
    extension = EFileExtension.other,
    buttonClose = false,
    justOneFile = false,
    maxSize = 1,
    initialItems,
    readOnly,
    updateFormContext,
    deleteAttachmentFromFormContext,
  }: AttachmentFileProps) => {
    const styles = customStyles();
    const isEdition = entityId ? undefined : [];

    const photosList = useRef<IObjectGalery[]>([]);
    const uploadedFiles: LocalFileData[] = useMemo(() => [], []);
    const [itemsToUpload, setItemsToUpload] = React.useState<
      ItemListFile[] | undefined
    >(isEdition);
    const {
      attachmentFile: resources,
      imagePicker: {
        button: {gallery},
      },
    } = useTranslation();
    const [openGallery, setOpenGallery] = useState(false);

    const fileService = container.get<IFileService>(TOKENS.FileService);

    const handleClose = () => {
      if (updateFormContext && uploadedFiles.length > 0) {
        updateFormContext(uploadedFiles);
      }
    };

    const deleteAttachments = (id: string) => {
      if (deleteAttachmentFromFormContext) {
        deleteAttachmentFromFormContext(id);
      }
    };

    const updateFiles = (id: string) => {
      const index = uploadedFiles.findIndex(file => file.id === id);
      if (index > -1) {
        uploadedFiles.splice(index, 1);
      }
    };

    React.useEffect(() => {
      (async () => {
        if (entityId) {
          const list: ItemListFile[] = [];

          const data = await fileService.getByEntityAndType(
            entity,
            entityId,
            type,
          );
          if (data instanceof AppError) {
            showToastError(resources.errors.init_files, {placement: "top"});
          } else {
            let objectFile: ItemListFile;

            data.forEach(file => {
              if (file.type === type) {
                objectFile = {
                  key: file.id,
                  name: file.originalName,
                  progress: undefined,
                  size: file.size,
                  extension: file.extension,
                  uri: file.url,
                  type,
                  abortController: undefined,
                  id: file.id,
                  canceled: false,
                  failed: false,
                };

                list.push(objectFile);
              }
            });

            photosList.current = list.map(image => ({
              url: image.uri,
              type: image.extension,
            }));

            setItemsToUpload(list);
            updateFormContext && updateFormContext(data);
          }
        } else {
          if (initialItems) {
            let objectAttachment: ItemListFile;
            initialItems.forEach(item => {
              if (item.type === type) {
                objectAttachment = {
                  key: now().toString(),
                  name: item.originalName,
                  progress: undefined,
                  size: item.size,
                  extension: item.extension,
                  uri: item.url,
                  type,
                  abortController: undefined,
                  id: item.id,
                  canceled: false,
                  failed: false,
                };
                setItemsToUpload(items => {
                  if (items) {
                    return [...items, objectAttachment];
                  }
                });
              }
              uploadedFiles.push(item);
            });
          }
        }
      })();

      setItemsToUpload(state => {
        if (state) {
          return state.filter(itemList => !itemList.canceled);
        }
      });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleError = useCallback(
      (key: string, type: "general" | "size" | "canceled") => {
        setItemsToUpload(items => {
          if (items) {
            return items.map(item => {
              if (item.key === key) {
                if (type === "canceled") {
                  item.canceled = true;
                } else {
                  item.failed = true;
                }
                item.failure_type = type;
              }
              return item;
            });
          }
        });
        if (updateFormContext) {
          updateFormContext(uploadedFiles.filter(item => item.key !== key));
        }
      },
      [updateFormContext, uploadedFiles],
    );

    const handleUploadFile = useCallback(
      async (document: DocumentPickerResponse) => {
        const key = now().toString();

        const objectUpload: ItemListFile = {
          key,
          name: document.name ? document.name : now().toString(),
          progress: 0,
          size: document.size!,
          extension: document.type!,
          uri: document.uri,
          type,
          abortController: new AbortController(),
          canceled: false,
          failed: false,
        };

        setItemsToUpload(state => {
          if (state) {
            return [...state, objectUpload];
          }
          [];
        });

        if (
          typeof document.size === "number" &&
          document.size > maxSize * 1024 * 1024
        ) {
          handleError(key, "size");
        } else {
          const response = await fileService.save(
            document,
            {
              entity,
              entityId,
              type,
              size: document.size,
            } as SaveFile,
            objectUpload.abortController?.signal,
            e => {
              setItemsToUpload(state => {
                if (state) {
                  return state.map(item => {
                    if (item.key === key) {
                      item.progress = (e.loaded * 100) / e.total;
                    }
                    return item;
                  });
                }
              });
            },
          );

          if (response instanceof AppError) {
            if (response.message === "canceled") {
              handleError(key, "canceled");
            } else if (response.message === "large_file") {
              handleError(key, "size");
            } else {
              handleError(key, "general");
            }
          } else {
            setItemsToUpload(state => {
              if (state) {
                const copyState = [...state];
                const itemIndex = copyState?.findIndex(
                  item => item.key === key,
                );

                if (itemIndex !== undefined) {
                  copyState[itemIndex].id = response.id;
                  copyState[itemIndex].progress = MAX_UPLOAD_PROGRESS;
                }

                return copyState;
              }
            });

            uploadedFiles.push({
              ...response,
              entity,
              entityId,
              type,
              size: document.size || 0,
              extension: EFileExtension[extension],
              url_local: document.uri,
            });
            updateFormContext && updateFormContext(uploadedFiles);
          }
        }
      },
      [
        entity,
        entityId,
        fileService,
        handleError,
        maxSize,
        extension,
        updateFormContext,
        uploadedFiles,
        type,
      ],
    );

    const handleDocumentSelection = useCallback(async () => {
      try {
        const response = justOneFile
          ? await DocumentPicker.pick({
              presentationStyle: "fullScreen",
              type: handleTypeFiles(extension),
            })
          : await DocumentPicker.pickMultiple({
              presentationStyle: "fullScreen",
              type: handleTypeFiles(extension),
            });
        response.forEach(item => {
          handleUploadFile(item);
        });
      } catch (err) {
        showToastError(resources.toasts.select_error, {
          placement: "top",
        });
      }
    }, [
      handleUploadFile,
      justOneFile,
      resources.toasts.select_error,
      extension,
    ]);

    const onPressHandleDocument = useCallback(
      () =>
        handleDocument({
          justOneFile,
          listUpload: itemsToUpload,
          resources,
          handleDocumentSelection,
        }),
      [handleDocumentSelection, itemsToUpload, justOneFile, resources],
    );

    const openGalleryModal = () => {
      setOpenGallery(!openGallery);
    };

    return (
      <Center
        borderWidth={itemsToUpload?.length === 0 ? 3 : 0}
        style={
          itemsToUpload?.length === 0
            ? styles.mainCenterWithMargin.style
            : styles.mainCenter.style
        }>
        <ModalSimple
          visibility={openGallery}
          onClose={openGalleryModal}
          titleHeader={gallery}>
          <View {...styles.centerGallery}>
            <Gallery photos={photosList.current} showCarousel={false} />
          </View>
        </ModalSimple>
        {itemsToUpload?.length === 0 ? (
          <NoUploads
            readOnly={readOnly}
            maxSize={maxSize}
            onPressHandleDocument={onPressHandleDocument}
          />
        ) : (
          <Box {...styles.mainBoxFlatlist}>
            <FlatList
              {...styles.flatlist}
              data={itemsToUpload}
              keyExtractor={(item: ItemListFile) => item.key.toString()}
              renderItem={({item}) => (
                <ItemUpload
                  item={item}
                  maxSize={maxSize}
                  setItemsToUpload={setItemsToUpload}
                  updateFiles={updateFiles}
                  deleteAttachmentFromFormContext={deleteAttachments}
                />
              )}
              contentContainerStyle={styles.flatListContent.style}
              ListEmptyComponent={<ListEmptyLoading />}
            />

            {itemsToUpload !== undefined &&
            (!justOneFile || itemsToUpload.length < 1) &&
            !readOnly ? (
              <AddFile
                onPressHandleDocument={onPressHandleDocument}
                openGalleryModal={openGalleryModal}
              />
            ) : null}

            {buttonClose && (
              <Button onPress={handleClose} {...styles.button}>
                {resources.button.close}
              </Button>
            )}
          </Box>
        )}
      </Center>
    );
  },
);

export default AttachmentFile;
