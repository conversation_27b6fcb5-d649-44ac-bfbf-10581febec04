import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { moderateScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    fileNameText: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    progressContainer: {
      alignItems: "center",
      justifyContent: "space-between",
    },
    progress: {
      flex: 1,
    },
    closeButtonContainer: {
      marginLeft: wp("3%"),
    },
    iconCancel: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.muted[600],
      },
    },
  };
};

export default customStyles;
