import { HStack, Pressable, Progress, Text, VStack } from "native-base";
import React from "react";
import customStyles from "src/app/Components/Attachment/Components/ItemUpload/Uploading/styles";
import { CancelCircle } from "src/assets/Icons/Flaticon";
import { MAX_UPLOAD_PROGRESS } from "src/infrastructure/constants";

interface ItemUploadCanceledBodyProps {
  fileName: string;
  progress: number;
  onCancelPress: () => void;
}

const ItemUploadingBody = ({ fileName, progress, onCancelPress }: ItemUploadCanceledBodyProps) => {
  const styles = customStyles();
  return (
    <VStack>
      <Text ellipsizeMode="tail" numberOfLines={1} {...styles.fileNameText}>
        {fileName}
      </Text>

      <HStack {...styles.progressContainer}>
        <Progress value={progress} max={MAX_UPLOAD_PROGRESS} size="xs" {...styles.progress} />

        <Pressable
          {...styles.closeButtonContainer}
          onPress={onCancelPress}
          _pressed={{ opacity: 0.5 }}
          isDisabled={progress === MAX_UPLOAD_PROGRESS}
        >
          <CancelCircle {...styles.iconCancel.style} />
        </Pressable>
      </HStack>
    </VStack>
  );
};

export default ItemUploadingBody;
