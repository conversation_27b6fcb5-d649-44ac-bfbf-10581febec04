import { IStyleProps } from "src/business/Interfaces/IStyleProps";

import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";

import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    box: {
      style: {
        width: "100%",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        paddingVertical: wp("1%"),
        paddingHorizontal: wp("5%"),
      },
    },

    image: {
      style: {
        width: horizontalScale(45),
        height: verticalScale(45),
        borderWidth: moderateScale(1),
        borderRadius: moderateScale(5),
        marginRight: wp("2%"),
      },
    },

    boxIcon: {
      size: moderateScale(45),
      style: {
        borderWidth: moderateScale(1),
        borderRadius: moderateScale(5),
        borderColor: ThemesApp.getTheme().colors.textApp,
        marginRight: wp("2%"),
        alignItems: "center",
        justifyContent: "center",
      },
    },

    iconUpload: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.iconUploadLight,
      },
    },

    hstack: {
      style: {
        width: wp("30%"),
        justifyContent: "flex-end",
        alignItems: "center",
        alignContent: "center",
      },
    },

    vstack: {
      style: {
        flex: 1,
      },
    },

    iconButton: {
      style: {
        marginLeft: wp("3%"),
        width: moderateScale(28),
        height: moderateScale(28),
        fill: ThemesApp.getTheme().colors.iconUploadLight,
      },
    },

    badgeText: {
      fontWeight: "bold",
      fontSize: RFValue(10),
      lineHeight: RFValue(12),
    },

    progress: {
      flex: 1,
    },

    boxErrorElement: {
      style: {
        width: moderateScale(5),
        height: "100%",
        backgroundColor: ThemesApp.getTheme().colors.danger[500],
        position: "absolute",
        left: 0,
      },
    },

    hStackFooter: {
      alignItems: "center",
      justifyContent: "space-between",
    },
  };
};

export default customStyles;
