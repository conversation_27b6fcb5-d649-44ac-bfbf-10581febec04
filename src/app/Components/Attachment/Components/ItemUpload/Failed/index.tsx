import { HStack, Text, VStack } from "native-base";
import React from "react";
import customStyles from "src/app/Components/Attachment/Components/ItemUpload/Failed/styles";
import { TrashOutline } from "src/assets/Icons/Flaticon";

interface ItemUploadCanceledBodyProps {
  fileName: string;
  errorText: string;
  onTrashPress: () => void;
}

const ItemUploadFailedBody = ({ fileName, errorText, onTrashPress }: ItemUploadCanceledBodyProps) => {
  const styles = customStyles();
  return (
    <VStack>
      <Text ellipsizeMode="tail" numberOfLines={1} {...styles.fileNameText}>
        {fileName}
      </Text>
      <HStack {...styles.horizontalContainer}>
        <Text {...styles.errorText}>{errorText}</Text>
        <TrashOutline onPress={onTrashPress} {...styles.iconsButtons.style} />
      </HStack>
    </VStack>
  );
};

export default ItemUploadFailedBody;
