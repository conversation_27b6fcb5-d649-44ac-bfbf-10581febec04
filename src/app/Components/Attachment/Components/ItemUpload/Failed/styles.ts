import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    fileNameText: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    errorText: {
      style: {
        color: ThemesApp.getTheme().colors.heartIcon,
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        fontWeight: "bold",
      },
    },
    horizontalContainer: {
      style: {
        justifyContent: "space-between",
        alignItems: "flex-end",
      },
    },
    iconsButtons: {
      style: {
        fill: ThemesApp.getTheme().colors.arrowDropdown,
        marginLeft: wp("3%"),
      },
    },
  };
};

export default customStyles;
