import {Box, HStack, Image, Pressable, View, VStack} from "native-base";
import React from "react";
import ItemUploadCanceledBody from "src/app/Components/Attachment/Components/ItemUpload/Canceled";
import ItemUploadFailedBody from "src/app/Components/Attachment/Components/ItemUpload/Failed";
import customStyles from "src/app/Components/Attachment/Components/ItemUpload/styles";
import ItemUploadedBody from "src/app/Components/Attachment/Components/ItemUpload/Uploaded";
import ItemUploadingBody from "src/app/Components/Attachment/Components/ItemUpload/Uploading";
import {ItemListFile} from "src/app/Components/Attachment/types";
import handleCancellation from "src/app/Components/Attachment/Utils/handleCancellation";
import HandleDownloadFile from "src/app/Components/Attachment/Utils/handleDownloadFile";
import handleRemoveFile from "src/app/Components/Attachment/Utils/handleRemoveFile";
import useTranslation from "src/app/Hooks/useTranslation";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import {Upload} from "src/assets/Icons/Flaticon";
import {MAX_UPLOAD_PROGRESS} from "src/infrastructure/constants";

interface Props {
  item: ItemListFile;
  maxSize: number;
  setItemsToUpload: React.Dispatch<
    React.SetStateAction<ItemListFile[] | undefined>
  >;
  updateFiles: (key: string) => void;
  deleteAttachmentFromFormContext?: (id: string) => void;
}
const ItemUpload = ({
  item,
  maxSize,
  setItemsToUpload,
  updateFiles,
  deleteAttachmentFromFormContext,
}: Props) => {
  const styles = customStyles();
  const {attachmentFile: resources} = useTranslation();

  const handleDownload = () => {
    HandleDownloadFile({
      url: item.uri,
      fileName: item.name,
    });
  };

  const handleDelete = () => {
    setItemsToUpload(items => {
      if (items) {
        return items.filter(itemList => itemList.key !== item.key);
      }
    });
    if (item.id) {
      updateFiles(item.id);
      handleRemoveFile({id: item.id, resources});

      if (deleteAttachmentFromFormContext) {
        deleteAttachmentFromFormContext(item.id);
      }
    }
  };

  const handleCancel = () => {
    handleCancellation({item, resources});
    setItemsToUpload(items => {
      if (items) {
        const updatedList = items.map(itemsPrev => {
          if (itemsPrev.key === item.key) {
            return {...itemsPrev, canceled: true};
          } else {
            return itemsPrev;
          }
        });

        return updatedList;
      }
    });
  };

  return (
    <Pressable _pressed={{opacity: 0.5}}>
      {item?.failed ? <View {...styles.boxErrorElement} /> : null}
      <HStack {...styles.box}>
        {item.extension.includes("image") ? (
          <Image
            source={{uri: item.uri}}
            alt="image"
            style={styles.image.style}
          />
        ) : (
          <Box {...styles.boxIcon}>
            <Upload {...styles.iconUpload.style} />
          </Box>
        )}
        <VStack space={1} {...styles.vstack}>
          {(() => {
            if (item.canceled) {
              return (
                <ItemUploadCanceledBody
                  fileName={item.name}
                  badgeText={resources.badge.canceled}
                  onTrashPress={handleDelete}
                />
              );
            }

            if (item.failed) {
              return (
                <ItemUploadFailedBody
                  fileName={item.name}
                  errorText={
                    item.failure_type === "size"
                      ? setResourceParameters(
                          resources.errors.maxSizeFile,
                          maxSize,
                        )
                      : resources.errors.add_file
                  }
                  onTrashPress={handleDelete}
                />
              );
            }

            if (
              item.progress === undefined ||
              item.progress === MAX_UPLOAD_PROGRESS
            ) {
              return (
                <ItemUploadedBody
                  fileName={item.name}
                  fileSize={item.size}
                  onDownloadPress={handleDownload}
                  onTrashPress={handleDelete}
                />
              );
            }

            if (item.progress !== undefined) {
              return (
                <ItemUploadingBody
                  fileName={item.name}
                  progress={item.progress}
                  onCancelPress={handleCancel}
                />
              );
            }
          })()}
        </VStack>
      </HStack>
    </Pressable>
  );
};
export default ItemUpload;
