import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    fileNameText: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    iconButton: {
      style: {
        marginLeft: wp("3%"),
        width: moderateScale(28),
        height: moderateScale(28),
        fill: ThemesApp.getTheme().colors.iconUploadLight,
      },
    },
    fileInfoText: {
      style: {
        color: ThemesApp.getTheme().colors.muted[400],
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
      },
    },
    horizontalContainer: {
      style: {
        justifyContent: "space-between",
        alignItems: "flex-end",
      },
    },
    iconsButtons: {
      style: {
        fill: ThemesApp.getTheme().colors.arrowDropdown,
        marginLeft: wp("3%"),
      },
    },
  };
};

export default customStyles;
