import { HStack, Text, VStack } from "native-base";
import React from "react";
import customStyles from "src/app/Components/Attachment/Components/ItemUpload/Uploaded/styles";
import formatFileSize from "src/app/Utils/FormatFileSize";
import { Download, TrashOutline } from "src/assets/Icons/Flaticon";

interface ItemUploadCanceledBodyProps {
  fileName: string;
  fileSize: number;
  onTrashPress: () => void;
  onDownloadPress: () => void;
}

const ItemUploadedBody = ({ fileName, fileSize, onTrashPress, onDownloadPress }: ItemUploadCanceledBodyProps) => {
  const styles = customStyles();
  return (
    <VStack>
      <Text ellipsizeMode="tail" numberOfLines={1} {...styles.fileNameText}>
        {fileName}
      </Text>
      <HStack {...styles.horizontalContainer}>
        <Text {...styles.fileInfoText}>{formatFileSize(fileSize)}</Text>
        <HStack>
          <Download onPress={onDownloadPress} {...styles.iconsButtons.style} />
          <TrashOutline onPress={onTrashPress} {...styles.iconsButtons.style} />
        </HStack>
      </HStack>
    </VStack>
  );
};

export default ItemUploadedBody;
