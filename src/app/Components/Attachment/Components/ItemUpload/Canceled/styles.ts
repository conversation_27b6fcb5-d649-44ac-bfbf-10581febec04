import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    fileNameText: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    badgeText: {
      fontWeight: "bold",
      fontSize: RFValue(10),
      lineHeight: RFValue(12),
      color: ThemesApp.getTheme().colors.muted[600],
    },
    horizontalContainer: {
      style: {
        justifyContent: "space-between",
      },
    },
    iconsButtons: {
      style: {
        fill: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },
  };
};

export default customStyles;
