import React from "react";
import { Badge, HStack, Text, VStack } from "native-base";
import customStyles from "src/app/Components/Attachment/Components/ItemUpload/Canceled/styles";
import { TrashOutline } from "src/assets/Icons/Flaticon";

interface ItemUploadCanceledBodyProps {
  fileName: string;
  badgeText: string;
  onTrashPress: () => void;
}

const ItemUploadCanceledBody = ({ fileName, badgeText, onTrashPress }: ItemUploadCanceledBodyProps) => {
  const styles = customStyles();
  return (
    <VStack>
      <Text ellipsizeMode="tail" numberOfLines={1} {...styles.fileNameText}>
        {fileName}
      </Text>
      <HStack {...styles.horizontalContainer}>
        <Badge variant="solid" _text={{ ...styles.badgeText }} colorScheme="dark">
          {badgeText}
        </Badge>
        <TrashOutline onPress={onTrashPress} {...styles.iconsButtons.style} />
      </HStack>
    </VStack>
  );
};

export default ItemUploadCanceledBody;
