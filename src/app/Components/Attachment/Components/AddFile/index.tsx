import {Divider, HStack, Pressable, Text, View} from "native-base";
import React from "react";
import customStyles from "src/app/Components/Attachment/Components/AddFile/styles";
import useTranslation from "src/app/Hooks/useTranslation";
import {Picture, Plus} from "src/assets/Icons/Flaticon";

type Props = {
  onPressHandleDocument: () => void;
  openGalleryModal: () => void;
};

const AddFile = ({onPressHandleDocument, openGalleryModal}: Props) => {
  const styles = customStyles();

  const {attachmentFile: resources} = useTranslation();

  return (
    <HStack {...styles.boxAdd}>
      <Pressable
        onPress={() => onPressHandleDocument()}
        _pressed={{opacity: 0.8}}
        {...styles.buttonContainer}>
        <Text {...styles.textButton}>{resources.button.add_file}</Text>
        <View {...styles.pressableButtons}>
          <Plus {...styles.iconButtonAdd.style} />
        </View>
      </Pressable>
      <Divider orientation="vertical" thickness={2} />
      <Pressable
        onPress={openGalleryModal}
        _pressed={{opacity: 0.5}}
        {...styles.buttonContainer}>
        <Text {...styles.textButton}>{resources.button.view_gallery}</Text>
        <View {...styles.pressableButtons}>
          <Picture {...styles.iconButtonAdd.style} />
        </View>
      </Pressable>
    </HStack>
  );
};

export default AddFile;
