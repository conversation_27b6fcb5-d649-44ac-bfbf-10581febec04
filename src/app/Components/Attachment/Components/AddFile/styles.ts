import {theme} from "native-base";
import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxAdd: {
      style: {
        width: wp("100%"),
        alignItems: "center",
        justifyContent: "space-between",
      },
    },
    buttonContainer: {
      style: {
        flexDirection: "row",
        alignItems: "center",
        width: wp("50%"),
        justifyContent: "center",
      },
    },
    textButton: {
      style: {
        fontSize: RFValue(12),
        color: theme.colors.primary["900"],
        textTransform: "uppercase",
      },
    },
    pressableButtons: {
      padding: wp("1%"),
      marginTop: hp("2%"),
      marginBottom: hp("2%"),
      marginLeft: wp("2%"),
      borderColor: ThemesApp.getTheme().colors.iconUpload,
      borderWidth: moderateScale(2),
      borderRadius: moderateScale(5),
    },
    iconButtonAdd: {
      style: {
        fill: ThemesApp.getTheme().colors.iconUpload,
        width: moderateScale(20),
        height: moderateScale(20),
      },
    },
  };
};

export default customStyles;
