import {Box, Button, Text} from "native-base";
import React from "react";
import customStyles from "src/app/Components/Attachment/Components/NoUploads/styles";
import useTranslation from "src/app/Hooks/useTranslation";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import {File, UploadCloud} from "src/assets/Icons/Flaticon";

type Props = {
  readOnly?: boolean;
  maxSize: number;
  onPressHandleDocument: () => void;
};

const NoUploads = ({readOnly, maxSize, onPressHandleDocument}: Props) => {
  const styles = customStyles();
  const {attachmentFile: resources} = useTranslation();

  return (
    <Box {...styles.boxUpload}>
      {readOnly ? (
        <>
          <File {...styles.iconUpload.style} />
          <Text {...styles.textChooseFiles}>{resources.text.noFiles}</Text>
        </>
      ) : (
        <>
          <UploadCloud {...styles.iconUpload.style} />
          <Text {...styles.textChooseFiles}>{resources.text.chooseFiles}</Text>
          <Button onPress={() => onPressHandleDocument()} {...styles.button}>
            {resources.button.choose}
          </Button>
          <Text {...styles.textNote}>
            {setResourceParameters(resources.errors.maxSizeFile, maxSize)}
          </Text>
        </>
      )}
    </Box>
  );
};

export default NoUploads;
