import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxUpload: {
      style: {
        flex: 1,
        alignItems: "center",
        justifyContent: "space-evenly",
        marginVertical: wp("6%"),
      },
    },
    iconUpload: {
      style: {
        fill: ThemesApp.getTheme().colors.iconUpload,
        width: moderateScale(70),
        height: moderateScale(70),
      },
    },
    textChooseFiles: {
      style: {
        fontSize: RFValue(12),
        fontWeight: "bold",
        textTransform: "uppercase",
      },
    },
    button: {
      style: {
        marginTop: hp("5%"),
      },
    },
    textNote: {
      style: {
        fontSize: RFValue(12),
        opacity: 0.5,
      },
    },
  };
};

export default customStyles;
