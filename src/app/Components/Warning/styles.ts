import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    alertPixContainer: {
      style: {
        width: wp("100%"),
        marginLeft: hp("5%"),
        alignItems: "center",
      },
    },
    alertPixText: {
      style: {
        width: wp("70%"),
        color: ThemesApp.getTheme().colors.gray[700],
        fontSize: RFValue(12),
      },
    },
    buttonPix: {
      style: {
        paddingRight: RFValue(5),
      },
    },
  };
};

export default customStyles;
