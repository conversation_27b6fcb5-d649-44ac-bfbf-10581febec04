import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, VStack, Text} from "native-base";
import useTranslation from "src/app/Hooks/useTranslation";
import {RFValue} from "react-native-responsive-fontsize";
import customStyles from "src/app/Components/Warning/styles";

interface Props {
  handleMissingPix: () => void;
  isDeliveryman?: boolean;
}

const VerifyPixWarning = ({handleMissingPix, isDeliveryman = true}: Props) => {
  const {verifyPix: resources} = useTranslation();
  const styles = customStyles();
  return (
    <Alert status="warning">
      <VStack>
        <HStack {...styles.alertPixContainer}>
          <Alert.Icon />
          <Button
            variant="link"
            onPress={handleMissingPix}
            _text={{fontSize: RFValue(12)}}
            {...styles.buttonPix}>
            {resources.click_here}
          </Button>
          <Text {...styles.alertPixText}>
            {isDeliveryman
              ? resources.deliverymanText
              : resources.shopkeeperText}
          </Text>
        </HStack>
      </VStack>
    </Alert>
  );
};

export default VerifyPixWarning;
