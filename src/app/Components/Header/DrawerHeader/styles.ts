import { green } from "react-native-reanimated/lib/typescript/reanimated2/Colors";
import {RFValue} from "react-native-responsive-fontsize";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        padding: wp("4%"),
      },
    },
    routeName: {
      style: {
        textAlign: "center",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
        alignSelf: "center",
        width: wp("90%"),
        fontWeight: "bold",
      },
    },
    avatar: {
      size: moderateScale(40),
      style: {
        backgroundColor: "lightgray",
      },
    },
    avatarBadge: {
      size: moderateScale(25),
      style: {
        backgroundColor: "white",
        alignItems: "baseline",
        justifyContent: "center",
      },
    },

    avatarIcon: {
      style: {
        width: moderateScale(40),
        height: moderateScale(40),
        fill: ThemesApp.getTheme().colors.coolGray[100],
      },
    },
    pageTitleContainer: {
      flex: 1,
    },

    drawerIconSettings: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginLeft: wp("3%"),
        fontWeight: "bold",
        fill: ThemesApp.getTheme().colors.white,
      },
    },

    addressContainer: {
      style: {
        flex: 1,
        flexDirection: "row",
        alignItems: "center",
      },
    },
    addressBox: {
      style: {
        flex: 1,
        borderRadius: moderateScale(5),
        paddingLeft: moderateScale(10),
        // paddingRight: moderateScale(10),
        
      },
    },

    selectAddress: {
      flex: 1,
      bgColor: "transparent",
      fontSize: RFValue(14),
      fontWeight: "bold",
      ml: -4,
    },

    skeletonAddress: {
      width: "2xs",
    },

    selectItemAddress: {
      p: 1,
    },

    select: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.bgSelect,
        borderRadius: moderateScale(5),
      },
    },

    dropdownIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.primary[500],
        marginRight: "50%"
      },
    },

    selectNbItem: {
      py: 2,
    },

    endIconSelect: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.checkedIcon,
        position: "absolute",
        right: RFValue(0),
      },
    },

    selectText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    menuIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.textApp,
      },
    },

    notificationButton: {
      style: {   
        borderRadius: 50,
        borderWidth: 1.5,
        borderColor: ThemesApp.getTheme().colors.muted[100]
      },
    },

    cartButton: {
      style: {   
        borderRadius: 50,
        borderWidth: 1.5,
        borderColor: ThemesApp.getTheme().colors.muted[200]
      },
    },



  };

};

export default customStyles;
