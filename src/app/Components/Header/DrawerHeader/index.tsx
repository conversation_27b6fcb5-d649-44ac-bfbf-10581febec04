import {Route} from "@react-navigation/native";
import {NativeStackNavigationProp} from "@react-navigation/native-stack";
import {
  Avatar,
  Box,
  HStack,
  IconButton,
  Select as SelectNB,
  Skeleton,
  Text,
} from "native-base";
import React, {useEffect, useLayoutEffect, useState} from "react";
import customStyles from "src/app/Components/Header/DrawerHeader/styles";
import showToastError from "src/app/Components/Toast/toastError";
import {Components} from "src/app/Context/Utils/Components";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {IUserPosition} from "src/app/Hooks/useGetCurrentLocation";
import useTranslation from "src/app/Hooks/useTranslation";
import useGetAddressOptions from "src/app/Modules/Main/Address/Query/useGetAddressOptions";

import getCurrentLocation from "src/app/Utils/GetCurrentLocation";
import useAddress from "src/app/Zustand/Store/useAddress";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useUser from "src/app/Zustand/Store/useUser";
import {
  Check,
  ChevronDown,
  Notification,
  ShoppingCart,
  UserCircle,
} from "src/assets/Icons/Flaticon";
import {Location} from "src/business/DTOs/Location";
import {AddressOption} from "src/business/Models/Address/AddressOption";

interface Props {
  navigation: NativeStackNavigationProp<any, any>;
  route: Route<string>;
  rightIcon?: React.ReactNode;
  onAddressChange?: (item: Location) => void;
  onSelectCurrentLocation?: (position: IUserPosition) => void;
}

const DrawerHeader = ({
  navigation,
  route,
  rightIcon,
  onAddressChange,
  onSelectCurrentLocation,
}: Props) => {
  const {user} = useUser();
  const [urlPhotoProfile, setUrlPhotoProfile] = useState(
    user?.profilePictureUrl ? user?.profilePictureUrl : undefined,
  );

  const {setCurrentAddress} = useAddress();
  const [addressId, setAddressId] = useState<string>("");
  // FIXME Get addresses only when the user is a client
  const addressOptionsQuery = useGetAddressOptions();

  const {isLoading} = addressOptionsQuery;

  useEffect(() => {
    if (
      addressId === "" &&
      addressOptionsQuery.data &&
      addressOptionsQuery.data.length > 0
    ) {
      const [firstAddress] = addressOptionsQuery.data;
      setAddressId(firstAddress.id);
      setCurrentAddress(firstAddress);
    }
  }, [addressId, addressOptionsQuery.data, setCurrentAddress]);

  const {languageApp} = useGeneralSettings();

  const styles = customStyles();

  const {
    routes: resources,
    orders,
    stores: {create},
    generic: {errors},
  } = useTranslation();

  const handleSelectedAddress = async (address: AddressOption) => {
    setCurrentAddress(address);

    if (onAddressChange) {
      onAddressChange({
        latitude: address.latitude,
        longitude: address.longitude,
      });
    }
  };

  const handleCurrentLocation = () => {
    const subscription = Components.showLoading();

    getCurrentLocation({
      languageApp,
      onLoadLocation: position => {
        if (onSelectCurrentLocation) {
          onSelectCurrentLocation(position);
        }
        subscription.remove();
      },
      onError: () => {
        showToastError(errors.location_error);
      },
    });
  };

  useLayoutEffect(() => {
    setUrlPhotoProfile(user?.profilePictureUrl);
  }, [user]);

  return (
    <HStack
      {...styles.container}
      background={
        route.name.includes("SettingsHome")
          ? ThemesApp.getTheme().colors.headerSettingsHome
          : ThemesApp.getTheme().colors.background
      }>
      <Avatar {...styles.avatar} source={{uri: urlPhotoProfile || undefined}}>
        <UserCircle {...styles.avatarIcon.style} />
      </Avatar>

      {route.name.includes("StoreList") ? (
        <Box {...styles.addressContainer}>
          {isLoading ? (
            <Skeleton {...styles.skeletonAddress} />
          ) : (
            <HStack alignItems="center" justifyContent="space-between" flex={1}>
              <Box {...styles.addressBox}>
                <Text
                  fontSize={14}
                  color={ThemesApp.getTheme().colors.primary[500]}
                  style={{fontWeight: "ultralight"}}>
                  {create.header.options_address}
                </Text>
                <SelectNB
                  variant="unstyled"
                  placeholder={orders.placeholder.address}
                  selectedValue={addressId}
                  _selectedItem={{
                    endIcon: <Check {...styles.endIconSelect.style} />,
                    style: styles.select.style,
                  }}
                  dropdownIcon={<ChevronDown {...styles.dropdownIcon.style} />}
                  onValueChange={(itemValue: string) => setAddressId(itemValue)}
                  _item={{...styles.selectNbItem}}
                  {...styles.selectAddress}>
                  <SelectNB.Item
                    key={1}
                    label={orders.label.background_location_task_title}
                    value="1"
                    onTouchEnd={handleCurrentLocation}
                    _text={{...styles.selectText}}
                    {...styles.selectItemAddress}
                  />
                  {addressOptionsQuery.data?.map(address => {
                    return (
                      <SelectNB.Item
                        key={address.id}
                        label={
                          address.nickname ? address.nickname : address.street
                        }
                        value={address.id}
                        onTouchEnd={() => handleSelectedAddress(address)}
                        _text={{...styles.selectText}}
                        {...styles.selectItemAddress}
                      />
                    );
                  })}
                </SelectNB>
              </Box>
              <Box
                flexDirection="row"
                alignItems="center"
                justifyContent="space-between">
                <IconButton
                  {...styles.cartButton}
                  margin={1}
                  icon={
                    <Notification
                      color={ThemesApp.getTheme().colors.primary[500]}
                    />
                  }
                  onPress={() => {
                    navigation.navigate("MainApp", {
                      screen: "UserNotifications",
                    });
                  }}
                />
                <IconButton
                  {...styles.cartButton}
                  margin={1}
                  icon={
                    <ShoppingCart
                      color={ThemesApp.getTheme().colors.primary[500]}
                    />
                  }
                  onPress={() => {
                    navigation.push("Cart" as any);
                  }}
                />
              </Box>
            </HStack>
          )}
        </Box>
      ) : (
        <Box {...styles.pageTitleContainer}>
          <Text {...styles.routeName}>
            {resources[route.name as keyof typeof resources]}
          </Text>
        </Box>
      )}
      {rightIcon || null}
    </HStack>
  );
};

export default DrawerHeader;
