import {RFValue} from "react-native-responsive-fontsize";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        padding: wp("4%"),
        alignItems: "center",
        justifyContent: "center",
        borderBottomColor: ThemesApp.getTheme().colors.lineGray,
        borderBottomWidth: moderateScale(1),
      },
    },

    settingsContainer: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.headerSettingsHome,
        borderBottomWidth: moderateScale(0),
      },
    },

    containerBlueTheme: {
      style: {
        padding: wp("4%"),
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: ThemesApp.getTheme().colors.stackHeaderBlue,
        borderBottomColor: ThemesApp.getTheme().colors.stackHeaderBorderBlue,
        borderBottomWidth: moderateScale(1),
      },
    },

    routeName: {
      style: {
        textAlign: "center",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
        alignSelf: "center",
        fontWeight: "bold",
      },
    },

    routeNameBlueTheme: {
      style: {
        textAlign: "center",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: "white",
        alignSelf: "center",
        fontWeight: "bold",
      },
    },

    backIcon: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        position: "absolute",
        left: wp("5%"),
        zIndex: 100,
        fontWeight: "bold",
        fill: ThemesApp.getTheme().colors.textColor,
      },
    },

    rightContainer: {
      style: {
        position: "absolute",
        right: wp("5%"),
        zIndex: 100,
      },
    },

    drawerIconBlueTheme: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        position: "absolute",
        left: wp("5%"),
        zIndex: 100,
        fontWeight: "bold",
        fill: ThemesApp.getTheme().colors.textColor,
      },
    },

    buttonGroup: {
      style: {
        justifyContent: "flex-end",
        width: wp("100%"),
      },
    },

    btnPositive: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.error,
      },
    },

    btnNegative: {
      style: {
        width: wp("30%"),
      },
    },
  };
};

export default customStyles;
