import {NavigationProp, Route} from "@react-navigation/native";
import {Button, HStack, Text} from "native-base";
import React, {useState} from "react";
import customStyles from "src/app/Components/Header/StackHeader/styles";
import ModalRN from "src/app/Components/Modal";
import {Resources} from "src/app/Context/Utils/Resources";
import handleBackNavigationBottomTabsActive from "src/app/Utils/HandleBackNavigationBottomTabsActive";
import {rootGoBack} from "src/app/Utils/RootNavigation";
import {ChevronLeft} from "src/assets/Icons/Flaticon";

interface Props {
  navigation: NavigationProp<any, any>;
  route: Route<string>;
  title?: string;
  withMultistep?: boolean;
  rightNode?: React.ReactNode;
  overrideBackBehavior?: () => void;
}

const StackHeader = ({
  navigation,
  route,
  title,
  withMultistep = false,
  rightNode,
  overrideBackBehavior,
}: Props) => {
  const [visible, setVisible] = useState(false);
  const resources = Resources.get();
  const styles = customStyles();

  const handleStack = () => {
    if (withMultistep) {
      setVisible(true);
    } else {
      if (overrideBackBehavior) {
        overrideBackBehavior();
      } else {
        const handledNavigation = handleBackNavigationBottomTabsActive(
          navigation.getState(),
        );

        if (!handledNavigation) {
          navigation.goBack();
        }
      }
    }
  };

  const isSettings = route.name.includes("SettingsHome");

  return (
    <>
      <HStack
        style={[
          styles.container.style,
          isSettings ? styles.settingsContainer.style : {},
        ]}>
        <ChevronLeft
          onPress={() => handleStack()}
          style={styles.backIcon.style}
          fill={isSettings ? "white" : "black"}
        />

        <Text
          style={styles.routeName.style}
          ellipsizeMode="tail"
          numberOfLines={1}>
          {!title
            ? resources.routes[route.name as keyof typeof resources.routes] ||
              ""
            : title}
        </Text>

        <HStack style={styles.rightContainer.style}>{rightNode || null}</HStack>
      </HStack>
      <ModalRN
        title={resources.multiStepForm.modal.title}
        visibility={visible}
        onClose={() => setVisible(!visible)}
        componentFooter={
          <Button.Group direction="row" {...styles.buttonGroup}>
            <Button
              onPress={() => {
                setVisible(!visible);
              }}
              {...styles.btnNegative}>
              {resources.multiStepForm.modal.button.negative}
            </Button>
            <Button
              onPress={() => {
                rootGoBack();
                setVisible(false);
              }}
              {...styles.btnPositive}>
              {resources.multiStepForm.modal.button.positive}
            </Button>
          </Button.Group>
        }>
        {resources.multiStepForm.modal.text}
      </ModalRN>
    </>
  );
};

export default StackHeader;
