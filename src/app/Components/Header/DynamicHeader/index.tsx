import React from "react";
import { Animated } from "react-native";
import { verticalScale } from "src/app/Utils/Metrics";

interface IProps {
  animatedHeaderValue: Animated.Value;
  minHeight: number;
  maxHeight: number;
  outputRangeColor?: string[] | number[];
  outputRangeOpacity?: string[] | number[];
  stackHeader?: React.ReactNode;
  children: JSX.Element;
}

const DynamicHeader = ({
  animatedHeaderValue,
  minHeight,
  maxHeight,
  outputRangeColor,
  outputRangeOpacity,
  stackHeader,
  children,
}: IProps) => {
  const responsiveMaxHeight = verticalScale(maxHeight);
  const responsiveMinHeight = verticalScale(minHeight);

  const scrollDistance = responsiveMaxHeight - responsiveMinHeight;

  const animateHeaderWidth = animatedHeaderValue.interpolate({
    inputRange: [0, scrollDistance],
    outputRange: ["100%", "0%"],
    extrapolate: "clamp",
  });

  const animateHeaderHeight = animatedHeaderValue.interpolate({
    inputRange: [0, scrollDistance],
    outputRange: [responsiveMaxHeight, responsiveMinHeight],
    extrapolate: "clamp",
  });

  const animateHeaderBackgroundColor = animatedHeaderValue.interpolate({
    inputRange: [0, scrollDistance],
    outputRange: outputRangeColor ?? [0, 0],
    extrapolate: "clamp",
  });

  const animateHeaderOpacity = animatedHeaderValue.interpolate({
    inputRange: [0, scrollDistance],
    outputRange: outputRangeOpacity ?? [0, 0],
    extrapolate: "clamp",
  });

  return (
    <Animated.View
      style={{
        // width: animateHeaderWidth,
        height: animateHeaderHeight,
        backgroundColor: outputRangeColor
          ? animateHeaderBackgroundColor
          : undefined,
        opacity: outputRangeOpacity ? animateHeaderOpacity : undefined,
      }}
    >
      {stackHeader}
      {children}
    </Animated.View>
  );
};

export default DynamicHeader;
