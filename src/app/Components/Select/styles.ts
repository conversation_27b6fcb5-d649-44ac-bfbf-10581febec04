import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    selectLabel: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },

    endIconSelect: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.checkedIcon,
        position: "absolute",
        right: RFValue(0),
      },
    },

    select: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.bgSelect,
        borderRadius: moderateScale(5),
      },
    },
    selectText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    selectNb: {
      py: 1,
      style: {
        lineHeight: RFValue(16),
        fontSize: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    selectNbItem: {
      py: 2,
    },

    dropdownIcon: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.arrowDropdown,
        marginRight: wp("4%"),
      },
    },

    warningOutlineIcon: {
      style: {
        width: moderateScale(13),
        height: moderateScale(13),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },

    formControlErrorMessage: {
      mt: 1,
    },

    formControlHelperText: {
      mt: 2,
    },
  };
};

export default customStyles;
