import { FormControl, ISelectProps, Select as SelectNB } from "native-base";
import React, { memo } from "react";
import { Controller, useFormContext } from "react-hook-form";
import customStyles from "src/app/Components/Select/styles";
import { Check, ChevronDown, WarningOutline } from "src/assets/Icons/Flaticon";

interface InputProps extends ISelectProps {
  name: string;
  label: string;
  errorMessage?: string;
  optionsLabels: string[];
  optionsValues: number[];
  required?: boolean;
  endIconErrorInfo?: JSX.Element;
}

const Select = memo(
  ({
    name,
    label,
    errorMessage,
    optionsLabels,
    optionsValues,
    required = true,
    endIconErrorInfo,
    ...props
  }: InputProps) => {
    const { control } = useFormContext();
    const styles = customStyles();
    return (
      <FormControl isRequired={required} isInvalid={!!errorMessage}>
        <FormControl.Label _text={{ ...styles.selectLabel }} _astrick={{ ...styles.selectLabel }}>
          {label}
        </FormControl.Label>
        <Controller
          control={control}
          name={name}
          render={({ field: { onChange, onBlur, value } }) => {
            const handleChange = (newValue: string) => {
              onChange(newValue);
            };
            return (
              <SelectNB
                onValueChange={handleChange}
                selectedValue={value}
                _selectedItem={{
                  endIcon: <Check {...styles.endIconSelect.style} />,
                  style: styles.select.style,
                }}
                dropdownIcon={<ChevronDown {...styles.dropdownIcon.style} />}
                onClose={onBlur}
                _item={{ ...styles.selectNbItem }}
                {...styles.selectNb}
                {...props}
              >
                {optionsLabels.map((optionLabel, index) => (
                  <SelectNB.Item
                    key={optionLabel}
                    label={optionLabel}
                    value={optionsValues[index].toString()}
                    _text={{ ...styles.selectText }}
                  />
                ))}
              </SelectNB>
            );
          }}
        />

        {errorMessage ? (
          <FormControl.ErrorMessage
            _text={{ fontSize: 10, lineHeight: 12 }}
            leftIcon={<WarningOutline {...styles.warningOutlineIcon.style} />}
            {...styles.formControlErrorMessage}
            endIcon={endIconErrorInfo}
          >
            {errorMessage}
          </FormControl.ErrorMessage>
        ) : (
          <FormControl.HelperText _text={{ fontSize: 10, lineHeight: 12 }} {...styles.formControlHelperText}>
            &nbsp;
          </FormControl.HelperText>
        )}
      </FormControl>
    );
  },
);

export default Select;
