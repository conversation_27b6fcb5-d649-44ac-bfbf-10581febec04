import { StyledProps } from "native-base";
import { ImageStyle, TextInputProps, TextStyle, ViewStyle } from "react-native";
import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { SvgProps } from "react-native-svg";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { moderateScale } from "src/app/Utils/Metrics";

interface IChatStyleProps {
  [key: string]: StyledProps & {
    style: ViewStyle & TextStyle & ImageStyle & SvgProps & TextInputProps;
  };
}

const customStyles = (): IChatStyleProps => {
  return {
    avatarChat: {
      style: {
        height: moderateScale(28),
        width: moderateScale(28),
        backgroundColor: ThemesApp.getTheme().colors.gray[100],
      },
    },

    sendContainer: {
      style: {
        paddingHorizontal: wp("2%"),
        borderWidth: 0,
        alignItems: "center",
        justifyContent: "center",
      },
    },

    sendIcon: {
      style: {
        height: moderateScale(18),
        width: moderateScale(18),
        fill: ThemesApp.getTheme().colors.primary[600],
      },
    },

    clientBubbleRight: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.primary[600],
        paddingLeft: RFValue(2),
      },
    },
    deliverymanBubbleRight: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.secondary[600],
        paddingLeft: RFValue(2),
      },
    },

    shopkeeperBubbleRight: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.yellow[700],
        paddingLeft: RFValue(2),
      },
    },

    clientBubbleLeft: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.lightBlue[100],
        paddingLeft: RFValue(5),
      },
    },

    shopkeeperBubbleLeft: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.yellow[100],
        paddingLeft: RFValue(5),
      },
    },
    deliverymanBubbleLeft: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.red[100],
        paddingLeft: RFValue(5),
      },
    },

    bubbleRightText: {
      style: {
        color: ThemesApp.getTheme().colors.white,
      },
    },

    bubbleTimeRight: {
      style: {
        color: ThemesApp.getTheme().colors.white,
      },
    },

    bubbleTimeLeft: {
      style: {
        color: ThemesApp.getTheme().colors.gray[600],
      },
    },

    avatarIcon: {
      style: {
        width: moderateScale(28),
        height: moderateScale(28),
        fill: ThemesApp.getTheme().colors.muted[400],
      },
    },

    avatar: {
      size: moderateScale(28),
      backgroundColor: "white",
      style: {
        borderColor: ThemesApp.getTheme().colors.muted[600],
        borderWidth: RFValue(1),
        marginRight: RFValue(2),
      },
    },

    closedChatText: {
      style: {
        fontSize: RFValue(11),
        lineHeight: RFValue(13),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.red[600],
      },
    },
    closedChatBox: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        marginTop: RFValue(8),
      },
    },
    userProfile: {
      style: {
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        color: ThemesApp.getTheme().colors.darkBlue[600],
        paddingHorizontal: wp("2%"),
        fontWeight: "bold",
      },
    },
    userProfileBox: {
      style: {
        justifyContent: "center",
        alignItems: "center",
        paddingBottom: wp("4%"),
      },
    },

    textInput: {
      style: {
        color: ThemesApp.getTheme().colors.black,
        placeholderTextColor: ThemesApp.getTheme().colors.gray[600],
      },
    },
  };
};

export default customStyles;
