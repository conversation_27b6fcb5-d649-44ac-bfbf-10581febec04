/* eslint-disable react-hooks/exhaustive-deps */
import "dayjs/locale/en";
import "dayjs/locale/pt-br";
import {uniqueId} from "lodash";
import {Avatar, Box, Text} from "native-base";
import React, {useCallback} from "react";
import {
  AvatarProps,
  Bubble,
  BubbleProps,
  GiftedChat,
  IMessage,
  InputToolbar,
  Send,
  SendProps,
  Time,
  User,
} from "react-native-gifted-chat";
import customStyles from "src/app/Components/Chat/styles";
import useGetLanguage from "src/app/Hooks/useGetLanguage";
import useTranslation from "src/app/Hooks/useTranslation";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useUser from "src/app/Zustand/Store/useUser";
import {SendIcon, UserCircle} from "src/assets/Icons/Flaticon";
import {GiftedChatMessages} from "src/business/DTOs/Chat/GiftedChatMessages";
import {GiftedChatUser} from "src/business/DTOs/Chat/GiftedChatUser";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";
import {EProfile} from "src/business/Models/Profile";

interface IChatProps {
  messages: GiftedChatMessages[];
  saveMessages: (newMessages: GiftedChatMessages[]) => void;
  showInput: boolean;
  handleIsTyping?: (typing: boolean, userProfile: EProfile) => void;
  isTypingMessage?: string[];
}

const Chat = ({
  messages,
  saveMessages,
  showInput,
  handleIsTyping,
  isTypingMessage,
}: IChatProps) => {
  const styles = customStyles();
  const locale = useGetLanguage() === LanguageOptions.EN ? "en" : "pt-br";
  const {selectedProfile} = useGeneralSettings();
  const {
    chat: resources,
    forms: {selectProfile: profileResources},
  } = useTranslation();

  const {user} = useUser();

  const onSend = useCallback((newMessages: GiftedChatMessages[] = []) => {
    GiftedChat.append([...messages], newMessages);
    saveMessages(newMessages);
  }, []);

  const chatUser: User | undefined = selectedProfile
    ? {
        _id: selectedProfile,
        name: `${user?.firstName} ${user?.lastName}`,
        avatar: user?.profilePictureUrl,
      }
    : undefined;

  const renderAvatar = (props: AvatarProps<IMessage>) => {
    return (
      <Avatar
        {...styles.avatar}
        source={
          props.currentMessage?.user.avatar
            ? {uri: props.currentMessage.user.avatar.toString()}
            : undefined
        }>
        <UserCircle {...styles.avatarIcon.style} />
      </Avatar>
    );
  };

  const renderSend = (props: SendProps<IMessage>) => {
    return (
      <Send
        {...props}
        containerStyle={{...styles.sendContainer.style}}
        disabled={!props?.text?.trim()}>
        <SendIcon {...styles.sendIcon.style} />
      </Send>
    );
  };

  const renderBubble = (props: Readonly<BubbleProps<IMessage>>) => {
    const currentUserProp = props.currentMessage?.user as GiftedChatUser;
    return (
      <Bubble
        {...props}
        wrapperStyle={{
          right: selectedProfile && {
            ...styles[`${selectedProfile}BubbleRight`].style,
          },
          left: {...styles[`${currentUserProp.profile}BubbleLeft`].style},
        }}
        textStyle={{
          right: {...styles.bubbleRightText.style},
        }}
        renderTime={timeProps => (
          <Time
            {...timeProps}
            timeTextStyle={{
              left: {...styles.bubbleTimeLeft.style},
              right: {...styles.bubbleTimeRight.style},
            }}
            currentMessage={timeProps.currentMessage}
          />
        )}
        renderUsername={(currentUser: GiftedChatUser) => {
          return (
            <Text {...styles.userProfile}>
              {`~${
                profileResources.profileNames[
                  currentUser.profile as keyof typeof profileResources.profileNames
                ]
              }`}
            </Text>
          );
        }}
      />
    );
  };

  const renderFooter = () => {
    return isTypingMessage && isTypingMessage.length > 0
      ? () => {
          return (
            <Box {...styles.userProfileBox}>
              {isTypingMessage.map(item => (
                <Text key={uniqueId()} {...styles.userProfile}>
                  {item}
                </Text>
              ))}
            </Box>
          );
        }
      : undefined;
  };

  return (
    <GiftedChat
      renderInputToolbar={props =>
        showInput ? (
          <InputToolbar {...props} />
        ) : (
          <Box {...styles.closedChatBox}>
            <Text {...styles.closedChatText}>
              {resources.labels.closed_chat}
            </Text>
          </Box>
        )
      }
      textInputProps={styles.textInput.style}
      messages={messages}
      onSend={(newMessages: GiftedChatMessages[]) => onSend(newMessages)}
      user={chatUser}
      renderAvatarOnTop
      renderAvatar={renderAvatar}
      renderUsernameOnMessage
      locale={locale}
      scrollToBottom
      placeholder={resources.placeholder}
      alwaysShowSend
      renderSend={renderSend}
      renderBubble={renderBubble}
      renderFooter={renderFooter()}
      onInputTextChanged={
        handleIsTyping
          ? text => {
              if (text.length === 1 && selectedProfile) {
                handleIsTyping(true, selectedProfile);
              }
              if (text.length === 0 && selectedProfile) {
                handleIsTyping(false, selectedProfile);
              }
            }
          : undefined
      }
    />
  );
};

export default Chat;
