import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      w: "full",
      style: { marginTop: verticalScale(10) },
    },
    mainBox: {
      w: "full",
    },

    hStackHeader: {
      flex: 1,
      style: {
        marginRight: horizontalScale(10),
        alignItems: "center",
        justifyContent: "space-between",
      },
    },

    icon: {
      style: {
        width: moderateScale(14),
        height: moderateScale(14),
        fill: ThemesApp.getTheme().colors.textApp,
      },
    },

    pressableAccordion: {
      style: {
        marginBottom: verticalScale(10),
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
      },
    },

    statusText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.accordionStatusText,
      },
    },

    statusSubTitle: {
      style: {
        color: ThemesApp.getTheme().colors.accordionStatusSubtitle,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
  };
};

export default customStyles;
