import {Box, Divider, HStack, Pressable, Text} from "native-base";
import React from "react";
import customStyles from "src/app/Components/Accordion/styles";
import {verticalScale} from "src/app/Utils/Metrics";
import {ChevronDown, ChevronUp} from "src/assets/Icons/Flaticon";

interface IComponentProps {
  title?: string;
  subTitle?: string;
  setExpand: () => void;
  isExpanded?: boolean;
  controlExpand: boolean;
  children: JSX.Element | React.ReactNode;
}

const Accordion = ({
  title,
  subTitle,
  isExpanded = false,
  setExpand,
  controlExpand,
  children,
}: IComponentProps) => {
  const styles = customStyles();
  return (
    <Box {...styles.container}>
      <Box {...styles.mainBox}>
        <Pressable onPress={setExpand} {...styles.pressableAccordion}>
          <HStack {...styles.hStackHeader}>
            <Text {...styles.statusText}>{title}</Text>
            <Text {...styles.statusSubTitle}>{subTitle}</Text>
          </HStack>

          {isExpanded ? (
            <ChevronUp {...styles.icon.style} />
          ) : (
            <ChevronDown {...styles.icon.style} />
          )}
        </Pressable>

        {(controlExpand || isExpanded) && children}
        <Divider
          // eslint-disable-next-line react-native/no-inline-styles
          style={{
            marginTop: controlExpand || isExpanded ? verticalScale(10) : 0,
          }}
        />
      </Box>
    </Box>
  );
};

export default Accordion;
