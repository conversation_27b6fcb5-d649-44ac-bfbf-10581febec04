import { ScrollView } from "native-base";
import React from "react";
import ListOptionItem from "src/app/Components/ListOptions/Components/ListOptionItem";
import { IListOption } from "src/app/Components/ListOptions/types";

export interface ListOptionsProps {
  options: IListOption[];
}

const ListOptions = ({ options }: ListOptionsProps) => {
  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      {options.map(({ label, Icon, onPress, iconSizeMultiplier, badgeError }) => {
        return (
          <ListOptionItem
            key={label}
            label={label}
            onPress={onPress}
            Icon={Icon}
            iconSizeMultiplier={iconSizeMultiplier}
            badgeError={badgeError}
          />
        );
      })}
    </ScrollView>
  );
};

export default ListOptions;
