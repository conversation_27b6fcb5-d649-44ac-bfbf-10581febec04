import { Badge, HStack, Pressable, Text, View } from "native-base";
import React from "react";
import { SvgProps } from "react-native-svg";
import customStyles from "src/app/Components/ListOptions/Components/ListOptionItem/styles";
import { moderateScale } from "src/app/Utils/Metrics";
import { ChevronRight } from "src/assets/Icons/Flaticon";

interface ListOptionItemProps {
  label: string;
  onPress: () => void;
  Icon: (props: SvgProps) => JSX.Element;
  iconSizeMultiplier?: number;
  badgeError?: boolean;
}
const ListOptionItem = ({ label, onPress, Icon, iconSizeMultiplier = 1, badgeError = false }: ListOptionItemProps) => {
  const styles = customStyles();
  const iconSize = {
    width: moderateScale(16 * iconSizeMultiplier),
    height: moderateScale(16 * iconSizeMultiplier),
  };

  return (
    <Pressable onPress={onPress} _pressed={{ opacity: 0.5 }} {...styles.container}>
      <HStack {...styles.itemsContainer}>
        <View {...styles.leftContainer}>
          <Icon
            {...styles.optionIcon.style}
            // eslint-disable-next-line react-native/no-inline-styles
            style={{
              position: "absolute",
              left: "25%",
              // top: "50%",
              // transform: [{ translateX: -iconSize.width / 2 }, { translateY: -iconSize.height / 2 }],
            }}
          />
        </View>
        <View {...styles.middleContainer}>
          <Text {...styles.textItem}>{label}</Text>
        </View>

        <View {...styles.rightContainer}>
          {badgeError ? (
            <Badge
              _text={{
                ...styles.badgeText,
              }}
              {...styles.badge}
            >
              !
            </Badge>
          ) : null}
          <ChevronRight {...styles.arrowIcon.style} />
        </View>
      </HStack>
    </Pressable>
  );
};

export default ListOptionItem;
