import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        padding: wp("2%"),
        marginVertical: hp("1%"),
        width: "100%",
      },
    },
    itemsContainer: {
      style: {
        width: "100%",
        alignItems: "center",
        justifyContent: "space-between",
      },
    },
    leftContainer: {
      style: {
        flex: 0.1,
        position: "relative",
        height: 20,
      },
    },
    middleContainer: {
      style: {
        flex: 0.8,
      },
    },
    textItem: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        paddingLeft: wp("2%"),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    rightContainer: {
      style: {
        flex: 0.1,
        alignItems: "center",
        flexDirection: "row",
        justifyContent: "flex-end",
        marginRight: moderateScale(10),
      },
    },
    optionIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.iconHelp,
      },
    },

    arrowIcon: {
      style: {
        width: moderateScale(14),
        height: moderateScale(14),
        fill: ThemesApp.getTheme().colors.iconHelp,
      },
    },

    badge: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.error,
        marginRight: RFValue(5),
      },
      rounded: "full",
    },

    badgeText: {
      style: {
        color: ThemesApp.getTheme().colors.white,
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
      },
    },
  };
};

export default customStyles;
