import { Button, <PERSON><PERSON><PERSON><PERSON>, Popover, Pressable, Text } from "native-base";
import React, { useState } from "react";
import customStyles from "src/app/Components/Popover/styles";

interface PopoverProps {
  content: {
    title?: string | JSX.Element;
    message?: string | JSX.Element;
  };
  hasCloseButton?: boolean;
  componentTrigger: () => React.ReactNode;
}

interface callPopoverProps {
  triggerProps: any;
  componentTrigger: () => React.ReactNode;
}

const PopoverRN: React.FC<React.PropsWithChildren<PopoverProps>> = ({
  content,
  componentTrigger,
  hasCloseButton = true,
}) => {
  const styles = customStyles();
  const [popoverOffset, setPopoverOffset] = useState(0);

  const callPopover = (props: callPopoverProps) => {
    return (
      <Button
        padding={0}
        margin={0}
        variant="link"
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout;
          setPopoverOffset(height);
        }}
        {...props.triggerProps}
      >
        {props.componentTrigger()}
      </Button>
    );
  };

  return (
    <Popover
      placement="bottom left"
      offset={popoverOffset}
      trigger={(triggerProps) => {
        return callPopover({ triggerProps, componentTrigger });
      }}
    >
      <Popover.Content {...styles.popoverContent}>
        <Popover.Arrow />
        {hasCloseButton ? <Popover.CloseButton /> : null}
        {content.title ? (
          <Popover.Header>
            {typeof content.message === "string" ? (
              <Text {...styles.textTitlePopover}>{content.title} </Text>
            ) : (
              content.title
            )}
          </Popover.Header>
        ) : null}
        {content.message ? (
          <Popover.Body>
            {typeof content.message === "string" ? (
              <HStack>
                <Text {...styles.textPopover}>{content.message}</Text>
              </HStack>
            ) : (
              content.message
            )}
          </Popover.Body>
        ) : null}
      </Popover.Content>
    </Popover>
  );
};

export default PopoverRN;
