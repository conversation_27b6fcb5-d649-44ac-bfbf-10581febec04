import { RFValue } from "react-native-responsive-fontsize";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    textPopover: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        textAlign: "justify",
      },
    },
    textTitlePopover: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },
    popoverContent: {
      w: "92%",
      minW: "35%",
    },
  };
};

export default customStyles;
