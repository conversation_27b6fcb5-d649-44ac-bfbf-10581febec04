import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxSelect: {
      style: {
        flex: 1,
      },
    },

    pressable: {
      style: {
        marginLeft: horizontalScale(20),
        justifyContent: "space-evenly",
      },
    },

    hStack: {
      style: {
        justifyContent: "flex-start",
        alignItems: "center",
      },
    },

    textOption: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },

    iconCheckSelect: {
      size: moderateScale(20),
      style: {
        marginRight: horizontalScale(3),
      },
    },

    checkbox: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.checkboxIcon,
      },
    },

    lIcon: {
      style: {
        width: moderateScale(14),
        height: moderateScale(14),
        marginBottom: verticalScale(10),
      },
    },

    square: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.squareIcon,
      },
    },
  };
};

export default customStyles;
