import {Box, HStack, Pressable, Text} from "native-base";
import React from "react";

import customStyles from "src/app/Components/AttributeOption/styles";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

import {Checkbox, L, Square} from "src/assets/Icons/Flaticon";

type Props = {
  title: string;
  checked?: boolean;
  onPress: () => void;
  isOrderCreate?: boolean;
};
const AttributeOption = ({
  title,
  checked = false,
  onPress,
  isOrderCreate = false,
}: Props) => {
  const styles = customStyles();

  return (
    <Pressable
      onPress={onPress}
      {...styles.pressable}
      _pressed={{opacity: 0.5}}>
      <Box {...styles.boxSelect}>
        <HStack space={2} {...styles.hStack}>
          {isOrderCreate ? (
            checked ? (
              <Checkbox {...styles.checkbox.style} />
            ) : (
              <Square {...styles.square.style} />
            )
          ) : (
            <L
              fill={
                checked
                  ? ThemesApp.getTheme().colors.checkedIcon
                  : ThemesApp.getTheme().colors.squareIcon
              }
              {...styles.lIcon.style}
            />
          )}

          <Text
            {...styles.textOption}
            color={
              checked
                ? ThemesApp.getTheme().colors.checkedIcon
                : ThemesApp.getTheme().colors.squareIcon
            }
            fontWeight={checked ? "bold" : "normal"}>
            {title}
          </Text>
        </HStack>
      </Box>
    </Pressable>
  );
};

export default AttributeOption;
