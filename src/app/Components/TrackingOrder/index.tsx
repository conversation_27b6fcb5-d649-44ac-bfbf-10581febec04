import {Box} from "native-base";
import React from "react";
import ModalProgress from "src/app/Components/ModalProgress";
import {useLocation} from "src/app/Components/TrackingOrder/Hooks/useLocation";
import customStyles from "src/app/Components/TrackingOrder/styles";
import CloseTrackingMap from "src/app/Modules/Main/Orders/Presentation/TrackingOrder/Components/CloseMap";
import MapDirections from "src/app/Modules/Main/Orders/Presentation/TrackingOrder/Components/MapDirections";
import {CancelCircle} from "src/assets/Icons/Flaticon";

interface ITrackingProps {
  orderId: string;
  dimensionsMap?: {width: number; height: number};
  zoomLevel?: {min: number; max: number};
  showsMyLocationButton?: boolean;
  closeMap?: () => void;
  footer?: JSX.Element;
}

const TrackingOrderComponent = ({
  orderId,
  dimensionsMap,
  zoomLevel,
  showsMyLocationButton,
  closeMap,
  footer,
}: ITrackingProps) => {
  const styles = customStyles();
  const {location} = useLocation(orderId);

  // console.log("location TESTEE", location);

  return location?.deliveryman && location?.deliveryman.length > 0 ? (
    <>
      <Box>
        <MapDirections
          coords={location}
          dimensionsMap={dimensionsMap}
          zoomLevel={zoomLevel}
          showsMyLocationButton={showsMyLocationButton}
        />
      </Box>

      {closeMap ? (
        <CancelCircle onPress={closeMap} {...styles.cancelCircleIcon.style} />
      ) : (
        <CloseTrackingMap />
      )}

      {footer}
    </>
  ) : !closeMap ? (
    <ModalProgress />
  ) : null;
};

export default TrackingOrderComponent;
