import {useEffect, useState} from "react";
import {loadDeliverymanLocation} from "src/app/Modules/Main/Orders/Presentation/TrackingOrder/Utils/loadDeliverymanLocation";
import {TrackingOrderList} from "src/business/Models/List/TrackingOrder";

/* eslint-disable import/prefer-default-export */
export const useLocation = (orderId: string) => {
  const [location, setLocation] = useState<TrackingOrderList>();

  useEffect(() => {
    (async () => {
      const data = await loadDeliverymanLocation(orderId);
      setLocation(data);
    })();
  }, [orderId]);

  return {location};
};
