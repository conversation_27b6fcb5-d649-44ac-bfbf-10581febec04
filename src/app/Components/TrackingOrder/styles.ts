import {RFValue} from "react-native-responsive-fontsize";
import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    alert: {
      style: {
        width: wp("94%"),
      },
    },
    boxAlert: {
      w: "full",
      position: "absolute",
      bottom: verticalScale(15),
      alignItems: "center",
    },
    hStack: {
      style: {
        alignItems: "center",
      },
    },
    icon: {
      style: {
        fill: ThemesApp.getTheme().colors.secondary[200],
        height: verticalScale(20),
        width: horizontalScale(20),
      },
    },
    deliveryIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginRight: wp("2%"),
        fill: ThemesApp.getTheme().colors.white,
      },
    },
    deliveryText: {
      color: ThemesApp.getTheme().colors.white,
      fontSize: moderateScale(14),
      lineHeight: moderateScale(16),
    },
    boxFooter: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.background,
        paddingHorizontal: wp("2%"),
        paddingBottom: wp("3%"),
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 99,
      },
    },

    title: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    cancelCircleIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.gray[500],
        width: moderateScale(20),
        height: moderateScale(20),
        position: "absolute",
        top: 0,
        left: 0,
        margin: moderateScale(10),
        zIndex: 5,
      },
    },

    buttonClose: {
      style: {
        position: "absolute",
        top: 0,
        left: 0,
        margin: moderateScale(10),
        zIndex: 5,
      },
    },

    badge: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.primary[400],
        borderRadius: moderateScale(5),
      },
    },

    textBadgeButton: {
      style: {
        color: ThemesApp.getTheme().colors.white,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
  };
};

export default customStyles;
