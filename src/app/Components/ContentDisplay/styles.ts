import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        flex: 1,
        margin: wp("5%"),
      },
    },

    containerNotFound: {
      style: {
        flex: 1,
        alignItems: "center",
        margin: wp("5%"),
      },
    },

    containerLoading: {
      style: {
        flex: 1,
        alignItems: "center",
        margin: wp("5%"),
      },
    },
  };
};

export default customStyles;
