import {useFocusEffect} from "@react-navigation/native";
import {Box, Spinner, Text} from "native-base";
import React, {useCallback, useEffect, useState} from "react";
import {WebView} from "react-native-webview";
import customStyles from "src/app/Components/ContentDisplay/styles";
import ModalSimple from "src/app/Components/ModalSimple";
import {Resources} from "src/app/Context/Utils/Resources";
import useGetContentManagement from "src/app/Modules/Main/Settings/Query/useGetContentManagement";
import EContentManagementType from "src/business/Enums/EContentManagementType";
import {ContentManagement} from "src/business/Models/ContentManagement";

interface Props {
  visible: boolean;
  type: EContentManagementType | undefined;
  onClose: () => void;
}

const ContentDisplay = ({visible, type, onClose}: Props) => {
  const styles = customStyles();
  const {data, refetch} = useGetContentManagement(type);

  useFocusEffect(() => {
    refetch();
  });

  const {
    settings: {modalContent},
  } = Resources.get();

  const handleTitle = (cmtype: EContentManagementType | undefined) => {
    switch (cmtype) {
      case EContentManagementType.faq:
        return modalContent.title.faq;
      case EContentManagementType.privacyPolicy:
        return modalContent.title.privacyPolicy;
      case EContentManagementType.termsOfUse:
        return modalContent.title.termsOfUse;
      case EContentManagementType.contract:
        return modalContent.title.contract;
      case EContentManagementType.about:
        return modalContent.title.about;
    }
  };

  const handleHtml = (body: string) => {
    return `
    <!DOCTYPE html>
    <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, max-scale=1.0">
        </head>
        <body>
            ${body}
        </body>
    </html>
    `;
  };

  return (
    <ModalSimple
      titleHeader={handleTitle(type)}
      visibility={visible}
      onClose={onClose}
      height="90%">
      {data === undefined ? (
        <Box {...styles.containerLoading}>
          <Spinner size="lg" />
        </Box>
      ) : data === null ? (
        <Box {...styles.containerNotFound}>
          <Text>{modalContent.text.not_found}</Text>
        </Box>
      ) : (
        <WebView
          source={{html: handleHtml(data.content)}}
          {...styles.container}
        />
      )}
    </ModalSimple>
  );
};

export default ContentDisplay;
