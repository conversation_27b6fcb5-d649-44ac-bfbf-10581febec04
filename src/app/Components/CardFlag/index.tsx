import { types } from "credit-card-type";
import React from "react";
import { moderateScale } from "src/app/Utils/Metrics";
import { CardAmericanExpress } from "src/assets/Icons/Flaticon/Simple/CardAmericanExpress";
import { CardElo } from "src/assets/Icons/Flaticon/Simple/CardElo";
import { CardHipercard } from "src/assets/Icons/Flaticon/Simple/CardHipercard";
import { CardMastercard } from "src/assets/Icons/Flaticon/Simple/CardMastercard";
import { CardVisa } from "src/assets/Icons/Flaticon/Simple/CardVisa";

interface Props {
  cardType?: string;
  size?: { width: number; height: number };
}

const CardFlag = ({ cardType, size }: Props) => {
  const responsiveSize = {
    width: size ? moderateScale(size.width) : undefined,
    height: size ? moderateScale(size.height) : undefined,
  };
  const handleCards = (type?: string) => {
    switch (type) {
      case types.VISA:
        return size ? <CardVisa {...responsiveSize} /> : <CardVisa />;
      case types.MASTERCARD:
        return size ? <CardMastercard {...responsiveSize} /> : <CardMastercard />;
      case types.ELO:
        return size ? <CardElo {...responsiveSize} /> : <CardElo />;
      case types.HIPERCARD:
      case types.HIPER:
        return size ? <CardHipercard {...responsiveSize} /> : <CardHipercard />;
      case types.AMERICAN_EXPRESS:
        return size ? <CardAmericanExpress {...responsiveSize} /> : <CardAmericanExpress />;
      default:
        return null;
    }
  };
  return handleCards(cardType);
};

export default CardFlag;
