import {useEffect} from "react";
import {
  DeepPartial,
  useForm,
  FieldValues,
  Resolver,
  Mode,
} from "react-hook-form";

export interface IFormBaseProps<T> {
  initialValues?: DeepPartial<T & FieldValues> | undefined;
  defaultValues?: DeepPartial<T & FieldValues> | undefined;
  mode: Mode;
  resolver?: Resolver<T & FieldValues>;
}

export function useFormBase<T>({
  initialValues,
  defaultValues,
  mode,
  resolver,
}: IFormBaseProps<T>) {
  const methods = useForm<T & FieldValues>({
    mode,
    defaultValues,
    resolver,
  });

  const {trigger, getValues} = methods;

  const handleSubmitFormBase = async (callback: Function) => {
    const result = await trigger();

    if (result) {
      callback(getValues());
    }
  };

  useEffect(() => {
    methods.reset(initialValues, {
      keepDefaultValues: true,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    methods,
    handleSubmitFormBase,
  };
}
