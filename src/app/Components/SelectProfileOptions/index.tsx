import {Box, HStack, Pressable, Text} from "native-base";
import React, {useLayoutEffect, useMemo, useState} from "react";
import customStyles from "src/app/Components/SelectProfileOptions/styles";
import {ComponentsContext} from "src/app/Context/ComponentsContext";
import {Components} from "src/app/Context/Utils/Components";
import {Resources} from "src/app/Context/Utils/Resources";
import useGetUserQuantityNotification from "src/app/Modules/Main/Users/<USER>/useGetUserQuantityNotification";
import {moderateScale} from "src/app/Utils/Metrics";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useOrder from "src/app/Zustand/Store/useOrder";
import {Motorcycle, ShoppingBag, Store} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {IUserService} from "src/business/Interfaces/Services/IUser";
import {EProfile} from "src/business/Models/Profile";

interface Props {
  title?: string;
  showOptions?: string[];
  excludeOptions?: string[];
  /**
   * This function is defined when this component is used inside multistep (create account)
   */
  handleOptionChange?: (profileType: EProfile) => void;
  /**
   * This function is defined when this component is used inside a modal (select profile)
   */
  handleProfileSelect?: (profileType: EProfile) => void;
}

/**
 * This component is used by many components with differents purposes, so one improvement
 * could be to create a new component for each purpose, but for now, this is the best.
 * Another solution could be lift the state management for this component so the rules
 * behind usage of this component could be defined by the component that uses it.
 */
const SelectProfileOptions = ({
  title,
  showOptions = [],
  excludeOptions = [],
  handleOptionChange,
  handleProfileSelect,
}: Props) => {
  const styles = customStyles();
  const {selectedProfile} = useGeneralSettings();
  const userService = container.get<IUserService>(TOKENS.UserService);
  const {eraseCurrentOrderStatus} = useOrder();
  const resources = Resources.get().forms.selectProfile;

  const userQuantityNotificationQuery =
    useGetUserQuantityNotification(selectedProfile);

  const profileOptions = useMemo(() => {
    return [
      {
        type: EProfile.deliveryman,
        name: resources.profileNames.deliveryman,
        iconSize: 26,
        iconSvg: Motorcycle,
      },
      {
        type: EProfile.client,
        name: resources.profileNames.client,
        iconSize: 24,
        iconSvg: ShoppingBag,
      },
      {
        type: EProfile.shopkeeper,
        name: resources.profileNames.shopkeeper,
        iconSize: 20,
        iconSvg: Store,
      },
    ];
  }, [
    resources.profileNames.client,
    resources.profileNames.deliveryman,
    resources.profileNames.shopkeeper,
  ]);

  const options = profileOptions.filter(option => {
    if (showOptions && showOptions.length > 0) {
      return showOptions.some(showOption => showOption === option.type);
    }

    if (excludeOptions && excludeOptions.length > 0) {
      return !excludeOptions.some(
        excludeOption => excludeOption === option.type,
      );
    }

    return true;
  });

  const hasAllProfiles = options.length === 0;

  const defineSelectedProfile = () => {
    if (options.length === 1) return options[0].type;
    if (selectedProfile) return selectedProfile;
    if (options.length === 3) return EProfile.client;
    return options[0].type;
  };

  const [selected, setSelected] = useState(defineSelectedProfile());

  const makeSureCorrectSchemaWillBeUsed = () => {
    if (handleOptionChange && selected) {
      handleOptionChange(selected);
    }
  };

  useLayoutEffect(() => {
    makeSureCorrectSchemaWillBeUsed();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOptionPress = (profileType: EProfile) => {
    setSelected(profileType);
    if (handleOptionChange) handleOptionChange(profileType);
    if (handleProfileSelect) handleProfileSelect(profileType);
    userService.updateDefaultProfile(profileType);
    userQuantityNotificationQuery.refetch();
    eraseCurrentOrderStatus();
  };

  return (
    <>
      {title && !hasAllProfiles ? (
        <Text {...styles.select}>{title}</Text>
      ) : null}
      <HStack {...styles.optionsContainer}>
        {options.length > 0 ? (
          options.map(({iconSvg: Icon, iconSize, name, type}) => {
            let optionContainer;
            let iconContainer;
            let optionName;
            let iconColor;
            let sizeIncreaseCoefficient = 1;
            if (type === selected) {
              optionContainer = styles.optionContainerSelected;
              iconContainer = styles.iconContainerSelected;
              optionName = styles.optionNameSelected;
              iconColor = styles.iconSelected.style;
              sizeIncreaseCoefficient = 1.3;
            } else {
              optionContainer = styles.optionContainer;
              iconContainer = styles.iconContainer;
              optionName = styles.optionName;
              iconColor = styles.icon.style;
            }
            return (
              <Pressable
                _pressed={{opacity: 0.5}}
                {...optionContainer}
                key={type}
                onPress={() => handleOptionPress(type)}>
                <Box {...iconContainer}>
                  <Icon
                    width={moderateScale(iconSize * sizeIncreaseCoefficient)}
                    height={moderateScale(iconSize * sizeIncreaseCoefficient)}
                    {...iconColor}
                  />
                </Box>

                <Text {...optionName}>{name}</Text>
              </Pressable>
            );
          })
        ) : (
          <Text>{resources.noOptions}</Text>
        )}
      </HStack>
    </>
  );
};

export default SelectProfileOptions;
