import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainBox: {
      style: {
        width: wp("100%"),
        height: hp("100%"),
        backgroundColor: ThemesApp.getTheme().colors.modalProgressBg,
        justifyContent: "center",
        alignItems: "center",
        position: "absolute",
        zIndex: 1000,
      },
    },
  };
};

export default customStyles;
