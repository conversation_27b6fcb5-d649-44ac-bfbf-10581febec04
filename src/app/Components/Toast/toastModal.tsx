import { Button, Toast } from "native-base";
import React from "react";
import { PERMISSIONS } from "react-native-permissions";
import ModalRN from "src/app/Components/Modal";
import customStyles from "src/app/Components/Toast/styles";
import { Resources } from "src/app/Context/Utils/Resources";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { handleOneAppPermission } from "src/app/Utils/Permissions";
import { IToastProps } from "src/business/Interfaces/IToastProps";

const showToastModal = (title: string, description: string | undefined, params?: IToastProps) => {
  const resources = Resources.get().orders;
  const styles = customStyles();
  Toast.show({
    description,
    placement: params?.placement || "top",
    duration: params?.duration || null,
    bgColor: ThemesApp.getTheme().colors.primary[500],
    render() {
      return (
        <ModalRN
          title={title}
          visibility={true}
          onClose={() => Toast.closeAll()}
          componentFooter={
            <Button.Group direction="row">
              <Button onPress={() => Toast.closeAll()} {...styles.btnNegative}>
                {resources.button.cancel}
              </Button>
              <Button
                onPress={() => {
                  Toast.closeAll();
                  handleOneAppPermission({
                    android: PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION,
                  });
                }}
                {...styles.btnPositive}
              >
                {resources.button.accept}
              </Button>
            </Button.Group>
          }
        >
          {description}
        </ModalRN>
      );
    },
  });
};

export default showToastModal;
