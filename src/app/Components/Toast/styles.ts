import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    btnPositive: {
      style: {
        width: wp("30%"),
      },
    },

    btnNegative: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.error,
      },
    },
  };
};

export default customStyles;
