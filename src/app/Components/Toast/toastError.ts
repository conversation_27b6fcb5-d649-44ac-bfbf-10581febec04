import { Toast } from "native-base";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { IToastProps } from "src/business/Interfaces/IToastProps";

const showToastError = (description: string | undefined, params?: IToastProps) => {
  Toast.show({
    description,
    placement: params?.placement || "bottom",
    duration: params?.duration || 2000,
    bgColor: ThemesApp.getTheme().colors.red[600],
  });
};

export default showToastError;
