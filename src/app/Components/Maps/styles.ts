import { Dimensions } from "react-native";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const { width, height } = Dimensions.get("screen");

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        flex: 1,
        /*  backgroundColor: "red",
        width: "100%",
        height, */
      },
    },
    map: {
      style: {
        width,
        height,
      },
    },
  };
};

export default customStyles;
