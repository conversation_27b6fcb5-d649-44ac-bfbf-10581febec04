import {useEffect, useState} from "react";
import {Region} from "react-native-maps";
import Geolocation from "react-native-geolocation-service";
import {Location} from "src/business/DTOs/Location";
import {
  LATITUDE_DELTA,
  LONGITUDE_DELTA,
} from "src/app/Utils/Address/CoordsUtilities";

/* eslint-disable import/prefer-default-export */
export const useMap = () => {
  const [region, setRegion] = useState<Region | undefined>();
  const [location, setLocation] = useState<Location>();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    Geolocation.getCurrentPosition(
      info => {
        setLocation(info.coords);

        setRegion({
          latitude: info.coords.latitude,
          longitude: info.coords.longitude,
          latitudeDelta: LATITUDE_DELTA,
          longitudeDelta: LONGITUDE_DELTA,
        });
      },
      error => {
        console.log("GetCurrentLocation error: ", error);
      },
      {
        enableHighAccuracy: true,
        timeout: 2000,
      },
    );
  }, []);

  return {
    region,
    location,
    LATITUDE_DELTA,
    LONGITUDE_DELTA,
    setRegion,
    setIsLoading,
    isLoading,
  };
};
