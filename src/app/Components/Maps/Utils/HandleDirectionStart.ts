import MapView from "react-native-maps";
import { MapDirectionsResponse } from "react-native-maps-directions";
import { Dimensions } from "react-native";

const { width, height } = Dimensions.get("screen");

/* eslint-disable import/prefer-default-export */
export const handleDirectionStart = (result: MapDirectionsResponse, mapview: React.RefObject<MapView>) => {
  console.log(`Distance ${result.distance} km`);
  console.log(`Duration ${result.duration} min`);

  if (mapview.current) {
    mapview.current?.fitToCoordinates(result.coordinates, {
      edgePadding: {
        right: (width / 8),
        bottom: (height / 8),
        left: (width / 8),
        top: (height / 8),
      },
      animated: true
    });

  }


};
