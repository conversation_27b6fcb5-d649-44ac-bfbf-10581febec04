import { Box } from "native-base";
import React, { useEffect, useRef } from "react";
import { Dimensions } from "react-native";
import Config from "react-native-config";

import MapView, { MapViewProps, Marker } from "react-native-maps";
import MapViewDirections from "react-native-maps-directions";
import { useMap } from "src/app/Components/Maps/Hooks/useMap";
import { Location } from "src/business/DTOs/Location";

import customStyles from "src/app/Components/Maps/styles";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const { width, height } = Dimensions.get("screen");

type Props = MapViewProps & {
  calcRoute?: {
    origin?: Location;
    destination?: Location;
  };
  marker?: Location;
};

const Maps: React.FC<React.PropsWithChildren<Props>> = ({
  calcRoute,
  marker,
  children,
  ...rest
}) => {
  const styles = customStyles();
  const { region, setRegion, LATITUDE_DELTA, LONGITUDE_DELTA } = useMap();
  const mapView = useRef<MapView>(null);

  useEffect(() => {
    if (calcRoute) {
      setRegion({
        latitude: calcRoute.origin!.latitude,
        longitude: calcRoute.origin!.longitude,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      });
    } else if (marker) {
      setRegion({
        latitude: marker.latitude,
        longitude: marker.longitude,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      });
    }
  }, [marker, calcRoute, setRegion, LATITUDE_DELTA, LONGITUDE_DELTA]);

  return (
    <Box {...styles.container}>
      <MapView
        initialRegion={region}
        showsUserLocation={true}
        style={{
          width,
          height,
        }}
        minZoomLevel={1}
        maxZoomLevel={20}
        loadingEnabled={true}
        ref={mapView}
        {...rest}>
        {calcRoute ? (
          <>
            <MapViewDirections
              apikey={Config.ANDROID_MAPS_KEY!}
              origin={calcRoute.origin}
              destination={calcRoute.destination}
              strokeWidth={5}
              strokeColor={ThemesApp.getTheme().colors.secondary[200]}
            />
            <Marker
              coordinate={{
                latitude: calcRoute.origin!.latitude,
                longitude: calcRoute.origin!.longitude,
              }}
            />
            <Marker
              coordinate={{
                latitude: calcRoute.destination!.latitude,
                longitude: calcRoute.destination!.longitude,
              }}
            />
          </>
        ) : null}
        {children}
        {marker?.latitude ? (
          <Marker
            coordinate={{
              latitude: marker.latitude,
              longitude: marker.longitude,
            }}
          />
        ) : null}
      </MapView>
    </Box>
  );
};

export default Maps;
