import { TreeViewOptions } from "src/business/DTOs/TreeViewOptions";
import { TreeViewList } from "src/business/DTOs/TreeViewList";

const formatTreeViewList = (category: TreeViewList[]) => {
  const resultData = category.filter((categoryItem) => categoryItem.checked === true);
  resultData.map((categoryList) => {
    const subcategoryList = categoryList.subcategory.filter((currentOption) => currentOption.checked === true);
    return {
      id: categoryList.id,
      name: categoryList.name,
      checked: categoryList.checked,
      subcategory: subcategoryList,
    };
  });
  return resultData;
};

const formatTreeViewOptions = (data: TreeViewList[]) => {
  let subcategories: TreeViewOptions[] = [];

  data.map((item) => {
    const result = item.subcategory.filter((subcategory) => subcategory.checked === true);
    subcategories = subcategories.concat(result);
    return item;
  });

  return subcategories;
};

const setSelectOptions = (category?: TreeViewList[]) => {
  let result = category;
  result = result?.map((item) => {
    item.subcategory = item.subcategory.filter((subcategoryItem) => subcategoryItem.checked === true);
    return item;
  });
  result = result?.filter((item) => {
    return item.checked === true;
  });
  return result;
};

const openTreeViewList = (id: string, category: TreeViewList[]) => {
  const result = category.map((item) => {
    if (item.id === id) {
      item.isOpen = !item.isOpen;
    }
    return item;
  });
  return result;
};

const checkTreeViewOption = (
  identifiers: { categoryId?: string; subcategoryId?: string },
  categoryData: TreeViewList[],
) => {
  const { categoryId, subcategoryId } = identifiers;

  const result = categoryData.map((category) => {
    if (category.id === categoryId && subcategoryId === undefined) {
      category.checked = !category.checked;
      category.subcategory = category.subcategory.map((option) => {
        option.checked = category.checked;
        return option;
      });
    }

    if (category.id === categoryId && subcategoryId !== undefined) {
      category.subcategory.map((subcategoryItem: TreeViewOptions) => {
        if (subcategoryItem.id === subcategoryId) {
          subcategoryItem.checked = !subcategoryItem.checked;
        }
        return subcategoryItem;
      });
    }

    if (category.subcategory.length > 0) {
      category.checked = !!category.subcategory.find((selectedOption) => selectedOption.checked === true);
    }
    return category;
  });
  return result;
};

const getSelectedCategories = (category: TreeViewList[]) => {
  const selectCategories = category
    .filter((c) => c.checked === true)
    .map((c) => {
      return { ...c, subcategory: c.subcategory.filter((sc) => sc.checked === true) };
    });
  return selectCategories;
};

const setSelectedCategoriesInTreeViewData = (
  selectedCategory: TreeViewList[] | undefined,
  treeViewData: TreeViewList[],
) => {
  const dataResult = treeViewData.map((c) => {
    const category = selectedCategory ? selectedCategory.find((item) => item.id === c.id) : undefined;
    if (category) {
      return {
        ...c,
        checked: true,
        subcategory: c.subcategory.map((sc) => {
          const subCategory = category.subcategory.find((item) => item.id === sc.id);
          if (subCategory) {
            return { ...sc, checked: true };
          }
          return { ...sc, checked: false };
        }),
      };
    }
    return { ...c, checked: false, subcategory: c.subcategory.map((item) => ({ ...item, checked: false })) };
  });
  return dataResult;
};

export {
  formatTreeViewList,
  formatTreeViewOptions,
  setSelectOptions,
  checkTreeViewOption,
  openTreeViewList,
  getSelectedCategories,
  setSelectedCategoriesInTreeViewData,
};
