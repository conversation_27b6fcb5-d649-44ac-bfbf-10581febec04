import {cloneDeep} from "lodash";
import {
  Box,
  FlatList,
  FormControl,
  HStack,
  Pressable,
  ScrollView,
  Text,
  View,
} from "native-base";
import React, {useState} from "react";
import {useFormContext} from "react-hook-form";
import CheckboxTreeModal from "src/app/Components/TreeView/Components/Modal";
import TreeViewListComponent from "src/app/Components/TreeView/Components/TreeViewList";
import customStyles from "src/app/Components/TreeView/styles";
import {
  openTreeViewList,
  setSelectedCategoriesInTreeViewData,
  setSelectOptions,
} from "src/app/Components/TreeView/Utils/FormatTreeViewData";
import {Plus, WarningOutline} from "src/assets/Icons/Flaticon";
import {IFormTreeView} from "src/business/DTOs/Forms/TreeView";
import {TreeViewList} from "src/business/DTOs/TreeViewList";

interface IComponentProps {
  label: string;
  placeholder: string;
  options: TreeViewList[];
  cancelButtonText: string;
  saveButtonText: string;
  margin?: string;
  action: "create" | "update";
}

const CheckboxTree = ({
  cancelButtonText,
  label,
  options,
  placeholder,
  saveButtonText,
  margin,
  action,
}: IComponentProps) => {
  const {
    getValues,
    formState: {errors},
  } = useFormContext<IFormTreeView>();

  const styles = customStyles();

  const [treeViewData, setTreeViewData] = useState(
    action === "create"
      ? setSelectedCategoriesInTreeViewData(getValues("category"), options)
      : options,
  );
  const [modalVisibility, setModalVisibility] = useState(false);

  const optionsSelected = setSelectOptions(cloneDeep(treeViewData));

  const onOpen = (id: string) => {
    const result = openTreeViewList(id, cloneDeep(treeViewData));
    setTreeViewData(result);
  };

  const handleModal = () => {
    setModalVisibility(!modalVisibility);
  };

  return (
    <FormControl
      isRequired={true}
      paddingLeft={margin || 0}
      isInvalid={!!errors}>
      <FormControl.Label>{label}</FormControl.Label>
      <View {...styles.container}>
        <Pressable
          onPress={() => {
            handleModal();
          }}
          {...styles.pressableCategory}>
          <HStack {...styles.hStack}>
            <Text {...styles.textSelectedCategory}>{placeholder}</Text>
            <Plus {...styles.iconPlus.style} />
          </HStack>
        </Pressable>

        {errors.category?.message &&
        (!optionsSelected || optionsSelected.length <= 0) ? (
          <FormControl.ErrorMessage
            leftIcon={<WarningOutline {...styles.warningOutlineIcon.style} />}>
            {errors.category?.message}
          </FormControl.ErrorMessage>
        ) : null}

        {optionsSelected && (
          <Box {...styles.listContainer}>
            <ScrollView horizontal={true}>
              <FlatList
                data={optionsSelected}
                keyExtractor={item => item.id}
                renderItem={({item}) => (
                  <TreeViewListComponent data={item} onOpen={onOpen} viewMode />
                )}
              />
            </ScrollView>
          </Box>
        )}

        <CheckboxTreeModal
          isVisible={modalVisibility}
          placeholder={placeholder}
          cancelButtonText={cancelButtonText}
          saveButtonText={saveButtonText}
          treeViewData={treeViewData}
          setTreeViewData={setTreeViewData}
          onOpen={onOpen}
          onClose={handleModal}
        />
      </View>
    </FormControl>
  );
};

export default CheckboxTree;
