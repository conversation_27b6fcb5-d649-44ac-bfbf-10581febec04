import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils//Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    formControl: {
      style: {
        paddingHorizontal: wp("5%"),
      },
    },
    container: {
      style: {
        marginBottom: verticalScale(20),
        marginTop: verticalScale(5),
      },
    },
    listContainer: {
      style: {
        marginBottom: verticalScale(10),
        marginTop: verticalScale(10),
      },
    },
    name: {
      style: {
        fontSize: RFValue(20),
        lineHeight: RFValue(22),
        marginLeft: horizontalScale(8),
      },
    },
    blankSpace: {
      style: {
        marginLeft: horizontalScale(32),
      },
    },
    categoryContainer: {
      style: {
        justifyContent: "center",
      },
    },
    subCategoryContainer: {
      style: {
        flexDirection: "row",
        marginLeft: horizontalScale(20),
      },
    },
    iconCheckSelect: {
      size: moderateScale(17),
      style: {
        marginRight: horizontalScale(3),
      },
    },
    textCategory: {
      style: {
        marginLeft: horizontalScale(6),
        color: ThemesApp.getTheme().colors.textCategories,
        fontSize: RFValue(18),
        lineHeight: RFValue(20),
        fontWeight: "bold",
      },
    },
    textSubcategory: {
      style: {
        marginLeft: horizontalScale(6),
        color: ThemesApp.getTheme().colors.textCategories,
        fontSize: RFValue(18),
        lineHeight: RFValue(20),
      },
    },
    hStack: {
      style: {
        justifyContent: "space-between",
      },
    },
    pressable: {
      style: {
        width: "100%",
        height: verticalScale(48),
        borderBottomWidth: moderateScale(2),
        borderColor: ThemesApp.getTheme().colors.pressableCategories,
        borderRadius: moderateScale(5),
        justifyContent: "space-evenly",
      },
    },
    boxSelect2: {
      style: {
        width: wp("83%"),
      },
    },
    dividerDetails: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.primary[50],
        height: verticalScale(20),
        marginHorizontal: horizontalScale(7),
        width: horizontalScale(3),
      },
    },
    boxSubacategory2: {
      style: {
        marginLeft: horizontalScale(20),
      },
    },
    textSelectedCategory: {
      style: {
        marginLeft: horizontalScale(14),
        color: ThemesApp.getTheme().colors.pressableCategories,
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },
    pressableCategory: {
      style: {
        width: wp("90%"),
        height: RFValue(48),
        borderWidth: moderateScale(2),
        borderColor: ThemesApp.getTheme().colors.pressableCategories,
        borderRadius: moderateScale(5),
        justifyContent: "space-evenly",
      },
    },
    iconPlus: {
      style: {
        fill: ThemesApp.getTheme().colors.accordionStatusText,
        width: moderateScale(20),
        height: moderateScale(20),
        marginRight: horizontalScale(6),
      },
    },
    modal: {
      size: "full",
      style: {
        justifyContent: "flex-end",
      },
    },
    modalContent: {
      style: {
        height: hp("67%"),
        borderTopLeftRadius: moderateScale(15),
        borderTopRightRadius: moderateScale(15),
      },
    },

    modalCloseButton: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
      },
    },

    modalHeader: {
      style: { alignSelf: "center" },
    },

    modalHeaderText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    boxModal: {
      style: {
        margin: wp("5%"),
        height: wp("85%"),
      },
    },
    boxButtons: {
      style: {
        position: "absolute",
        bottom: RFValue(0),
        alignItems: "center",
        marginBottom: RFValue(8),
        width: wp("100%"),
      },
    },

    buttonBottom: {
      style: {
        width: wp("45%"),
      },
    },

    iconSearch: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        marginRight: horizontalScale(8),
        fill: ThemesApp.getTheme().colors.iconHelp,
      },
    },

    warningOutlineIcon: {
      style: {
        width: moderateScale(13),
        height: moderateScale(13),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },
  };
};

export default customStyles;
