import { Resources } from "src/app/Context/Utils/Resources";
import ZodString from "src/app/Utils/Zod/ZodString";
import { z } from "zod";

const getSchemaCreateCheckboxTree = () => {
  const resources = Resources.get();
  const schema = {
    category: z
      .array(
        z.object({
          id: ZodString(),
          name: ZodString(),
          checked: z.boolean(),
          subcategory: z.array(
            z.object({
              id: ZodString(),
              name: ZodString(),
              checked: z.boolean(),
            }),
          ),
          /* .nonempty({
              message: resources.generic.errors.forbidden_exception,
            }) */
        }),
      )
      .nonempty({ message: resources.generic.errors.required }),
    // subcategory: z
    //   .array(
    //     z
    //       .object({ id: ZodString(), name: ZodString(), checked: z.boolean() })
    //       .required()
    //   )
    //   .nonempty({ message: resources.generic.errors.required }),
  };

  return schema;
};

export default getSchemaCreateCheckboxTree;
