import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    categoryContainer: {
      style: {
        justifyContent: "center",
        alignItems: "center",
      },
    },
    iconCheckSelect: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        marginRight: horizontalScale(3),
      },
    },
    textCategory: {
      style: {
        color: ThemesApp.getTheme().colors.textCategories,
        marginLeft: horizontalScale(6),
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "bold",
        textTransform: "capitalize",
      },
    },
    textSubcategory: {
      style: {
        color: ThemesApp.getTheme().colors.textCategories,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        marginLeft: horizontalScale(6),
        textTransform: "capitalize",
      },
    },
    hStack: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    pressable: {
      style: {
        width: "100%",
        height: verticalScale(48),
        borderBottomWidth: moderateScale(2),
        borderColor: ThemesApp.getTheme().colors.pressableCategories,
        borderRadius: moderateScale(5),
        justifyContent: "space-evenly",
      },
    },
    boxSelect2: {
      style: {
        width: wp("83%"),
      },
    },
    dividerDetails: {
      style: {
        width: moderateScale(10),
        height: moderateScale(10),
        marginRight: horizontalScale(3),
        marginBottom: verticalScale(6),
        fill: ThemesApp.getTheme().colors.textCategories,
      },
    },
    boxSubcategory: {
      style: {
        marginLeft: horizontalScale(20),
      },
    },
    hStackCenter: {
      style: {
        alignItems: "center",
      },
    },

    fullCheckIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.checkedIcon,
        width: moderateScale(22),
        height: moderateScale(22),
      },
    },
    partialCheckIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.iconHelp,
        width: moderateScale(20),
        height: moderateScale(20),
      },
    },
    checkIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.squareIcon,
        width: moderateScale(20),
        height: moderateScale(20),
      },
    },

    textCategoryCheckedItems: {
      style: {
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        color: ThemesApp.getTheme().colors.white,
      },
    },

    boxCategories: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.primary[300],
        borderRadius: moderateScale(99),
        paddingVertical: verticalScale(2),
        paddingHorizontal: horizontalScale(6),
      },
    },
  };
};

export default customStyles;
