import { Box, HStack, Text } from "native-base";
import React from "react";
import { Pressable, View } from "react-native";
import customStyles from "src/app/Components/TreeView/Components/TreeViewList/styles";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import {
  Checkbox,
  Checklist,
  FullChecklist,
  L,
  PartialChecklist,
  Square,
  SquareMinus,
  SquarePlus,
} from "src/assets/Icons/Flaticon";
import { TreeViewList } from "src/business/DTOs/TreeViewList";
import { TreeViewOptions } from "src/business/DTOs/TreeViewOptions";

type IProps = {
  data: TreeViewList;
  onOpen: (id: string) => void;
  onCheck?: (categoryId?: string, subcategoryId?: string) => void;
  viewMode?: boolean;
};

const TreeViewListComponent = ({ onOpen, data, onCheck, viewMode = false }: IProps) => {
  const styles = customStyles();

  const renderTreeViewCategoriesIcon = () => {
    if (viewMode) {
      return (
        <Box {...styles.boxCategories}>
          <Text {...styles.textCategoryCheckedItems}>{data.subcategory.length}</Text>
        </Box>
      );
    }

    if (data.subcategory.filter((option) => option.checked).length === data.subcategory.length) {
      return <FullChecklist {...styles.fullCheckIcon.style} />;
    }

    if (
      data.subcategory.filter((option) => option.checked).length < data.subcategory.length &&
      data.subcategory.filter((option) => option.checked).length > 0 &&
      data.checked
    ) {
      return <PartialChecklist {...styles.partialCheckIcon.style} />;
    }

    if (data.subcategory.filter((option) => option.checked).length === 0) {
      return <Checklist {...styles.checkIcon.style} />;
    }
  };

  return (
    <View>
      <HStack {...styles.categoryContainer}>
        <Pressable onPress={() => onOpen(data.id)}>
          {data.isOpen ? (
            <SquareMinus
              fill={data.subcategory.length > 0 ? ThemesApp.getTheme().colors.iconHelp : "transparent"}
              {...styles.iconCheckSelect.style}
            />
          ) : (
            <SquarePlus
              fill={data.subcategory.length > 0 ? ThemesApp.getTheme().colors.iconHelp : "transparent"}
              {...styles.iconCheckSelect.style}
            />
          )}
        </Pressable>

        <Box {...styles.boxSelect2}>
          <Pressable onPress={onCheck ? () => onCheck(data.id) : () => {}} {...styles.pressable} disabled={!onCheck}>
            <HStack {...styles.hStack}>
              <HStack {...styles.hStackCenter}>
                <Text {...styles.textCategory}>{data.name}</Text>
              </HStack>

              {renderTreeViewCategoriesIcon()}
            </HStack>
          </Pressable>
        </Box>
      </HStack>
      {data.isOpen &&
        data.subcategory.map((subcategory: TreeViewOptions) => (
          <View key={subcategory.id} {...styles.boxSubcategory}>
            <Box>
              <Pressable
                onPress={onCheck ? () => onCheck(data.id, subcategory.id) : () => {}}
                {...styles.pressable}
                disabled={!onCheck}
              >
                <HStack {...styles.hStack}>
                  <HStack {...styles.hStackCenter}>
                    <L {...styles.dividerDetails.style} />
                    <Text {...styles.textSubcategory}>{subcategory.name}</Text>
                  </HStack>

                  {!viewMode ? (
                    subcategory.checked && data.checked ? (
                      <Checkbox
                        fill={
                          subcategory.checked && data.checked
                            ? ThemesApp.getTheme().colors.checkedIcon
                            : ThemesApp.getTheme().colors.squareIcon
                        }
                        {...styles.iconCheckSelect.style}
                      />
                    ) : (
                      <Square
                        fill={
                          subcategory.checked && data.checked
                            ? ThemesApp.getTheme().colors.checkedIcon
                            : ThemesApp.getTheme().colors.squareIcon
                        }
                        {...styles.iconCheckSelect.style}
                      />
                    )
                  ) : null}
                </HStack>
              </Pressable>
            </Box>
          </View>
        ))}
    </View>
  );
};

export default TreeViewListComponent;
