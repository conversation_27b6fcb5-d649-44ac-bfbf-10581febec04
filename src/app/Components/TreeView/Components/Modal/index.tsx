import {cloneDeep} from "lodash";
import {Box, Button, FlatList, FormControl, Input, Modal} from "native-base";
import React, {useState} from "react";
import {useFormContext} from "react-hook-form";
import TreeViewListComponent from "src/app/Components/TreeView/Components/TreeViewList";
import customStyles from "src/app/Components/TreeView/Components/Modal/styles";
import {
  checkTreeViewOption,
  getSelectedCategories,
} from "src/app/Components/TreeView/Utils/FormatTreeViewData";
import {Close, Search} from "src/assets/Icons/Flaticon";
import {IFormTreeView} from "src/business/DTOs/Forms/TreeView";
import {TreeViewList} from "src/business/DTOs/TreeViewList";

type Props = {
  isVisible: boolean;
  placeholder: string;
  cancelButtonText: string;
  saveButtonText: string;
  treeViewData: TreeViewList[];
  setTreeViewData: (value: React.SetStateAction<TreeViewList[]>) => void;
  onOpen: (id: string) => void;
  onClose: () => void;
};

const CheckboxTreeModal = ({
  isVisible,
  placeholder,
  cancelButtonText,
  saveButtonText,
  treeViewData,
  setTreeViewData,
  onOpen,
  onClose,
}: Props) => {
  const {setValue} = useFormContext<IFormTreeView>();

  const styles = customStyles();
  const [searchInput, setSearchInput] = useState("");

  const optionsFiltered = searchInput.length
    ? treeViewData.filter(option =>
        option.name.toLowerCase().includes(searchInput.toLowerCase()),
      )
    : treeViewData;

  const handleSubmit = () => {
    setValue("category", getSelectedCategories(treeViewData));
  };

  const onCheck = (categoryId?: string, subcategoryId?: string) => {
    const result = checkTreeViewOption(
      {categoryId, subcategoryId},
      cloneDeep(treeViewData),
    );
    setTreeViewData(result);
  };

  return (
    <Modal isOpen={isVisible} {...styles.modal}>
      <Modal.Content {...styles.modalContent}>
        <Modal.CloseButton
          icon={<Close />}
          _icon={{style: styles.modalCloseButton.style}}
          onPress={() => {
            onClose();
          }}
        />
        <Modal.Header
          {...styles.modalHeader}
          _text={{...styles.modalHeaderText}}>
          {placeholder}
        </Modal.Header>
        <Box {...styles.boxModal}>
          <FormControl marginBottom={5}>
            <Input
              value={searchInput}
              onChangeText={text => {
                setSearchInput(text);
              }}
              placeholder={placeholder}
              InputRightElement={<Search {...styles.iconSearch.style} />}
              isRequired={false}
            />
          </FormControl>
          <FlatList
            showsVerticalScrollIndicator={false}
            data={optionsFiltered}
            keyExtractor={item => item.id}
            renderItem={({item}) => {
              return (
                <TreeViewListComponent
                  data={item}
                  onOpen={onOpen}
                  onCheck={onCheck}
                />
              );
            }}
          />
        </Box>
        <Box {...styles.boxButtons}>
          <Button.Group space={4}>
            <Button
              variant="outline"
              onPress={() => {
                onClose();
              }}
              {...styles.buttonBottom}>
              {cancelButtonText}
            </Button>
            <Button
              onPress={() => {
                handleSubmit();
                onClose();
              }}
              {...styles.buttonBottom}>
              {saveButtonText}
            </Button>
          </Button.Group>
        </Box>
      </Modal.Content>
    </Modal>
  );
};

export default CheckboxTreeModal;
