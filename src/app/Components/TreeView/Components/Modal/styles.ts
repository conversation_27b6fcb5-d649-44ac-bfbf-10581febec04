import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {horizontalScale, moderateScale} from "src/app/Utils//Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    modal: {
      size: "full",
      style: {
        justifyContent: "flex-end",
      },
    },
    modalContent: {
      style: {
        height: hp("67%"),
        borderTopLeftRadius: moderateScale(15),
        borderTopRightRadius: moderateScale(15),
      },
    },
    modalCloseButton: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
      },
    },
    modalHeader: {
      style: {alignSelf: "center"},
    },
    modalHeaderText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    boxModal: {
      style: {
        margin: wp("5%"),
        height: wp("85%"),
      },
    },
    iconSearch: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        marginRight: horizontalScale(8),
        fill: ThemesApp.getTheme().colors.iconHelp,

      },
    },
    boxButtons: {
      style: {
        position: "absolute",
        bottom: RFValue(0),
        alignItems: "center",
        marginBottom: RFValue(8),
        width: wp("100%"),
      },
    },
    buttonBottom: {
      style: {
        width: wp("45%"),
      },
    },
  };
};

export default customStyles;
