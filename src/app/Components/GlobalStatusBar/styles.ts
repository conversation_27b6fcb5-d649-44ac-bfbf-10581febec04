import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { RFValue } from "react-native-responsive-fontsize";
import { moderateScale } from "src/app/Utils/Metrics";
import { Platform, StatusBar } from "react-native";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    container: {
        style: {
          flex: 1,
          backgroundColor: ThemesApp.getTheme().colors.background
        }
    }
  };
};

export default customStyles;
