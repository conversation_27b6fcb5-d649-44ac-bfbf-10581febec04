import React, {ReactNode, useContext} from "react";
import {StatusBar} from "react-native";
import {SafeAreaView} from "react-native-safe-area-context";
import {ThemeContext} from "src/app/Context/ThemeContext";
import customStyles from "./styles";

interface Props {
  children: ReactNode;
}

const GlobalStatusBar = ({children}: Props) => {
  const styles = customStyles();
  const {themeApp} = useContext(ThemeContext);

  return (
    <>
      <StatusBar
        backgroundColor={themeApp.colors.background}
        barStyle={
          themeApp.colors.background === "white"
            ? "dark-content"
            : "light-content"
        }
      />
      <SafeAreaView edges={["top", "left", "right"]} {...styles.container}>
        {children}
      </SafeAreaView>
    </>
  );
};

export default GlobalStatusBar;
