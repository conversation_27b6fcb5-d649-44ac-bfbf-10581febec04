import {Modal, Text} from "native-base";
import React from "react";
import customStyles from "src/app/Components/Modal/styles";
import {Close} from "src/assets/Icons/Flaticon";

interface ModalProps {
  title?: string;
  visibility: boolean;
  onClose: any;
  componentFooter?: React.ReactNode;
}
const ModalRN: React.FC<React.PropsWithChildren<ModalProps>> = ({
  title,
  children,
  visibility,
  componentFooter,
  onClose,
}) => {
  const styles = customStyles();
  return (
    <Modal
      isOpen={visibility}
      onClose={onClose}
      closeOnOverlayClick={false}
      style={{position: "absolute"}}>
      <Modal.Content {...styles.modalContent}>
        <Modal.CloseButton
          icon={<Close />}
          _icon={{style: styles.modalCloseButton.style}}
          {...styles.modalClose}
        />
        <Modal.Header {...styles.modalHeader}>
          <Text
            ellipsizeMode="tail"
            numberOfLines={3}
            {...styles.modalTextHeader}>
            {title}
          </Text>
        </Modal.Header>

        <Modal.Body {...styles.modalBody}>{children}</Modal.Body>
        {componentFooter && (
          <Modal.Footer {...styles.modalFooter}>{componentFooter}</Modal.Footer>
        )}
      </Modal.Content>
    </Modal>
  );
};

export default ModalRN;
