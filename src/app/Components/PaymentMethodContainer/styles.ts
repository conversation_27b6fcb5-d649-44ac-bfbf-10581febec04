import { RFValue } from "react-native-responsive-fontsize";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { horizontalScale, moderateScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    container: {
      w: "full",
    },

    containerCard: {
      style: {
        flex: 1,
        alignItems: "center",
      },
    },

    boxFlag: {
      rounded: "full",
      style: {
        alignContent: "center",
        justifyContent: "center",
        minWidth: horizontalScale(60),
        minHeight: horizontalScale(60),
        marginRight: wp("5%"),
        marginLeft: wp("2%"),
        paddingHorizontal: wp("1%"),
        backgroundColor: ThemesApp.getTheme().colors.muted[200],
      },
    },

    flag: {
      style: {
        width: moderateScale(40),
        height: moderateScale(40),
      },
    },

    pressable: {
      flex: 1,
      style: {
        height: hp("8%"),
        borderBottomColor: ThemesApp.getTheme().colors.muted[300],
        borderBottomWidth: 1,
      },
    },

    vstackCard: {
      flex: 1,
    },

    hstackCardBody: {
      // flex: 1,
      style: {
        justifyContent: "flex-start",
        alignItems: "center",
      },
    },

    cardNumberText: {
      style: {
        fontWeight: "bold",
        textTransform: "uppercase",
        fontSize: RFValue(16),
        lineHeight: RFValue(18),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    cardHolderText: {
      style: {
        textTransform: "uppercase",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    expirationText: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    expiredText: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.heartIcon,
      },
    },

    flagText: {
      style: {
        fontWeight: "bold",
        textTransform: "uppercase",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    checkbox: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.checkedIcon,
      },
    },

    square: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.squareIcon,
      },
    },
  };
};

export default customStyles;
