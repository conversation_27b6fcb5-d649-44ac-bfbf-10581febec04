import { maskField } from "input-mask-native-base-rhf";
import { Box, HStack, Text, VStack } from "native-base";
import React from "react";
import CardFlag from "src/app/Components/CardFlag";
import customStyles from "src/app/Components/PaymentMethodContainer/styles";
import { Resources } from "src/app/Context/Utils/Resources";
import { isCardExpired } from "src/app/Utils/IsCardExpired";
import DATE_MMYY from "src/app/Utils/MaskDateMMYY";
import ECardMethod from "src/business/Enums/Models/ECardMethod";
import { Card } from "src/business/Models/Card";

interface Props {
  card: Card;
}
const OPTION_CREDIT = 0;
const OPTION_DEBT = 1;

const PaymentMethodContainer = ({ card }: Props) => {
  const styles = customStyles();
  const {
    forms: {
      card: { select },
    },
    orders: { orderPaymentMethods },
  } = Resources.get();

  return (
    <Box {...styles.container}>
      <HStack {...styles.containerCard}>
        <Box {...styles.boxFlag}>
          <CardFlag cardType={card.flag} />
        </Box>
        <VStack {...styles.vstackCard}>
          <HStack {...styles.hstackCardBody} space={3}>
            <Text {...styles.cardHolderText}>
              ({select.paymentMethod.options[card.method === ECardMethod.credit ? OPTION_CREDIT : OPTION_DEBT]})
            </Text>
            <Text {...styles.cardNumberText}>{` ${card.cardNumberLastDigits}`.padStart(9, "*")}</Text>
          </HStack>
          <Text {...styles.cardHolderText}>{card.cardHolder}</Text>
          {isCardExpired(card.expiration) ? (
            <Text {...styles.expiredText}>
              {orderPaymentMethods.label.expired_on + maskField({ mask: DATE_MMYY, value: card.expiration }).masked}
            </Text>
          ) : (
            <Text {...styles.expirationText}>
              {orderPaymentMethods.label.expires_in + maskField({ mask: DATE_MMYY, value: card.expiration }).masked}
            </Text>
          )}
        </VStack>
      </HStack>
    </Box>
  );
};

export default PaymentMethodContainer;
