import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        marginBottom: hp("4%"),
      },
    },
    loadingContainer: {
      justifyContent: "center",
      alignItems: "center",
      flex: 1,
    },

    vstackHeader: {
      style: {
        alignItems: "center",
        marginVertical: wp("4%"),
      },
    },

    buttonRate: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.orange[500],
        width: wp("40%"),
        marginTop: hp("2%"),
      },
    },

    textButtonRate: {
      style: {
        color: ThemesApp.getTheme().colors.white,
        textTransform: "capitalize",
        textAlignVertical: "center",
      },
    },

    headerContainer: {
      style: {
        width: wp("35%"),
        justifyContent: "space-around",
        alignItems: "center",
      },
    },

    ratingNumber: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(16),
        lineHeight: RFValue(18),
        fontWeight: "bold",
      },
    },
    headerText: {
      color: ThemesApp.getTheme().colors.headerTextReview,
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    starIconModal: {
      size: moderateScale(35),
      style: {
        marginLeft: wp("2%"),
      },
    },
    starColor: {
      style: {
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },
    starIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },
    reviewIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginLeft: wp("2%"),
        fill: ThemesApp.getTheme().colors.white,
      },
    },
    divider: {
      style: {
        width: wp("92%"),
      },
    },

    boxReview: {
      style: {
        marginHorizontal: wp("4%"),
      },
    },
  };
};

export default customStyles;
