/* eslint-disable react/jsx-no-useless-fragment */
import { useIsFocused } from "@react-navigation/native";
import { Box, Button, Divider, Heading, HStack, ScrollView, Text, VStack } from "native-base";
import React, { useEffect, useState } from "react";
import ReviewCard from "src/app/Components/Review/Card";
import customStyles from "src/app/Components/Review/List/styles";
import ReplyReviewComment from "src/app/Components/Review/ReplyComment";
import useTranslation from "src/app/Hooks/useTranslation";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import { Star, StarOutline } from "src/assets/Icons/Flaticon";
import { ReviewList } from "src/business/DTOs/ReviewList";

type Props = {
  data: ReviewList[];
  createReview: () => void;
  entity: string;
  canReplyReview: boolean;
};

const ReviewsList = ({ data, createReview, entity, canReplyReview = false }: Props) => {
  const styles = customStyles();
  const maxRating = [1, 2, 3, 4, 5];
  let ratingSum = 0;
  let average = 0;

  const isFocused = useIsFocused();
  const [reviews, setReviews] = useState<ReviewList[]>();

  const { review: resources } = useTranslation();

  useEffect(() => {
    if (isFocused) {
      setReviews(data);
    }
  }, [isFocused, data]);

  if (reviews?.length) {
    ratingSum = reviews.reduce(function (soma, rate) {
      return soma + rate.rate;
    }, 0);

    average = ratingSum / reviews.length;
  }

  return (
    <>
      <ScrollView>
        <VStack {...styles.container}>
          <VStack space={1} {...styles.vstackHeader}>
            <Heading {...styles.ratingNumber}>{average === 0 ? 0 : average.toFixed(2)}</Heading>
            <HStack {...styles.headerContainer}>
              {maxRating.map((item) => {
                return item <= average ? (
                  <Star key={item} {...styles.starIcon.style} />
                ) : (
                  <StarOutline key={item} {...styles.starIcon.style} />
                );
              })}
            </HStack>

            <Text {...styles.headerText}>
              {reviews?.length
                ? reviews?.length === 1
                  ? setResourceParameters(resources.text.review, reviews?.length)
                  : setResourceParameters(resources.text.reviews, reviews?.length)
                : setResourceParameters(resources.text.review, 0)}
            </Text>

            {!canReplyReview ? (
              <Button onPress={createReview} _text={{ ...styles.textButtonRate }} {...styles.buttonRate}>
                {setResourceParameters(resources.button.createReview, entity)}
              </Button>
            ) : null}
          </VStack>
          {reviews?.map((currentReview) =>
            canReplyReview && !currentReview.storeResponse ? (
              <Box key={currentReview.id} {...styles.boxReview}>
                <Divider {...styles.divider} />
                <ReviewCard data={currentReview} />
                <ReplyReviewComment data={currentReview} />
              </Box>
            ) : (
              <Box key={currentReview.id} {...styles.boxReview}>
                <Divider {...styles.divider} />
                <ReviewCard key={currentReview.id} data={currentReview} />
              </Box>
            ),
          )}
        </VStack>
      </ScrollView>
    </>
  );
};

export default ReviewsList;
