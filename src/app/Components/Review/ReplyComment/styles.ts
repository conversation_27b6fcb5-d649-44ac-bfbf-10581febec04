import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        width: wp("90%"),
        marginTop: hp("5%"),
        alignSelf: "center",
      },
    },
    title: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        marginTop: hp("2%"),
        fontSize: RFValue(22),
        fontWeight: "bold",
      },
    },
    reviewInfoContainer: {
      style: {
        marginTop: hp("1%"),
      },
    },
    ratingText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        marginRight: wp("3%"),
      },
    },
    dateReview: {
      style: {
        color: ThemesApp.getTheme().colors.dateReview,
        marginLeft: wp("2%"),
      },
    },

    customerReviewText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        marginTop: hp("2%"),
      },
    },

    responseContainer: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.responseContainerBg,
        marginTop: hp("3%"),
        marginBottom: hp("2%"),
        width: wp("80%"),
        alignSelf: "center",
        padding: moderateScale(1),
      },
    },
    responseText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        marginTop: hp("2%"),
        marginLeft: wp("1%"),
        padding: moderateScale(1),
      },
    },
    reply: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginLeft: wp("2%"),
        fill: ThemesApp.getTheme().colors.iconHelp,
      },
    },
    replyButton: {
      padding: 0,
    },
    replyText: {
      style: {
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        fontWeight: "bold",

        color: ThemesApp.getTheme().colors.iconHelp,
      },
    },

    inputBox: {
      style: {
        width: wp("90%"),
        alignSelf: "center",
        marginTop: hp("2%"),
      },
    },

    commentInput: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    button: {
      style: {
        width: wp("50%"),
        alignSelf: "center",
        marginTop: hp("2%"),
        marginBottom: hp("5%"),
      },
    },
    reviewIcon: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
        marginLeft: wp("2%"),
        fill: ThemesApp.getTheme().colors.white,
      },
    },
  };
};

export default customStyles;
