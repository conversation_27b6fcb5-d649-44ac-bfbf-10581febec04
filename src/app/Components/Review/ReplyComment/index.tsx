import {Box, Button, Input, Text} from "native-base";
import React, {useState} from "react";

import ModalSimple from "src/app/Components/ModalSimple";
import customStyles from "src/app/Components/Review/ReplyComment/styles";
import useTranslation from "src/app/Hooks/useTranslation";
import useUpdateStoreReviews from "src/app/Modules/Main/Stores/Query/useUpdateStoreReviews";
import {ReviewList} from "src/business/DTOs/ReviewList";

type Props = {
  data: ReviewList;
};

const ReplyReviewComment: React.FC<Props> = ({data}) => {
  const styles = customStyles();
  const {review: resources} = useTranslation();

  const [replyModalVisibility, setReplyModalVisibility] = useState(false);
  const [review, setReview] = useState("");

  const updateStoreReviewQuery = useUpdateStoreReviews();

  const changeModalVisibility = () => {
    setReplyModalVisibility(!replyModalVisibility);
  };
  const handleSaveReview = async () => {
    updateStoreReviewQuery.mutate({storeId: data.id, response: review});
  };

  return (
    <>
      <Button
        onPress={changeModalVisibility}
        variant="link"
        {...styles.replyButton}>
        <Text {...styles.replyText}>{resources.button.reply}</Text>
      </Button>

      <ModalSimple
        visibility={replyModalVisibility}
        onClose={changeModalVisibility}
        titleHeader={resources.button.replyReview}
        useKeyboard>
        <Box {...styles.inputBox}>
          <Input
            placeholder={resources.placeholder.comment}
            numberOfLines={6}
            textAlignVertical="top"
            onChangeText={text => setReview(text)}
            onSubmitEditing={handleSaveReview}
            {...styles.commentInput}
          />
        </Box>
        <Button
          {...styles.button}
          onPress={handleSaveReview}
          isLoading={updateStoreReviewQuery.isPending}>
          {resources.button.submitReview}
        </Button>
      </ModalSimple>
    </>
  );
};

export default ReplyReviewComment;
