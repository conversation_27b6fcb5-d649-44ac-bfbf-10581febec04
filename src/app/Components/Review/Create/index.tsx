import {
  Box,
  Button,
  HStack,
  Input,
  Pressable,
  ScrollView,
  Text,
  VStack,
} from "native-base";
import React, {useRef, useState} from "react";

import useTranslation from "src/app/Hooks/useTranslation";

import {TextInput} from "react-native";
import customStyles from "src/app/Components/Review/Create/styles";
import {Star, StarOutline} from "src/assets/Icons/Flaticon";
import applicationSingleton from "src/business/Singletons/Application";
import useCreateOrderReview from "src/app/Modules/Main/Orders/Query/useCreateOrderReview";
import {useNavigation} from "@react-navigation/native";
import useCreateStoreReview from "src/app/Modules/Main/Stores/Query/useCreateStoreReview";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";

type Props = {
  title: string;
  orderId?: string;
  storeId?: string;
  deliverymanId?: string;
};

const CreateReview = ({title, orderId, storeId, deliverymanId}: Props) => {
  const styles = customStyles();

  const navigation = useNavigation();

  const [defaultRating, setDefaultRating] = useState<number>(1);
  const [review, setReview] = useState("");

  const inputRef = useRef<TextInput>(null);

  const {review: resources} = useTranslation();

  const createOrderReviewMutation = useCreateOrderReview();
  const createStoreReviewMutation = useCreateStoreReview();

  const maxRating = [1, 2, 3, 4, 5];

  const userId = applicationSingleton.authenticationResult?.userInfo.id;

  const handleSaveReview = () => {
    if (userId) {
      if (!orderId) {
        createStoreReviewMutation.mutate(
          {
            rate: defaultRating,
            customerReview: review,
            user: {
              id: userId,
            },
            store: {
              id: storeId,
            },
          },
          {
            onSuccess: () => {
              showToastSuccess(resources.toasts.create_success);
              navigation.goBack();
            },
          },
        );
      } else {
        createOrderReviewMutation.mutate(
          {
            rate: defaultRating,
            customerReview: review,
            user: {
              id: userId,
            },
            store: {
              id: storeId,
            },
            order: {
              id: orderId,
            },
            deliveryman: {
              id: deliverymanId,
            },
          },
          {
            onSuccess: () => {
              navigation.goBack();
              inputRef.current?.clear();
            },
          },
        );
      }
    }
  };

  return (
    <ScrollView>
      <VStack flex={1}>
        <VStack {...styles.mainContent}>
          <Text {...styles.title}>{title}</Text>
          <Text {...styles.subtitle}>{resources.text.createReviewText}</Text>
          <HStack {...styles.starRating}>
            {maxRating.map(item => {
              return (
                <Pressable key={item} onPress={() => setDefaultRating(item)}>
                  {item <= defaultRating ? (
                    <Star key={item} {...styles.starIcon.style} />
                  ) : (
                    <StarOutline key={item} {...styles.starIcon.style} />
                  )}
                </Pressable>
              );
            })}
          </HStack>
          <Box {...styles.inputBox}>
            <Input
              placeholder={resources.placeholder.comment}
              numberOfLines={6}
              textAlignVertical="top"
              onChangeText={text => setReview(text)}
              ref={inputRef}
              maxLength={200}
              onSubmitEditing={handleSaveReview}
            />

            <Button
              {...styles.button}
              onPress={handleSaveReview}
              isLoading={createOrderReviewMutation.isPending}>
              {resources.button.submitReview}
            </Button>
          </Box>
        </VStack>
      </VStack>
    </ScrollView>
  );
};

export default CreateReview;
