import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    loadingContainer: {
      justifyContent: "center",
      alignItems: "center",
      flex: 1,
    },
    container: {
      style: {
        flex: 1,
      },
    },

    mainContent: {
      style: {
        width: wp("90%"),
        marginTop: hp("10%"),
        alignSelf: "center",
        justifyContent: "center",
      },
    },

    starRating: {
      style: {
        width: wp("40%"),
        alignSelf: "center",
        justifyContent: "space-around",
        alignItems: "center",
        marginTop: hp("3%"),
      },
    },

    inputBox: {
      style: {
        width: wp("90%"),
        alignSelf: "center",
        marginTop: hp("5%"),
      },
    },

    button: {
      w: "full",
      style: {
        alignSelf: "center",
        marginVertical: hp("3%"),
      },
    },

    title: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        textAlign: "center",
        fontSize: RFValue(16),
        lineHeight: RFValue(18),
        fontWeight: "bold",
      },
    },
    subtitle: {
      style: {
        color: ThemesApp.getTheme().colors.gray[400],
        textAlign: "center",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        marginTop: hp("1%"),
      },
    },
    starIcon: {
      style: {
        width: moderateScale(22),
        height: moderateScale(22),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },
    reviewIcon: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
        marginLeft: wp("2%"),
        fill: ThemesApp.getTheme().colors.white,
      },
    },
  };
};

export default customStyles;
