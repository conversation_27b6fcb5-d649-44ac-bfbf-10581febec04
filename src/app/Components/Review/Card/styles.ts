import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        width: wp("92%"),
        alignSelf: "center",
        marginTop: hp("2%"),
      },
    },
    title: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",

        flex: 1,
      },
    },
    reviewInfoContainer: {
      style: {
        alignItems: "center",
      },
    },
    ratingText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        marginRight: wp("2%"),
      },
    },
    dateReview: {
      style: {
        marginLeft: wp("2%"),
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        color: ThemesApp.getTheme().colors.dateReview,
      },
    },
    dateVStack: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    customerReviewText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    responseContainer: {
      style: {
        width: wp("85%"),
        marginVertical: hp("1%"),
        padding: wp("2%"),
        borderRadius: moderateScale(5),
        backgroundColor: ThemesApp.getTheme().colors.responseContainerBg,
        alignSelf: "flex-end",
      },
    },
    responseText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        marginTop: hp("1%"),
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    customerReviewBox: {
      style: {
        padding: hp("1%"),
        borderRadius: moderateScale(5),
      },
    },
    titleStore: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    replyVStack: {
      style: {
        justifyContent: "space-between",
        alignItems: "baseline",
      },
    },
    starColor: {
      style: {
        width: moderateScale(14),
        height: moderateScale(14),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },
  };
};

export default customStyles;
