/* eslint-disable react/jsx-no-useless-fragment */
import {Box, HStack, Text, VStack} from "native-base";
import React from "react";

import customStyles from "src/app/Components/Review/Card/styles";
import CapitalizeTitleLetter from "src/app/Utils/CapitalizeTitleLetter";
import {formatDate} from "src/app/Utils/FormatDate";
import {Star, StarOutline} from "src/assets/Icons/Flaticon";
import {ReviewList} from "src/business/DTOs/ReviewList";

type Props = {
  data: ReviewList;
};

const ReviewCard: React.FC<Props> = ({data}) => {
  const styles = customStyles();

  const date = new Date(data.createdAt);

  const maxRating = [1, 2, 3, 4, 5];

  const formattedDate = formatDate(date);

  return (
    <>
      {data ? (
        <VStack {...styles.container}>
          <HStack {...styles.dateVStack}>
            <Text ellipsizeMode="tail" numberOfLines={1} {...styles.title}>
              {CapitalizeTitleLetter(data.user.firstName)}
            </Text>
            <Text {...styles.dateReview}>{formattedDate}</Text>
          </HStack>
          <HStack {...styles.reviewInfoContainer}>
            <Text {...styles.ratingText}>{data.rate.toFixed(0)}</Text>
            {maxRating.map(item => {
              return item <= data.rate ? (
                <Star key={item} {...styles.starColor.style} />
              ) : (
                <StarOutline key={item} {...styles.starColor.style} />
              );
            })}
          </HStack>
          {data.customerReview ? (
            <Box {...styles.customerReviewBox}>
              <Text {...styles.customerReviewText}>{data.customerReview}</Text>
            </Box>
          ) : null}
          {data.storeResponse ? (
            <VStack {...styles.responseContainer}>
              <HStack {...styles.replyVStack}>
                <Text {...styles.titleStore}>
                  {CapitalizeTitleLetter(data.store.name)}
                </Text>
                {/* <Text {...styles.dateReview}>{formattedDate}</Text> */}
              </HStack>
              <Text {...styles.responseText}>{data.storeResponse}</Text>
            </VStack>
          ) : null}
        </VStack>
      ) : null}
    </>
  );
};

export default ReviewCard;
