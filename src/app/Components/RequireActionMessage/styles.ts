import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { RFValue } from "react-native-responsive-fontsize";

const customStyles = (): IStyleProps => {
  return {
    container: {
      flex: 1,
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center",
    },
    text: {
      fontSize: RFValue(14),
      color: ThemesApp.getTheme().colors.textCode,
    },
  };
};

export default customStyles;
