import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      flex: 1,
    },
    checkbox: {
      style: {
        fill: ThemesApp.getTheme().colors.iconHelp,
        width: moderateScale(18),
        height: moderateScale(18),
      },
    },
    hStackAddOption: {
      style: {
        marginLeft: horizontalScale(16),
        marginTop: verticalScale(5),
        justifyContent: "flex-start",
        alignItems: "center",
        marginBottom: hp("2%"),
      },
    },
    textAddOption: {
      style: {
        color: ThemesApp.getTheme().colors.iconHelp,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
  };
};

export default customStyles;
