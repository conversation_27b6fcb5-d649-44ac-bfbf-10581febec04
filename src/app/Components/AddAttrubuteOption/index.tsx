import React from "react";
import { HStack, Pressable, Text } from "native-base";
import { Plus } from "src/assets/Icons/Flaticon";
import useAddAttributeOptionComponent from "src/app/Components/AddAttrubuteOption/useAddAttributeOptionComponent";

interface AddAttributeOptionProps {
  onPress: () => void;
}

const AddAttributeOption = ({ onPress }: AddAttributeOptionProps) => {
  const { styles, resourcesAttributeOption } = useAddAttributeOptionComponent();


  return (
    <Pressable
      onPress={onPress}
    >
      <HStack space={2} {...styles.hStackAddOption}>
        <Plus {...styles.checkbox.style} />
        <Text {...styles.textAddOption}>{resourcesAttributeOption.button.add_attribute_option}</Text>
      </HStack>
    </Pressable>

  );
};

export default AddAttributeOption;
