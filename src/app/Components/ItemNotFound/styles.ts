import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    vStackStore: {
      style: {
        alignItems: "center",
        marginTop: verticalScale(80),
      },
    },

    vStackStoreRow: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        margin: verticalScale(10),
      },
    },

    sadIcon: {
      style: {
        width: moderateScale(30),
        height: moderateScale(30),
        fill: ThemesApp.getTheme().colors.secondary[500],
      },
    },

    textTitle: {
      fontSize: RFValue(14),
      lineHeight: RFValue(16),
      color: ThemesApp.getTheme().colors.muted[400],
      marginTop: 4
    },
  };
};

export default customStyles;
