import { Text, VStack } from "native-base";
import { ResponsiveValue } from "native-base/lib/typescript/components/types";
import React from "react";
import customStyles from "src/app/Components/ItemNotFound/styles";
import { Sad } from "src/assets/Icons/Flaticon";

interface IProps {
  title: string;
  directionComponent: ResponsiveValue<"row" | "column" | "column-reverse" | "row-reverse">;
  flex: number | undefined;
}

const ItemNotFound = ({ title, directionComponent, flex }: IProps) => {
  const styles = customStyles();
  return (
    <VStack
      space={2}
      direction={directionComponent}
      flex={flex}
      style={
        directionComponent === "row" || directionComponent === "row-reverse"
          ? styles.vStackStoreRow.style
          : styles.vStackStore.style
      }
    >
      <Sad {...styles.sadIcon.style} />
      <Text {...styles.textTitle}>{title}</Text>
    </VStack>
  );
};

export default ItemNotFound;
