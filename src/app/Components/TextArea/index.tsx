import { FormControl, HStack, ITextAreaProps, Text, TextArea as TextAreaNB, VStack } from "native-base";
import React, { memo } from "react";
import { Controller, useFormContext } from "react-hook-form";
import customStyles from "src/app/Components/TextArea/styles";
import { WarningOutline } from "src/assets/Icons/Flaticon";

interface TextAreaProps extends ITextAreaProps {
  name: string;
  label: string;
  maxLength: number;
  length: number;
  isRequired?: boolean;
  errorMessage?: string;
}

const TextArea = memo(({ name, label, isRequired = true, maxLength, length, errorMessage, ...rest }: TextAreaProps) => {
  const styles = customStyles();

  const { control } = useFormContext();

  return (
    <FormControl isRequired={isRequired} isInvalid={!!errorMessage}>
      <FormControl.Label _text={{ ...styles.textArea }} _astrick={{ ...styles.textArea }}>
        {label}
      </FormControl.Label>

      <VStack>
        <Controller
          control={control}
          name={name}
          render={({ field: { onChange, onBlur, value, ref } }) => {
            return (
              <TextAreaNB
                ref={ref}
                autoCompleteType={undefined}
                value={value}
                onChangeText={onChange}
                isInvalid={!!errorMessage}
                maxLength={maxLength}
                onBlur={onBlur}
                {...styles.textArea}
                {...rest}
              />
            );
          }}
        />

        <HStack {...styles.textAreaError}>
          {errorMessage ? (
            <FormControl.ErrorMessage
              _text={{ fontSize: 10, lineHeight: 12 }}
              leftIcon={<WarningOutline {...styles.warningOutlineIcon.style} />}
              {...styles.formControlErrorMessage}
            >
              {errorMessage}
            </FormControl.ErrorMessage>
          ) : (
            <FormControl.HelperText _text={{ fontSize: 10, lineHeight: 12 }} {...styles.formControlHelperText}>
              &nbsp;
            </FormControl.HelperText>
          )}
          <Text {...styles.textAreaCounter}>
            {length}/{maxLength}
          </Text>
        </HStack>
      </VStack>
    </FormControl>
  );
});

export default TextArea;
