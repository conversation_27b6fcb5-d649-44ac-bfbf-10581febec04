import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";

import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    textArea: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textApp,
      },
    },

    textAreaCounter: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    textAreaError: {
      style: {
        justifyContent: "space-between",
      },
    },

    warningOutlineIcon: {
      style: {
        width: moderateScale(13),
        height: moderateScale(13),
        fill: ThemesApp.getTheme().colors.red[400],
      },
    },

    formControlErrorMessage: {
      mt: 1,
    },

    formControlHelperText: {
      mt: 2,
    },
  };
};

export default customStyles;
