import React from "react";
import { Box, Text } from "native-base";
import useAttributeAssociationRefact from "src/app/Components/AttributeAssociation/useAttributeAssociationComponent";
import SearchInput from "src/app/Components/SearchInput";
import AttributeListComponent from "src/app/Components/AttributeAssociation/AttributeListComponent";
import ModalRN from "src/app/Components/Modal";
import AttributeForm from "src/app/Modules/Main/Product/Components/Form/Attribute";
import AttributeOptionForm from "src/app/Modules/Main/Product/Components/Form/AttributeOption";
import CancelSaveFooter from "src/app/Components/CancelSaveFooter";
import { AttributeList } from "src/business/DTOs/AttributeList";

interface IAttributeAssociationProps {
  productId?: string;
  isEdit?: boolean;
  onSave?: (attributes: AttributeList[]) => void;
  onClose?: () => void;
}

const AttributeAssociation = ({ productId, isEdit = false, onSave, onClose }: IAttributeAssociationProps) => {
  const {
    styles,
    resourcesAttribute,
    resourcesAttributeOption,
    modalAttributeOptionVisibility,
    inputValue,
    debounceInputValue,
    dataValues,
    selectedAttributeId,
    creationAttributeVisibility,
    handleSubmit,
    handleCloseModalAttributeOption,
    handleCreateAttributeOption,
    handleModalAttributeVisibility,
    handleCloseCreationAttribute,
    handleChangeText,
    handleCancel,
    setModalAttributeOptionVisibility
  } = useAttributeAssociationRefact({
    onSave,
    onClose,
    productId
  });

  return (
    <Box {...styles.container}>
      {creationAttributeVisibility ? (
        <AttributeForm
          origin={{ attribute: "create" }}
          initialValues={{ data: dataValues }}
          hideCreation={handleCloseCreationAttribute}
        />
      ) : (
        <>
          <Box {...(isEdit ? { ...styles.boxData } : { ...styles.boxData2 })}>
            <Text {...styles.titlePageText}>{resourcesAttribute.header.title}</Text>
            <SearchInput
              placeholder={resourcesAttribute.placeholder.input_select}
              value={inputValue}
              onChangeText={handleChangeText}
              _input={{ maxLength: 50 }}
              onCreatePress={handleModalAttributeVisibility}
            />
            <AttributeListComponent
              debounceInputValue={debounceInputValue}
              onCreateAttributeOption={handleCreateAttributeOption}
              productId={productId}
            />
          </Box>
  
          {isEdit && (
            <CancelSaveFooter
              cancelText={resourcesAttribute.button.cancel}
              saveText={resourcesAttribute.button.save}
              onCancel={handleCancel}
              onSave={handleSubmit}
            />
          )}
        </>
      )}
  
      <ModalRN
        title={resourcesAttributeOption.header.title}
        visibility={modalAttributeOptionVisibility}
        onClose={() => setModalAttributeOptionVisibility(false)}
      >
        <AttributeOptionForm
          origin={{ attribute_option: "create" }}
          initialValues={{
            data: { value: "", attributeId: selectedAttributeId },
          }}
          hideModal={handleCloseModalAttributeOption}
        />
      </ModalRN>
    </Box>
  );  
};

export default AttributeAssociation;
