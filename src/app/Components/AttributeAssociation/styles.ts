import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      flex: 1,
    },

    boxItem: {
      style: {
        width: wp("100%"),
        // marginTop: hp("2%"),
        marginLeft: hp("2%"),
      },
    },

    boxSeparator: {
      borderWidth: moderateScale(1),
      borderColor: ThemesApp.getTheme().colors.gray[300],
      borderRadius: moderateScale(5),
    },

    titlePageText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
        paddingVertical: hp("3%"),
        alignSelf: "center",
      },
    },

    boxData: {
      style: {
        marginHorizontal: wp("5%"),
        marginBottom: hp("5%"),
        alignItems: "center",
        height: hp("75%"),
      },
    },
    boxData2: {
      style: {
        marginHorizontal: wp("5%"),
        marginBottom: hp("5%"),
        alignItems: "center",
        height: hp("65%"),
      },
    },

    flatlist: {
      style: {
        marginTop: hp("4%"),
        height: hp("55%"),
      },
    },

    boxButtons: {
      style: {
        position: "absolute",
        bottom: 0,
        alignItems: "center",
        marginBottom: verticalScale(8),
        width: wp("100%"),
      },
    },

    buttonBottom: {
      style: {
        width: wp("45%"),
      },
    },

    buttonInclude: {
      style: {
        width: wp("45%"),
        borderRadius: moderateScale(30),
        marginVertical: hp("3%"),
      },
    },

    modal: {
      size: "full",
      style: {
        justifyContent: "flex-end",
      },
    },

    modalContent: {
      style: {
        height: hp("67%"),
        borderTopLeftRadius: moderateScale(30),
        borderTopRightRadius: moderateScale(30),
      },
    },

    modalCloseButton: {
      style: { marginRight: wp("1%") },
    },

    modalHeader: {
      style: { alignSelf: "center" },
    },

    modalHeaderText: {
      style: {
        fontSize: RFValue(20),
        lineHeight: RFValue(20),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    boxModal: {
      style: {
        margin: wp("5%"),
      },
    },

    boxNewOption: {
      style: {
        width: wp("80%"),
        alignItems: "center",
      },
    },

    rightInputButton: {
      roundedBottomLeft: "none",
      roundedTopLeft: "none",
      h: "full",
    },
    leftInputButton: {
      roundedBottomRight: "none",
      roundedTopRight: "none",
      h: "full",
    },
    checkbox: {
      style: {
        fill: ThemesApp.getTheme().colors.iconHelp,
        width: moderateScale(18),
        height: moderateScale(18),
      },
    },
    hStackAddOption: {
      style: {
        marginLeft: horizontalScale(16),
        marginTop: verticalScale(5),
        justifyContent: "flex-start",
        alignItems: "center",
        marginBottom: hp("2%"),
      },
    },

    iconWhite: {
      style: {
        fill: ThemesApp.getTheme().colors.white,
      },
    },

    textAddOption: {
      style: {
        color: ThemesApp.getTheme().colors.iconHelp,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
  };
};

export default customStyles;
