/* eslint-disable react-hooks/exhaustive-deps */
import {useFocusEffect} from "@react-navigation/native";
import {useQueryClient} from "@tanstack/react-query";
import React, {useCallback, useState} from "react";
import useDebounce from "src/app/Hooks/useDebounce";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Components/AttributeAssociation/styles";
import useAttributeAssociation from "src/app/Zustand/Store/useAttributeAssociation";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {AttributeList} from "src/business/DTOs/AttributeList";
import EAttributeType from "src/business/Enums/Models/EAttributeType";

type Props = {
  productId?: string;
  onSave?: (attributes: AttributeList[]) => void;
  onClose?: () => void;
};

const useAttributeAssociationRefact = ({productId, onSave, onClose}: Props) => {
  const [creationAttributeVisibility, setCreationAttributeVisibility] =
    useState(false);
  const [modalAttributeOptionVisibility, setModalAttributeOptionVisibility] =
    useState(false);
  const [inputValue, setInputValue] = useState("");
  const [selectedAttributeId, setSelectedAttributeId] = useState("");

  const debounceInputValue = useDebounce<string>(inputValue, 500);

  const queryClient = useQueryClient();

  const {selectedAttributes, clearSelectedAttributes} =
    useAttributeAssociation();

  const styles = customStyles();

  useFocusEffect(
    React.useCallback(() => {
      queryClient.refetchQueries({
        queryKey: [
          QUERY_KEYS.GET_ATTRIBUTES_SEARCH,
          debounceInputValue,
          productId,
        ],
      });
    }, [productId]),
  );

  const {
    attribute: {create: resourcesAttribute},
    attribute_option: {create: resourcesAttributeOption},
  } = useTranslation();

  const dataValues = {
    name: inputValue,
    required: false,
    shortDescription: "",
    type: undefined
  };

  const handleModalAttributeVisibility = () => {
    setCreationAttributeVisibility(true);
  };

  const handleCloseCreationAttribute = () => {
    setCreationAttributeVisibility(false);
    queryClient.refetchQueries({
      queryKey: [
        QUERY_KEYS.GET_ATTRIBUTES_SEARCH,
        debounceInputValue,
        productId,
      ],
    });
  };

  const handleCloseModalAttributeOption = () => {
    setModalAttributeOptionVisibility(false);
    setSelectedAttributeId("");
    queryClient.refetchQueries({
      queryKey: [
        QUERY_KEYS.GET_ATTRIBUTES_SEARCH,
        debounceInputValue,
        productId,
      ],
    });
  };

  const handleCreateAttributeOption = (attributeId: string) => {
    setSelectedAttributeId(attributeId);

    setModalAttributeOptionVisibility(true);
  };

  const handleChangeText = useCallback((text: string) => {
    setInputValue(text);
  }, []);

  const handleCancel = () => {
    setInputValue("");
    clearSelectedAttributes();
    onClose && onClose();
  };

  const handleSubmit = () => {
    console.log("DataValues", selectedAttributes);
    onSave && onSave(selectedAttributes);
    clearSelectedAttributes();
    setInputValue("");

    onClose && onClose();
  };

  return {
    styles,
    inputValue,
    debounceInputValue,
    resourcesAttribute,
    resourcesAttributeOption,
    dataValues,
    creationAttributeVisibility,
    modalAttributeOptionVisibility,
    selectedAttributeId,
    handleModalAttributeVisibility,
    handleCreateAttributeOption,
    handleCloseModalAttributeOption,
    handleCloseCreationAttribute,
    handleSubmit,
    handleChangeText,
    handleCancel,
    setCreationAttributeVisibility,
    setModalAttributeOptionVisibility,
  };
};

export default useAttributeAssociationRefact;
