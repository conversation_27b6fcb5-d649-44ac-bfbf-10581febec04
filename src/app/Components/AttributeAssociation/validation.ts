import ZodString from "src/app/Utils/Zod/ZodString";
import EAttributeType from "src/business/Enums/Models/EAttributeType";
import { z } from "zod";

const getSchemaCreateAttributeAssociation = () => {
  const schema = {
    attributes: z
      .array(
        z.object({
          id: ZodString(),
          name: ZodString(),
          short_description: ZodString(),
          required: z.boolean(),
          type: z.nativeEnum(EAttributeType),
          checked: z.boolean(),
          attributeOption: z
            .array(
              z.object({
                id: ZodString(),
                value: ZodString(),
                checked: z.boolean(),
              }),
            )
            .optional(),
        }),
      )
      .optional(),
  };
  return schema;
};

export default getSchemaCreateAttributeAssociation;
