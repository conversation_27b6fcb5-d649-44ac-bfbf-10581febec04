import React from "react";
import { FlatList } from "native-base";
import { AttributeOptionList } from "src/business/DTOs/AttributeList";
import AttributeOption from "src/app/Components/AttributeOption";

interface AttributeOptionListProps {
  data?: AttributeOptionList[];
  onPressOption: (option: AttributeOptionList) => void
}

const AttributeOptionListComponent = ({ data, onPressOption }: AttributeOptionListProps) => {
  const renderItem = ({ item }: { item: AttributeOptionList }) => {
    return <AttributeOption onPress={() => onPressOption(item)} title={item.value} checked={item.checked} />;
  };

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
    />

  );
};

export default AttributeOptionListComponent;
