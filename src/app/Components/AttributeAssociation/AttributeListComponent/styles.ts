import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {moderateScale} from "src/app/Utils/Metrics";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxItem: {
      style: {
        width: wp("100%"),
        // marginTop: hp("2%"),
        marginLeft: hp("2%"),
      },
    },
    flatlist: {
      style: {
        marginTop: hp("4%"),
        height: hp("55%"),
      },
    },
    boxSeparator: {
      borderWidth: moderateScale(1),
      borderColor: ThemesApp.getTheme().colors.gray[300],
      borderRadius: moderateScale(5),
    },
  };
};

export default customStyles;
