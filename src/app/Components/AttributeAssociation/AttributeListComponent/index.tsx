/* eslint-disable react/jsx-no-useless-fragment */

import React from "react";
import { Box, FlatList, Spinner } from "native-base";
import { AttributeList } from "src/business/DTOs/AttributeList";
import useAttributeListComponent from "src/app/Components/AttributeAssociation/AttributeListComponent/useAttributeListComponent";
import AttributeProduct from "src/app/Components/Attribute";
import AttributeOptionListComponent from "src/app/Components/AttributeAssociation/AttributeOptionListComponent";
import AddAttributeOption from "src/app/Components/AddAttrubuteOption";

interface AttributeListProps {
  productId?: string;
  debounceInputValue: string;
  onCreateAttributeOption: (attributeId: string) => void;
}

const AttributeListComponent = ({ productId, debounceInputValue, onCreateAttributeOption }: AttributeListProps) => {
  const {
    styles,
    attributesData,
    attributesSearchQuery,
    handleSelectAttribute,
    handleSelectAttributeOption,
    handlePaginate,
  } = useAttributeListComponent({
    debounceInputValue,
    productId
  });

  const renderItem = ({ item }: { item: AttributeList }) => {
    return (
      <Box {...styles.boxItem}>
        <Box {...{ opacity: item?.checked ? 1 : 0.5 }}>
          <AttributeProduct
            data={item}
            hasCheckBox={true}
            onPress={handleSelectAttribute}
          />
          <AttributeOptionListComponent
            data={item?.attributeOption}
            onPressOption={(option) => handleSelectAttributeOption(item, option)}
          />
        </Box>

        <AddAttributeOption onPress={() => onCreateAttributeOption(item.id)} />

      </Box>
    );
  };

  const separatorComponent = () => {
    return <Box {...styles.boxSeparator} />;
  };

  const footerComponent = () => {
    return (attributesSearchQuery.isLoading || attributesSearchQuery.isFetchingNextPage ? (
      <Spinner size="sm" mt={6} />

    ) : null);
  };

  return (
    <>
      {
        attributesData.length > 0 ? (
          <FlatList
            data={attributesData.length === 0 ? undefined : attributesData}
            renderItem={renderItem}
            onEndReached={handlePaginate}
            onEndReachedThreshold={0.1}
            ItemSeparatorComponent={separatorComponent}
            nestedScrollEnabled={true}
            ListFooterComponent={footerComponent}
            {...styles.flatlist}
          />

        ) : (
          <Spinner size="sm" mt={6} />
        )
      }

    </>
  );
};

export default AttributeListComponent;
