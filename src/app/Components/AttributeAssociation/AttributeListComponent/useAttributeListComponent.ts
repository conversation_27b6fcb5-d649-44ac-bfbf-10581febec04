/* eslint-disable react-hooks/exhaustive-deps */
import {useIsFocused, useNavigation} from "@react-navigation/native";
import {useQueryClient} from "@tanstack/react-query";
import {useEffect} from "react";
import customStyles from "src/app/Components/AttributeAssociation/AttributeListComponent/styles";
import useFlatInfiniteQueryData from "src/app/Hooks/useFlatInfiniteQueryData";
import useGetAttributesPaginated from "src/app/Modules/Main/Product/Query/useGetAttributesPaginated";
import useAttributeAssociation from "src/app/Zustand/Store/useAttributeAssociation";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {
  AttributeList,
  AttributeOptionList,
} from "src/business/DTOs/AttributeList";

interface Props {
  productId?: string;
  debounceInputValue: string;
}

const useAttributeListComponent = ({productId, debounceInputValue}: Props) => {
  const styles = customStyles();

  const navigation = useNavigation();

  const isFocused = useIsFocused();

  const attributesSearchQuery = useGetAttributesPaginated(
    productId,
    debounceInputValue,
    () => {},
    () => {},
    isFocused,
  );

  const queryClient = useQueryClient();

  const {
    attributesData,
    setCheckedAttribute,
    setCheckedAttributeOption,
    setAttributeList,
    clearAttributes,
    clearSelectedAttributes,
  } = useAttributeAssociation();

  useEffect(() => {
    const unsubscribe = navigation.addListener("blur", () => {
      clearAttributes();
      clearSelectedAttributes();
      queryClient.resetQueries({
        queryKey: [
          QUERY_KEYS.GET_ATTRIBUTES_SEARCH,
          debounceInputValue,
          productId,
        ],
      });
    });

    return unsubscribe;
  }, []);

  const handleSelectAttribute = (attributeList: AttributeList) => {
    setCheckedAttribute(attributeList);
  };

  const handleSelectAttributeOption = (
    attributeList: AttributeList,
    option: AttributeOptionList,
  ) => {
    setCheckedAttributeOption(attributeList, option);
  };

  const handlePaginate = () => {
    if (attributesSearchQuery.hasNextPage && !attributesSearchQuery.isLoading) {
      attributesSearchQuery.fetchNextPage();
    }
  };

  const attributesSearchData = useFlatInfiniteQueryData(attributesSearchQuery);

  useEffect(() => {
    if (attributesSearchData) {
      setAttributeList(attributesSearchData);
    }
  }, [attributesSearchData]);

  return {
    styles,
    attributesSearchQuery,
    attributesData,
    handleSelectAttribute,
    handleSelectAttributeOption,
    handlePaginate,
  };
};

export default useAttributeListComponent;
