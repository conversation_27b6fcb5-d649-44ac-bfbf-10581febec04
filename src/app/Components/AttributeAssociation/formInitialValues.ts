import { AttributeList } from "src/business/DTOs/AttributeList";
import EAttributeType from "src/business/Enums/Models/EAttributeType";

const attributeFormInitialValues: AttributeList = {
  id: "",
  name: "",
  shortDescription: "",
  required: false,
  type: EAttributeType.simpleSelection,
  checked: false,
  attributeOption: [
    {
      id: "",
      value: "",
      checked: false,
    },
  ],
};

export default attributeFormInitialValues;
