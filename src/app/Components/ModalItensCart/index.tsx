import {Box, Button, Text, VStack} from "native-base";
import React from "react";
import ModalRN from "src/app/Components/Modal";
import customStyles from "src/app/Components/ModalItensCart/style";
import useTranslation from "src/app/Hooks/useTranslation";

type Props = {
  visibility: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isCart?: boolean;
};

const ModalItensCart: React.FC<Props> = ({
  visibility,
  onClose,
  onConfirm,
  isCart = false,
}) => {
  const styles = customStyles();
  const {productContainer, orders: resources} = useTranslation();

  const handleConfirm = async () => {
    onConfirm();
    onClose();
  };

  return (
    <ModalRN
      visibility={visibility}
      onClose={onClose}
      title={resources.cart.label.deleteAll}
      componentFooter={
        <Button.Group direction="row" {...styles.buttonGroup}>
          <Button onPress={onClose} {...styles.btnNegative}>
            {productContainer.button.no}
          </Button>
          <Button onPress={handleConfirm} {...styles.btnPositive}>
            {productContainer.button.yes}
          </Button>
        </Button.Group>
      }>
      <VStack space={3}>
        {!isCart ? (
          <Text {...styles.textModalHeader}>
            {resources.cart.label.alreadyCartItems}
          </Text>
        ) : null}
        <Box flex={1} />
        <Text {...styles.textModalBody}>{resources.cart.label.eraseCart}</Text>
      </VStack>
    </ModalRN>
  );
};

export default ModalItensCart;
