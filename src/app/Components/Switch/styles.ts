import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    switchLabel: {
      style: {
        color: ThemesApp.getTheme().colors.gray[500],
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "bold",
      },
    },

    endIconSelect: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.checkedIcon,
        position: "absolute",
        right: RFValue(0),
      },
    },

    warningOutlineIcon: {
      style: {
        width: moderateScale(13),
        height: moderateScale(13),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },

    formControlErrorMessage: {
      mt: 1,
      style: {
        marginBottom: "5%",
      },
    },

    formControlHelperText: {
      mt: 2,
      style: {
        marginBottom: "5%",
      },
    },

    hStackStatus: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
  };
};

export default customStyles;
