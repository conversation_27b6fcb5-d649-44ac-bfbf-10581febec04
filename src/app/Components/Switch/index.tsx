import { FormControl, HS<PERSON>ck, ISwitchProps, Switch as SwitchNB } from "native-base";
import React, { memo } from "react";
import { Controller, useFormContext } from "react-hook-form";
import customStyles from "src/app/Components/Switch/styles";
import { WarningOutline } from "src/assets/Icons/Flaticon";

interface SwitchProps extends ISwitchProps {
  name: string;
  label: string;
  errorMessage?: string;
  required?: boolean;
  endIconErrorInfo?: JSX.Element;
}

const Switch = memo(({ name, label, errorMessage, required = true, endIconErrorInfo, ...props }: SwitchProps) => {
  const styles = customStyles();
  const { control } = useFormContext();

  return (
    <FormControl isRequired={required} isInvalid={!!errorMessage}>
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, value } }) => {
          const handleChange = (newValue: string) => {
            onChange(newValue);
          };
          return (
            <HStack {...styles.hStackStatus}>
              <FormControl.Label _text={{ ...styles.switchLabel }} _astrick={{ ...styles.switchLabel }}>
                {label}
              </FormControl.Label>
              <SwitchNB name={name} value={value} onToggle={handleChange} {...props} />
            </HStack>
          );
        }}
      />

      {errorMessage ? (
        <FormControl.ErrorMessage
          _text={{ fontSize: 10, lineHeight: 12 }}
          leftIcon={<WarningOutline {...styles.warningOutlineIcon.style} />}
          {...styles.formControlErrorMessage}
          endIcon={endIconErrorInfo}
        >
          {errorMessage}
        </FormControl.ErrorMessage>
      ) : (
        <FormControl.HelperText _text={{ fontSize: 10, lineHeight: 12 }} {...styles.formControlHelperText}>
          &nbsp;
        </FormControl.HelperText>
      )}
    </FormControl>
  );
});

export default Switch;
