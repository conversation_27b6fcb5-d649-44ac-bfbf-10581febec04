import {View} from "native-base";
import React from "react";
import StepsComponents, {
  StepsProps,
} from "src/app/Components/ProgressPofileStatus/Components/Steps";
import customStyles from "src/app/Components/ProgressPofileStatus/styles";
import {Resources} from "src/app/Context/Utils/Resources";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";
import {EProfile} from "src/business/Models/Profile";

interface Props {
  currentStatus: EProfileStatus;
  profile?: EProfile;
}

const ProgressProfileStatus = ({currentStatus, profile}: Props) => {
  const styles = customStyles();
  const resources = Resources.get().forms.selectProfile.profileStatus;
  const steps: StepsProps[] = [];

  steps.push({label: resources.requested, key: "requested"});

  if (profile === EProfile.client || !profile) {
    steps.push({
      label: resources.filled_data,
      key: EProfileStatus.pendingDocuments,
    });
  } else if (currentStatus === EProfileStatus.pendingDocuments) {
    steps.push({
      label: resources.pending_documents,
      key: EProfileStatus.pendingDocuments,
    });
  } else {
    steps.push({
      label: resources.sent_document,
      key: EProfileStatus.pendingDocuments,
    });
  }

  steps.push({label: resources.review, key: EProfileStatus.review});

  if (currentStatus === EProfileStatus.disapproved) {
    steps.push({label: resources.disapproved, key: EProfileStatus.disapproved});
  } else {
    steps.push({label: resources.approved, key: EProfileStatus.approved});
  }

  return (
    <View {...styles.container}>
      <StepsComponents
        currentStatus={currentStatus}
        steps={steps}
        profile={profile}
      />
    </View>
  );
};

export default ProgressProfileStatus;
