import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = () => {
  return {
    activeStep: {
      borderColor: ThemesApp.getTheme().colors.green[600],
      backgroundColor: "transparent",
      textColor: ThemesApp.getTheme().colors.textColor,
      stepNumColor: ThemesApp.getTheme().colors.dark[100],
      barColor: ThemesApp.getTheme().colors.green[600],
      borderWidth: 4,
    },

    completeStep: {
      borderColor: ThemesApp.getTheme().colors.green[600],
      backgroundColor: ThemesApp.getTheme().colors.green[600],
      textColor: ThemesApp.getTheme().colors.muted[400],
      stepNumColor: ThemesApp.getTheme().colors.white,
      barColor: ThemesApp.getTheme().colors.green[600],
    },

    disapprovedStep: {
      borderColor: ThemesApp.getTheme().colors.danger[600],
      backgroundColor: ThemesApp.getTheme().colors.danger[400],
      textColor: ThemesApp.getTheme().colors.danger[600],
      stepNumColor: ThemesApp.getTheme().colors.white,
      barColor: ThemesApp.getTheme().colors.danger[600],
    },

    approvedStep: {
      borderColor: ThemesApp.getTheme().colors.green[600],
      backgroundColor: ThemesApp.getTheme().colors.green[600],
      textColor: ThemesApp.getTheme().colors.green[600],
      stepNumColor: ThemesApp.getTheme().colors.white,
      barColor: ThemesApp.getTheme().colors.green[600],
    },

    pendingStep: {
      borderColor: ThemesApp.getTheme().colors.warning[600],
      backgroundColor: "transparent",
      textColor: ThemesApp.getTheme().colors.warning[600],
      stepNumColor: ThemesApp.getTheme().colors.warning[600],
      barColor: ThemesApp.getTheme().colors.warning[600],
      borderWidth: 4,
    },
  };
};

export default customStyles;
