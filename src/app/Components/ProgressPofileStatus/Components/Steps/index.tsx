import React from "react";
import { ProgressStep, ProgressSteps } from "react-native-progress-steps";
import { RFValue } from "react-native-responsive-fontsize";
import customStyles from "src/app/Components/ProgressPofileStatus/Components/Steps/styles";
import { verticalScale } from "src/app/Utils/Metrics";
import EProfileStatus from "src/business/Enums/Models/EProfileStatus";
import { EProfile } from "src/business/Models/Profile";

export interface StepsProps {
  label: string;
  key: string;
}
interface Props {
  currentStatus: EProfileStatus;
  steps: StepsProps[];
  profile?: EProfile;
}

const StepsComponents = ({ currentStatus, steps, profile }: Props) => {
  const styles = customStyles();
  let statusToStep = 1;

  switch (currentStatus) {
    case EProfileStatus.pendingDocuments:
      statusToStep = 1;
      break;
    case EProfileStatus.review:
      statusToStep = 2;
      break;
    case EProfileStatus.approved:
      statusToStep = 3;
      break;
    case EProfileStatus.disapproved:
      statusToStep = 3;
      break;

    default:
      profile === EProfile.client || !profile ? (statusToStep = 3) : (statusToStep = 1);
      break;
  }

  const style =
    currentStatus === "disapproved"
      ? styles.disapprovedStep
      : currentStatus === "approved"
      ? styles.approvedStep
      : currentStatus === "pendingDocuments" || currentStatus === "review"
      ? styles.pendingStep
      : styles.activeStep;

  return (
    <ProgressSteps
      activeStep={statusToStep}
      activeStepIconBorderColor={style.borderColor}
      activeStepIconColor={style.backgroundColor}
      activeLabelColor={style.textColor}
      activeStepNumColor={style.stepNumColor}
      completedStepIconColor={styles.completeStep.barColor}
      completedProgressBarColor={styles.completeStep.barColor}
      completedLabelColor={styles.completeStep.textColor}
      borderWidth={styles.activeStep.borderWidth}
      labelFontSize={RFValue(10)}
      topOffset={verticalScale(10)}
      marginBottom={verticalScale(40)}
    >
      {steps.map((step) => (
        <ProgressStep key={step.key} label={step.label} removeBtnRow={true} />
      ))}
    </ProgressSteps>
  );
};

export default StepsComponents;
