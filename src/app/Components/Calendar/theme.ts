import { Theme } from "react-native-calendars/src/types";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

type HeaderType = {
  "stylesheet.calendar.header": object;
};

const customTheme = (): Theme & HeaderType => {
  return {
    "stylesheet.calendar.header": {
      dayHeader: {
        color: ThemesApp.getTheme().colors.primary[600],
        fontWeight: "bold",
      },
      week: {
        marginTop: 5,
        flexDirection: "row",
        justifyContent: "space-between",
        backgroundColor: ThemesApp.getTheme().colors.gray[200],
        borderRadius: 10,
        padding: 5,
        color: "black",
      },
    },
    calendarBackground: ThemesApp.getTheme().colors.cardList,
    dayTextColor: ThemesApp.getTheme().colors.textColor,
    monthTextColor: ThemesApp.getTheme().colors.textColor,
    dotColor: ThemesApp.getTheme().colors.primary[600],
    selectedDayBackgroundColor: ThemesApp.getTheme().colors.primary[600],
    selectedDayTextColor: ThemesApp.getTheme().colors.white,
    todayTextColor: ThemesApp.getTheme().colors.primary[600],
    todayBackgroundColor: ThemesApp.getTheme().colors.gray[200],
  };
};

export default customTheme;
