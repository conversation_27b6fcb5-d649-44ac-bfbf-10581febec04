import React, {useEffect, useState} from "react";
import customStyles from "src/app/Components/Calendar/styles";
import customTheme from "src/app/Components/Calendar/theme";

import {Button, VStack} from "native-base";
import {Calendar, DateData} from "react-native-calendars";
import useTranslation from "src/app/Hooks/useTranslation";
import {ChevronLeft, ChevronRight} from "src/assets/Icons/Flaticon";
import {formatDateToDB} from "src/app/Utils/FormatDateToDB";

type Props = {
  startInCurrentDay?: boolean;
  selectedDate?: string;
  setDate: (date: string) => void;
};

const CalendarRN: React.FC<Props> = ({
  selectedDate,
  setDate,
  startInCurrentDay = true,
}) => {
  const styles = customStyles();
  const theme = customTheme();
  const currentDate = formatDateToDB(new Date());
  const [newDate, setNewDate] = useState(selectedDate);
  const {calendar: resources} = useTranslation();

  useEffect(() => {
    setNewDate(selectedDate);
  }, [selectedDate]);

  return (
    <VStack {...styles.container} space={5}>
      <Calendar
        initialDate={!startInCurrentDay ? newDate : currentDate}
        onDayPress={(value: DateData) => {
          setNewDate(value.dateString);
        }}
        monthFormat="MMMM yyyy"
        theme={theme}
        renderArrow={(direction: string) =>
          direction === "left" ? (
            <ChevronLeft {...styles.icon.style} />
          ) : (
            <ChevronRight {...styles.icon.style} />
          )
        }
        hideExtraDays={true}
        firstDay={0}
        onPressArrowLeft={(subtractMonth: () => void) => subtractMonth()}
        onPressArrowRight={(addMonth: () => void) => addMonth()}
        enableSwipeMonths={true}
        markedDates={
          newDate
            ? {
                [newDate]: {selected: true, marked: true},
              }
            : undefined
        }
      />
      <Button
        onPress={() => {
          if (newDate) setDate(newDate);
        }}
        {...styles.button}>
        {resources.save_date}
      </Button>
    </VStack>
  );
};

export default CalendarRN;
