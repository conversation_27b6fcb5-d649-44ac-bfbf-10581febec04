import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { heightPercentage as hp, moderateScale, widthPercentage as wp } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    icon: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.markAsRead,
      },
    },
    container: {
      style: {
        marginHorizontal: wp("2%"),
      },
    },
    button: {
      style: {
        marginVertical: hp("2%"),
      },
    },
  };
};

export default customStyles;
