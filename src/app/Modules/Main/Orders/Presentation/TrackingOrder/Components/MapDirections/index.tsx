import {Box} from "native-base";
import React, {useEffect, useRef, useState} from "react";
import {Dimensions} from "react-native";
import Config from "react-native-config";
import Geolocation from "react-native-geolocation-service";
import MapView, {Marker} from "react-native-maps";
import MapViewDirections from "react-native-maps-directions";
import {useMap} from "src/app/Components/Maps/Hooks/useMap";
import ModalProgress from "src/app/Components/ModalProgress";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import customStyles from "src/app/Modules/Main/Orders/Presentation/TrackingOrder/Components/MapDirections/styles";
import {loadDeliverymanLocation} from "src/app/Modules/Main/Orders/Presentation/TrackingOrder/Utils/loadDeliverymanLocation";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useOrder from "src/app/Zustand/Store/useOrder";
import {
  MapMarkerClient,
  MapMarkerDeliveryman,
  MapMarkerStore,
} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {Location} from "src/business/DTOs/Location";
import {ITrackingOrderService} from "src/business/Interfaces/Services/ITrackingOrder";
import {TrackingOrderList} from "src/business/Models/List/TrackingOrder";
import {EProfile} from "src/business/Models/Profile";
import configAppSingleton from "src/business/Singletons/ConfigApp";

const {width, height} = Dimensions.get("screen");

type Props = {
  coords: TrackingOrderList;
  dimensionsMap?: {width: number; height: number};
  zoomLevel?: {min: number; max: number};
  showsMyLocationButton?: boolean;
};

const MapDirections: React.FC<Props> = ({
  coords,
  dimensionsMap,
  zoomLevel,
  showsMyLocationButton,
  ...rest
}) => {
  const styles = customStyles();
  const {region, setRegion, LATITUDE_DELTA, LONGITUDE_DELTA} = useMap();
  const {selectedProfile} = useGeneralSettings();
  const mapView = useRef<MapView>(null);
  const [watchId, setWatchId] = useState<{
    client?: number;
    deliveryman?: number;
  }>();
  const [limit, setLimit] = useState<number>(0);
  const [deliveryCoords, setDeliveryCoords] = useState<Location[]>(
    coords.deliveryman,
  );
  const {showStoreRoute: zustandShowStoreRoute} = useOrder();

  const interval = configAppSingleton.minTimeToGetLocation;

  useEffect(() => {
    if (limit <= deliveryCoords.length && selectedProfile === EProfile.client) {
      const timeoutId = setTimeout(async () => {
        setLimit(limit + 1);
        const data = await loadDeliverymanLocation(coords.orderId);
        setDeliveryCoords(data?.deliveryman || []);
      }, interval);
      setWatchId(prev => ({...prev, client: timeoutId}));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [limit]);

  useEffect(() => {
    if (selectedProfile === EProfile.deliveryman) {
      const id = Geolocation.watchPosition(
        response => {
          const trackingOrderService = container.get<ITrackingOrderService>(
            TOKENS.TrackingOrderService,
          );

          trackingOrderService.createTrackingOrder({
            latitude: response.coords.latitude,
            longitude: response.coords.longitude,
            orderId: coords.orderId,
          });

          setDeliveryCoords([
            ...deliveryCoords,
            {
              latitude: response.coords.latitude,
              longitude: response.coords.longitude,
            },
          ]);
        },
        error => console.log("➡️ Geolocation.watchPosition 🟦 error:", error),
        {
          fastestInterval: interval,
          enableHighAccuracy: true,
        },
      );
      setWatchId(prev => ({...prev, deliveryman: id}));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    return () => {
      if (watchId?.deliveryman) {
        Geolocation.clearWatch(watchId.deliveryman);
      }
      if (watchId?.client) {
        clearTimeout(watchId.client);
      }
    };
  }, [watchId]);

  useEffect(() => {
    if (coords) {
      setRegion({
        ...coords.store,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      });
    }
  }, [setRegion, LATITUDE_DELTA, LONGITUDE_DELTA, coords]);

  const showPrevDeliverymanRoute = new Set(deliveryCoords).size >= 2;

  return deliveryCoords && deliveryCoords.length > 0 ? (
    <Box {...styles.container}>
      <MapView
        showsMyLocationButton={showsMyLocationButton}
        initialRegion={region}
        showsUserLocation={true}
        style={{
          width: dimensionsMap ? dimensionsMap.width : width,
          height: dimensionsMap ? dimensionsMap.height : height,
        }}
        minZoomLevel={zoomLevel ? zoomLevel.min : 1}
        maxZoomLevel={zoomLevel ? zoomLevel.max : 20}
        loadingEnabled={true}
        ref={mapView}
        {...rest}>
        {/* Only render MapViewDirections if we have valid coordinates */}
        {(() => {
          const origin = deliveryCoords[deliveryCoords.length - 1];
          const destination =
            zustandShowStoreRoute && coords?.store
              ? coords.store
              : coords.client;

          // Check if coordinates are valid (not 0,0 and not undefined)
          const isValidOrigin =
            origin && origin.latitude !== 0 && origin.longitude !== 0;
          const isValidDestination =
            destination &&
            destination.latitude !== 0 &&
            destination.longitude !== 0;

          if (isValidOrigin && isValidDestination) {
            return (
              <MapViewDirections
                apikey={Config.ANDROID_MAPS_KEY!}
                strokeWidth={5}
                origin={origin}
                destination={destination}
                strokeColor={ThemesApp.getTheme().colors.black}
                onError={errorMessage => {
                  console.log("MapViewDirections Error:", errorMessage);
                }}
              />
            );
          }
          return null;
        })()}

        {showPrevDeliverymanRoute &&
          (() => {
            const origin = deliveryCoords[0];
            const destination = deliveryCoords[deliveryCoords.length - 1];

            // Check if coordinates are valid (not 0,0 and not undefined)
            const isValidOrigin =
              origin && origin.latitude !== 0 && origin.longitude !== 0;
            const isValidDestination =
              destination &&
              destination.latitude !== 0 &&
              destination.longitude !== 0;

            if (isValidOrigin && isValidDestination) {
              return (
                <MapViewDirections
                  apikey={Config.ANDROID_MAPS_KEY!}
                  strokeWidth={5}
                  origin={origin}
                  destination={destination}
                  strokeColor={ThemesApp.getTheme().colors.gray[400]}
                  waypoints={
                    deliveryCoords.length > 2 ? deliveryCoords : undefined
                  }
                  splitWaypoints
                  onError={errorMessage => {
                    console.log(
                      "MapViewDirections Deliveryman Route Error:",
                      errorMessage,
                    );
                  }}
                />
              );
            }
            return null;
          })()}

        <Marker coordinate={deliveryCoords[deliveryCoords.length - 1]}>
          <MapMarkerDeliveryman {...styles.deliverymanIcon.style} />
        </Marker>
        <Marker coordinate={coords.store}>
          <MapMarkerStore {...styles.routeIcon.style} />
        </Marker>
        <Marker coordinate={coords.client}>
          <MapMarkerClient {...styles.routeIcon.style} />
        </Marker>
      </MapView>
    </Box>
  ) : (
    <ModalProgress />
  );
};
export default MapDirections;
