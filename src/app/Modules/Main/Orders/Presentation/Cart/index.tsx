import {useFocusEffect} from "@react-navigation/native";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {Box, Button, FlatList, HStack, Text, View} from "native-base";
import React, {useEffect, useState} from "react";
import StackHeader from "src/app/Components/Header/StackHeader";
import ItemNotFound from "src/app/Components/ItemNotFound";
import ModalCustom from "src/app/Components/ModalItensCart";
import showToastWarning from "src/app/Components/Toast/toastWarning";
import useTranslation from "src/app/Hooks/useTranslation";
import ProductAnimated from "src/app/Modules/Main/Orders/Presentation/Cart/Components/ProductAnimated";
import ProductResume from "src/app/Modules/Main/Orders/Presentation/Cart/Components/ProductResume";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Cart/styles";
import HandleOrders from "src/app/Modules/Main/Orders/Presentation/Cart/Utils/handleOrders";
import useGetStoreAvailability from "src/app/Modules/Main/Stores/Query/useGetStoreAvailability";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import navigateHome from "src/app/Utils/NavigateHome";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useOrder from "src/app/Zustand/Store/useOrder";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {CartList} from "src/business/Models/List/CartList";
import configAppSingleton from "src/business/Singletons/ConfigApp";

export type Props = NativeStackScreenProps<MainAppStackParamList, "Cart">;

const handleOrders = container.get<HandleOrders>(TOKENS.HandleOrders);

const Cart: React.FC<Props> = ({navigation, route}) => {
  const styles = customStyles();
  const {cartList, eraseOrderTransactionData, clearOrderData} = useOrder();
  const [visible, setVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const {
    stores: {home: resourcesHome},
    orders: resources,
  } = useTranslation();
  const [collapse, setCollapse] = useState<boolean>(true);
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const storeId =
    cartList && cartList.length > 0 ? cartList[0].orderProduct.storeId : "";

  const storeAvailabilityQuery = useGetStoreAvailability(storeId);

  useFocusEffect(
    React.useCallback(() => {
      const isStoreOpen = storeAvailabilityQuery.data;

      if (isStoreOpen === true) {
        setIsOpen(isStoreOpen);
      }

      if (isStoreOpen === false) {
        showToastWarning(resourcesHome.text.toleranceTimeEndCart, {
          duration: 4000,
          placement: "top",
        });
      }

      eraseOrderTransactionData();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [storeAvailabilityQuery.data]),
  );

  const handleOpenStore = async () => {
    if (cartList && cartList.length > 0) {
      setIsLoading(true);
      const store = await handleOrders.getStore(
        cartList[0].orderProduct.storeId,
      );
      if (store) {
        rootNavigation("MainApp", {
          screen: "StoreShowCase",
          params: {id: store.id},
        });
      }
      setIsLoading(false);
    } else {
      rootNavigation("MainApp", {
        screen: "StoreList",
      });
    }
  };

  const separatorComponent = () => {
    return <Box {...styles.boxSeparator} />;
  };

  const renderItem = ({item}: {item: CartList}) => {
    return (
      <Box {...styles.boxProducts}>
        <ProductResume cartItem={item} />
      </Box>
    );
  };

  useEffect(() => {
    navigation.addListener("beforeRemove", e => {
      e.preventDefault();
      if (cartList && cartList.length > 0) {
        navigation.dispatch(e.data.action);
      } else {
        navigateHome();
      }
    });
    return () => {
      navigation.removeListener("beforeRemove", _ => {});
    };
  }, [cartList, navigation]);

  return (
    <Box {...styles.mainBox}>
      <StackHeader navigation={navigation} route={route} />
      {cartList && cartList?.length > 0 && (
        <HStack {...styles.pressableHeaderHStack}>
          <Button
            variant="link"
            onPress={() => setVisible(!visible)}
            _pressed={{opacity: 0.5}}
            isDisabled={!cartList || cartList?.length === 0}
            _text={{...styles.textLinkButtonDeleteAll}}>
            {resources.cart.label.deleteAll}
          </Button>
        </HStack>
      )}

      <Box flex={collapse ? 3 : 2} {...styles.boxContainer}>
        <FlatList
          data={cartList}
          renderItem={renderItem}
          keyExtractor={(item: CartList) =>
            item.orderItem.productId +
            item.orderItem.orderId +
            item.orderItem.quantity
          }
          ItemSeparatorComponent={separatorComponent}
          contentContainerStyle={{...styles.contentContainerFlatlist.style}}
          ListEmptyComponent={
            <ItemNotFound
              flex={1}
              title={resources.cart.label.emptyCart}
              directionComponent="column"
            />
          }
        />
      </Box>
      <Box flex={1} />
      <View {...styles.boxFooter}>
        <ProductAnimated collapse={collapse} setCollapse={setCollapse} />
        <Box flex={1} />
        <Box {...styles.hstackFooter}>
          <Text bold {...styles.textFooter}>
            {resources.cart.label.totalOrder}
          </Text>
          <Text {...styles.textFooter}>
            {
              maskField({
                value: cartList
                  ? handleOrders.sumOrders(cartList) +
                    configAppSingleton.shippingPrice
                  : "0.00",
                mask: BRL_CURRENCY_DECIMAL(10),
              }).masked
            }
          </Text>
        </Box>

        <Button
          _text={{...styles.buttonText}}
          onPress={() => {
            rootNavigation("MainApp", {
              screen: "OrderSummary",
            });
          }}
          isDisabled={isOpen === false || !cartList || cartList?.length <= 0}
          {...styles.buttonFooter}>
          {resources.cart.button.continue}
        </Button>

        <Button
          onPress={handleOpenStore}
          isLoading={isLoading}
          _pressed={{opacity: 0.5}}
          _text={{...styles.buttonText}}
          {...styles.buttonFooterAdd}>
          {!cartList || cartList?.length === 0
            ? resources.cart.label.addItems
            : resources.cart.label.addMoreItems}
        </Button>
      </View>
      <ModalCustom
        visibility={visible}
        onClose={() => setVisible(!visible)}
        onConfirm={() => {
          clearOrderData();
          setVisible(!visible);
        }}
        isCart={true}
      />
    </Box>
  );
};

export default Cart;
