import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainBox: {
      position: "relative",
      flex: 1,
    },

    boxContainer: {
      style: {
        width: wp("100%"),
      },
    },

    contentContainerFlatlist: {
      style: {
        paddingBottom: hp("8%"),
        flexGrow: 1,
      },
    },

    boxProducts: {
      style: {
        marginHorizontal: wp("5%"),
        marginVertical: wp("2%"),
        
      },
    },

    boxProductContainer: {
      style: {
        width: wp("100%"),
        paddingHorizontal: wp("5%"),
        marginTop: hp("1%"),
        marginBottom: hp("1%"),
        flexDirection: "row",
        justifyContent: "space-between",
        elevation: 3,
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        overflow: "hidden",
      },
    },

    amountStack: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },

    pressableDecreaseAmount: {
      style: {
        marginRight: wp("2%"),
      },
    },

    pressableIncreaseAmount: {
      style: {
        marginLeft: wp("2%"),
      },
    },

    flatlist: {
      style: {flex: 1},
    },

    textAmount: {
      fontSize: RFValue(20),
      style: {
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    decreaseOrder: {
      size: moderateScale(25),
    },

    increaseOrder: {
      color: ThemesApp.getTheme().colors.secondary[400],
      size: moderateScale(25),
    },

    deleteOrder: {
      color: ThemesApp.getTheme().colors.secondary[400],
      size: moderateScale(25),
    },

    addCart: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
        fill: ThemesApp.getTheme().colors.iconUploadLight,
        marginLeft: wp("2%"),
      },
    },

    textHeader: {
      style: {
        marginHorizontal: wp("5%"),
        marginTop: hp("2%"),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    pressableHeaderAddProduct: {
      style: {
        marginLeft: wp("5%"),
        alignItems: "flex-end",
      },
    },

    boxFooter: {
      style: {
        width: wp("100%"),
        paddingHorizontal: wp("3%"),
        paddingVertical: hp("1%"),
        borderTopLeftRadius: moderateScale(15),
        borderTopRightRadius: moderateScale(15),
        borderTopWidth: moderateScale(1),
        borderRightWidth: moderateScale(1),
        borderLeftWidth: moderateScale(1),
        borderColor: ThemesApp.getTheme().colors.muted[300],
        position: "absolute",
        bottom: 0,
        backgroundColor: ThemesApp.getTheme().colors.cardList,
      },
    },

    pressableArrow: {
      style: {
        alignSelf: "center",
        paddingVertical: verticalScale(5),
      },
    },

    hstackFooter: {
      style: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: ThemesApp.getTheme().colors.cardList,
      },
    },

    textFooter: {
      style: {
        fontSize: RFValue(13),
        marginHorizontal: horizontalScale(4),
        fontWeight: "bold",
      },
    },

    buttonFooter: {
      w: "full",
      style: {
        height: moderateScale(50),
        marginTop: hp("2%"),
        backgroundColor: ThemesApp.getTheme().colors.buttonFooterCart,
      },
    },

    buttonFooterAdd: {
      w: "full",
      style: {
        height: moderateScale(50),
        marginVertical: hp("1%"),
        backgroundColor: ThemesApp.getTheme().colors.primary[400],
      },
    },

    buttonText: {
      style: {
        fontSize: RFValue(14),
      },
    },

    textLinkButtons: {
      style: {
        fontSize: RFValue(14),
        color: ThemesApp.getTheme().colors.iconHelp,
      },
    },

    textLinkButtonDeleteAll: {
      style: {
        marginTop: 16,
        marginHorizontal: wp("4%"),
        fontSize: RFValue(12),
        color: ThemesApp.getTheme().colors.deleteAccountText,
      },
    },

    pressableHeaderHStack: {
      style: {
        alignItems: "center",
        justifyContent: "flex-end",
      },
    },

    clearCart: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
        marginLeft: wp("2%"),
      },
    },

    animatedView: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
      },
    },

    buttonCalculate: {
      p: 0,
    },

    textButtonCalculate: {
      style: {
        fontSize: RFValue(13),
        marginHorizontal: horizontalScale(4),
        textDecorationLine: "underline",
        color: ThemesApp.getTheme().colors.iconHelp,
      },
    },

    chevronIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.textApp,
      },
    },
  };
};

export default customStyles;
