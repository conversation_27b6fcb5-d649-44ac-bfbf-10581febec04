import { inject, injectable } from "inversify";

import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import { IFileService } from "src/business/Interfaces/Services/IFile";
import { IProductService } from "src/business/Interfaces/Services/IProduct";
import { IStoreService } from "src/business/Interfaces/Services/IStore";
import { CartList } from "src/business/Models/List/CartList";
import AppError from "src/business/Tools/AppError";
import { isEqual } from "lodash";

@injectable()
class HandleOrders {
  fileService: IFileService;

  productService: IProductService;

  storeService: IStoreService;

  constructor(
    @inject(TOKENS.FileService) fileService: IFileService,
    @inject(TOKENS.ProductService) productService: IProductService,
    @inject(TOKENS.StoreService) storeService: IStoreService,
  ) {
    this.fileService = fileService;
    this.productService = productService;
    this.storeService = storeService;
  }

  increaseOrder = (selectedOrder: CartList) => {
    const newOrderItem = selectedOrder;
    newOrderItem.orderItem.quantity += 1;
    newOrderItem.orderItem.totalPrice = this.handleCalculateOrderPrice(
      newOrderItem.orderItem.totalPrice + newOrderItem.orderItem.unitPrice,
    );
    return newOrderItem;
  };

  decreaseOrder = (selectedOrder: CartList) => {
    const newOrderItem = selectedOrder;
    newOrderItem.orderItem.quantity -= 1;
    newOrderItem.orderItem.totalPrice = this.handleCalculateOrderPrice(
      newOrderItem.orderItem.totalPrice - newOrderItem.orderItem.unitPrice,
    );
    return newOrderItem;
  };

  sumOrders = (orders: CartList[]) => {
    const totalSum = orders.reduce(
      (accumulator, currentValue) => this.handleCalculateOrderPrice(accumulator + currentValue.orderItem.totalPrice),
      0,
    );
    return totalSum;
  };

  getStore = async (storeId: string) => {
    const response = await this.storeService.getStoreInfo(storeId);

    if (response instanceof AppError) {
    } else {
      return response;
    }
  };

  removeSelectedOrder = (selectedOrder: CartList, orderList?: CartList[]) => {
    if (orderList) {
      const newOrderList = [...orderList];
      const indexOrder = newOrderList.findIndex((item) => item.orderItem.orderId === selectedOrder.orderItem.orderId);
      if (indexOrder >= 0) {
        newOrderList.splice(indexOrder, 1);
        return [...newOrderList];
      }
    }

    return undefined;
  };

  alterOrdersArray = (selectedOrder: CartList, orderArray?: CartList[]) => {
    if (orderArray) {
      const newOrders = orderArray.map((order) => {
        if (order.orderItem.orderId === selectedOrder.orderItem.orderId) {
          if (!isEqual(order, selectedOrder)) {
            order = selectedOrder;
          }
        }
        return order;
      });
      return [...newOrders];
    }
    return [selectedOrder];
  };

  handleCalculateOrderPrice = (price: number, decimalPlaces: number = 2) => {
    return Number(`${Math.round(parseFloat(`${price}e${decimalPlaces}`))}e-${decimalPlaces}`);
  };
}

export default HandleOrders;
