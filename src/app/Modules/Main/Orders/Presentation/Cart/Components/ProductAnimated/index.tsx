import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {Box, Pressable, Text} from "native-base";
import React, {useRef, useState} from "react";
import {Animated} from "react-native";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Cart/Components/ProductAnimated/styles";
import HandleOrders from "src/app/Modules/Main/Orders/Presentation/Cart/Utils/handleOrders";
import {verticalScale} from "src/app/Utils/Metrics";
import useOrder from "src/app/Zustand/Store/useOrder";
import {ChevronDown, ChevronUp} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import configAppSingleton from "src/business/Singletons/ConfigApp";

type Props = {
  collapse: boolean;
  setCollapse: React.Dispatch<React.SetStateAction<boolean>>;
};

const handleOrders = container.get<HandleOrders>(TOKENS.HandleOrders);

const ProductAnimated = ({collapse, setCollapse}: Props) => {
  const styles = customStyles();
  const {cartList} = useOrder();
  const {orders: resources} = useTranslation();
  const minHeight = new Animated.Value(verticalScale(25));
  const maxHeight = new Animated.Value(verticalScale(105));
  const currentHeight = useRef(new Animated.Value(verticalScale(25))).current;

  const animatedStyles = {
    animatedView: {
      footerMinHeight: {
        height: currentHeight,
      },
    },
  };

  return (
    <Animated.View
      style={[
        animatedStyles.animatedView.footerMinHeight,
        styles.animatedView.style,
      ]}>
      <Pressable
        _pressed={{opacity: 0.8}}
        onPress={() => {
          Animated.timing(currentHeight, {
            toValue: collapse ? maxHeight : minHeight,
            duration: 500,
            useNativeDriver: false,
          }).start();

          setCollapse(!collapse);
        }}
        {...styles.pressableArrow}>
        {collapse ? (
          <ChevronUp {...styles.chevronIcon.style} />
        ) : (
          <ChevronDown {...styles.chevronIcon.style} />
        )}
      </Pressable>

      {!collapse ? (
        <>
          <Box {...styles.hstackFooter}>
            <Text {...styles.textFooter}>
              {resources.cart.label.orderPrice}
            </Text>
            <Text {...styles.textFooter}>
              {
                maskField({
                  value: cartList ? handleOrders.sumOrders(cartList) : "0.00",
                  mask: BRL_CURRENCY_DECIMAL(10),
                }).masked
              }
            </Text>
          </Box>

          <Box {...styles.hstackFooter}>
            <Text {...styles.textFooter}>
              {resources.cart.label.deliveryFee}
            </Text>
            <Text {...styles.textFooter}>
              {
                maskField({
                  value: configAppSingleton.shippingPrice,
                  mask: BRL_CURRENCY_DECIMAL(10),
                }).masked
              }
            </Text>
          </Box>
        </>
      ) : null}
    </Animated.View>
  );
};

export default ProductAnimated;
