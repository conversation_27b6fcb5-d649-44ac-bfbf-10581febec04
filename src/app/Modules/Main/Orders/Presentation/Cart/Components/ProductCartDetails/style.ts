import {RFValue} from "react-native-responsive-fontsize";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    textTitleDescription: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(11),
        lineHeight: RFValue(12),
        fontWeight: "bold",
        textAlign: "left",
        flex: 1,
      },
    },

    textDescription: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(11),
        lineHeight: RFValue(12),
        textAlign: "left",
        flex: 1,
      },
    },

    textObservation: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(11),
        lineHeight: RFValue(12),
        textAlign: "justify",
        flex: 1,
      },
    },
  };
};

export default customStyles;
