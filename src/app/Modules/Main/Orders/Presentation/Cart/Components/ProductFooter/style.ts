import {RFValue} from "react-native-responsive-fontsize";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    productFooter: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    hStackFooter: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    hStackDetailsPrice: {
      w: "32%",
      style: {
        alignItems: "center",
        justifyContent: "space-between",
      },
    },
    pressableDecreaseQuantity: {
      paddingRight: "12%",
    },

    pressableIncreaseQuantity: {
      paddingLeft: "12%",
    },
    minusSquare: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
      },
    },
    textQuantity: {
      style: {
        fontSize: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    plusSquare: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },
    textPrice: {
      style: {
        textAlignVertical: "bottom",
        fontWeight: "bold",
        fontSize: RFValue(14),
        textTransform: "capitalize",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
  };
};

export default customStyles;
