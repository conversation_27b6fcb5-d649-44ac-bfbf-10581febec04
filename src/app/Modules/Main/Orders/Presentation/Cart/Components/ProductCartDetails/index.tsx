import {Box, Text, VStack} from "native-base";
import React from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Cart/Components/ProductCartDetails/style";
import {OrderItem} from "src/business/Models/OrderItem";

type Props = {
  orderItem: OrderItem;
};

const ProductCartDetails: React.FC<Props> = ({orderItem}) => {
  const styles = customStyles();
  const {
    orders: {cart: resources},
  } = useTranslation();

  return (
    <VStack space={1}>
      {orderItem.attributes?.map(item => {
        return (
          <Box key={item.id}>
            <Text
              ellipsizeMode="tail"
              numberOfLines={3}
              {...styles.textTitleDescription}>
              {item.name}
            </Text>

            {item.attributeOption?.map(subitem => {
              if (subitem.checked) {
                return (
                  <Text {...styles.textDescription} key={subitem.id}>
                    {subitem.value}
                  </Text>
                );
              }
              return null;
            })}
          </Box>
        );
      })}
      {orderItem.observation && (
        <Text {...styles.textObservation}>
          {resources.label.observation} {orderItem.observation}
        </Text>
      )}
    </VStack>
  );
};

export default ProductCartDetails;
