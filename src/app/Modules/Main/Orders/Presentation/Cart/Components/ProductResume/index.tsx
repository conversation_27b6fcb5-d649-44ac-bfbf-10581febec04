import {
  Badge,
  Box,
  Button,
  HStack,
  Image,
  Pressable,
  Text,
  VStack,
} from "native-base";
import React, {useState} from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import ProductCartDetails from "src/app/Modules/Main/Orders/Presentation/Cart/Components/ProductCartDetails";
import ProductFooter from "src/app/Modules/Main/Orders/Presentation/Cart/Components/ProductFooter";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Cart/Components/ProductResume/style";
import HandleOrders from "src/app/Modules/Main/Orders/Presentation/Cart/Utils/handleOrders";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useOrder from "src/app/Zustand/Store/useOrder";
import {CancelCircle, ProductPhoto, Trash} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {CartList} from "src/business/Models/List/CartList";

type Props = {
  cartItem: CartList;
};

const handleOrders = container.get<HandleOrders>(TOKENS.HandleOrders);

const ProductResume: React.FC<Props> = ({cartItem}) => {
  const styles = customStyles();
  const [trashClick, setTrashClick] = useState(false);

  const photoUrl = cartItem.orderProduct?.files?.[0]?.url;

  const {cartList, saveMultipleOrders} = useOrder();
  const {
    orders: {cart: resources},
  } = useTranslation();

  const handleOpenOrderScreen = () => {
    rootNavigation("MainApp", {
      screen: "OrdersCreate",
      params: {
        id: cartItem.orderProduct.id,
        data: cartItem,
        isUpdate: true,
      },
    });
  };

  const deleteItem = (selectedOrder: CartList) => {
    const newOrders = handleOrders.removeSelectedOrder(selectedOrder, cartList);
    if (newOrders) saveMultipleOrders(newOrders);
    setTrashClick(false);
  };

  return (
    <>
      {trashClick && (
        <Badge
          variant="subtle"
          _text={{...styles.badgeText}}
          colorScheme="danger"
          startIcon={<Trash {...styles.trashBadge.style} />}
          {...styles.badge}>
          {resources.label.tapDelete}
        </Badge>
      )}
      <Pressable
        {...styles.pressableContainer}
        onPress={() => {
          if (trashClick) {
            deleteItem(cartItem);
          } else {
            handleOpenOrderScreen();
          }
        }}
        isFocused={trashClick}
        _pressed={{opacity: 0.1}}
        _focus={{
          style: {...styles.pressableContainer.style, backgroundColor: "red"},
          opacity: 0.1,
        }}>
        <Box {...styles.mainHStack}>
          {photoUrl ? (
            <Image
              source={{
                uri: photoUrl,
              }}
              alt="Product food"
              style={styles.imageStore.style}
            />
          ) : (
            <Box {...styles.avatarIconBox}>
              <ProductPhoto {...styles.avatarIcon.style} />
            </Box>
          )}
          <VStack space={2} {...styles.vStackStore}>
            <HStack {...styles.hStackProductHeader}>
              <HStack {...styles.hStackTitleHeader}>
                <Text
                  ellipsizeMode="tail"
                  numberOfLines={2}
                  {...styles.textTitle}>
                  {cartItem.orderProduct.name}
                </Text>
              </HStack>

              <Pressable
                _pressed={{opacity: 0.5}}
                onPress={() => setTrashClick(true)}>
                <Trash {...styles.deleteIcon.style} />
              </Pressable>
            </HStack>
            <ProductCartDetails orderItem={cartItem.orderItem} />
            <ProductFooter cartItem={cartItem} />
          </VStack>
        </Box>
      </Pressable>
      {trashClick && (
        <Button
          variant="unstyled"
          rightIcon={<CancelCircle {...styles.cancelCircle.style} />}
          _pressed={{opacity: 0.5}}
          onPress={() => setTrashClick(!trashClick)}
          _text={{...styles.textCancelBadge}}
          {...styles.iconCancel}>
          Cancelar
        </Button>
      )}
    </>
  );
};

export default ProductResume;
