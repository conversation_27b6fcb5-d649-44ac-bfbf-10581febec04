import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {HStack, Pressable, Text, VStack} from "native-base";
import React from "react";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Cart/Components/ProductFooter/style";
import HandleOrders from "src/app/Modules/Main/Orders/Presentation/Cart/Utils/handleOrders";
import useOrder from "src/app/Zustand/Store/useOrder";
import {SquareMinus, SquarePlus} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {CartList} from "src/business/Models/List/CartList";

type Props = {
  cartItem: CartList;
};

const handleOrders = container.get<HandleOrders>(TOKENS.HandleOrders);

const ProductFooter: React.FC<Props> = ({cartItem}) => {
  const styles = customStyles();
  const {cartList, saveMultipleOrders} = useOrder();

  const addItem = (orderItem: CartList) => {
    const newOrderItem = handleOrders.increaseOrder(orderItem);
    const newOrders = handleOrders.alterOrdersArray(newOrderItem, cartList);
    saveMultipleOrders(newOrders);
  };

  const decreaseItem = (orderItem: CartList) => {
    const newOrderItem = handleOrders.decreaseOrder(orderItem);
    const newOrders = handleOrders.alterOrdersArray(newOrderItem, cartList);
    saveMultipleOrders(newOrders);
  };

  return (
    <HStack {...styles.productFooter}>
      <VStack flex={1}>
        <HStack {...styles.hStackFooter}>
          <HStack {...styles.hStackDetailsPrice}>
            <Pressable
              onPress={() => decreaseItem(cartItem)}
              _pressed={{opacity: 0.5}}
              isDisabled={cartItem.orderItem.quantity <= 1}
              {...styles.pressableDecreaseQuantity}>
              <SquareMinus
                fill={
                  cartItem.orderItem.quantity <= 1
                    ? ThemesApp.getTheme().colors.gray[300]
                    : ThemesApp.getTheme().colors.heartIcon
                }
                {...styles.minusSquare.style}
              />
            </Pressable>
            <Text {...styles.textQuantity}>{cartItem.orderItem.quantity}</Text>
            <Pressable
              onPress={() => addItem(cartItem)}
              _pressed={{opacity: 0.5}}
              isDisabled={cartItem.orderItem.quantity > 99}
              {...styles.pressableIncreaseQuantity}>
              <SquarePlus
                fill={
                  cartItem.orderItem.quantity > 99
                    ? "transparent"
                    : ThemesApp.getTheme().colors.heartIcon
                }
                {...styles.plusSquare.style}
              />
            </Pressable>
          </HStack>

          <Text {...styles.textPrice}>
            {
              maskField({
                value: cartItem.orderItem.totalPrice,
                mask: BRL_CURRENCY_DECIMAL(12),
              }).masked
            }
          </Text>
        </HStack>
      </VStack>
    </HStack>
  );
};

export default ProductFooter;
