import {RFValue} from "react-native-responsive-fontsize";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {horizontalScale, verticalScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    animatedView: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
      },
    },
    pressableArrow: {
      style: {
        alignSelf: "center",
        paddingVertical: verticalScale(5),
      },
    },
    chevronIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.textApp,
      },
    },
    hstackFooter: {
      style: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: ThemesApp.getTheme().colors.cardList,
      },
    },
    textFooter: {
      style: {
        fontSize: RFValue(13),
        marginHorizontal: horizontalScale(4),
      },
    },
  };
};

export default customStyles;
