import {RFValue} from "react-native-responsive-fontsize";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale, verticalScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    badgeText: {
      fontSize: RFValue(12),
      fontWeight: "bold",
    },
    trashBadge: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.trashBadgeProductResume,
      },
    },
    badge: {
      style: {
        borderRadius: moderateScale(10),
        borderWidth: moderateScale(1),
        borderColor: ThemesApp.getTheme().colors.badgeBorderProductResume,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        position: "absolute",
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
      },
    },
    pressableContainer: {
      style: {
        borderColor: ThemesApp.getTheme().colors.borderCard,
        elevation: 2,
        borderRadius: moderateScale(10),
        backgroundColor: ThemesApp.getTheme().colors.cardList,
      
        overflow: "hidden",
        padding: wp("2%"),
      },
    },
    mainHStack: {
      style: {
        alignItems: "center",
        flexDirection: "row",
        overflow: "hidden",
        flex: 1,
      },
    },
    imageStore: {
      style: {
        borderTopLeftRadius: moderateScale(10),
        borderBottomLeftRadius: moderateScale(10),
        width: wp("15%"),
        height: wp("15%"),
      },
    },
    avatarIconBox: {
      height: "full",
      style: {
        borderTopLeftRadius: moderateScale(10),
        borderBottomLeftRadius: moderateScale(10),
        overflow: "hidden",

        width: wp("15%"),
        backgroundColor: ThemesApp.getTheme().colors.muted[200],
        alignItems: "center",
        justifyContent: "center",
      },
    },
    avatarIcon: {
      style: {
        height: moderateScale(35),
        width: moderateScale(35),
        fill: ThemesApp.getTheme().colors.muted[400],
      },
    },
    vStackStore: {
      style: {
        marginLeft: wp("3%"),
        marginRight: wp("1%"),
        flex: 1,
      },
    },
    hStackProductHeader: {
      style: {
        justifyContent: "space-between",
      },
    },
    hStackTitleHeader: {
      style: {
        alignItems: "center",
        flex: 1,
      },
    },
    textTitle: {
      style: {
        flexWrap: "wrap",
        fontWeight: "bold",
        textTransform: "uppercase",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    deleteIcon: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },
    cancelCircle: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.muted[600],
      },
    },
    textCancelBadge: {
      fontSize: RFValue(11),
      lineHeight: RFValue(13),
      color: ThemesApp.getTheme().colors.textCancelBadgeProductResume,
    },
    iconCancel: {
      style: {
        marginTop: verticalScale(5),
        alignItems: "center",
        position: "absolute",
        top: 0,
        right: 0,
      },
    },
  };
};

export default customStyles;
