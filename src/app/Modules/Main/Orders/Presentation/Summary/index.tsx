/* eslint-disable react-hooks/exhaustive-deps */
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {useQueryClient} from "@tanstack/react-query";
import {Box, Divider, ScrollView, Text, View} from "native-base";
import React, {useCallback, useRef, useState} from "react";
import StackHeader from "src/app/Components/Header/StackHeader";
import ModalSimple from "src/app/Components/ModalSimple";
import showToastError from "src/app/Components/Toast/toastError";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import {Components} from "src/app/Context/Utils/Components";
import useTranslation from "src/app/Hooks/useTranslation";
import DeliveryAddress from "src/app/Modules/Main/Orders/Presentation/Summary/Components/DeliveryAddress";
import ModalPix from "src/app/Modules/Main/Orders/Presentation/Summary/Components/ModalPix";
import CardForm from "src/app/Modules/Main/Orders/Presentation/Summary/Components/OrderPaymentMethod/Components/Card";
import SummaryButtonSubmit, {
  ChildRef,
} from "src/app/Modules/Main/Orders/Presentation/Summary/Components/SummaryButtonSubmit";
import SummaryListProduts from "src/app/Modules/Main/Orders/Presentation/Summary/Components/SummaryListProducts";
import SummaryOrderTotals from "src/app/Modules/Main/Orders/Presentation/Summary/Components/SummaryOrderTotals";
import SummaryPaymentMethod from "src/app/Modules/Main/Orders/Presentation/Summary/Components/SummaryPaymentMethod";
import SummaryStoreInfo from "src/app/Modules/Main/Orders/Presentation/Summary/Components/SummaryStoreInfo";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Summary/styles";
import useCreateOrder from "src/app/Modules/Main/Orders/Query/useCreateOrder";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useOrder from "src/app/Zustand/Store/useOrder";
import useUser from "src/app/Zustand/Store/useUser";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {OrderTransactionData} from "src/business/DTOs/OrderTransactionData";
import EPaymentMethod from "src/business/Enums/EPaymentMethod";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {IAddressService} from "src/business/Interfaces/Services/IAddress";
import {IOrderService} from "src/business/Interfaces/Services/IOrder";
import {IOrderStatusService} from "src/business/Interfaces/Services/IOrderStatus";
import {ITransactionService} from "src/business/Interfaces/Services/ITransaction";
import {CreateOrder} from "src/business/Models/Create/Order";
import {EProfile} from "src/business/Models/Profile";
import {UpdateOrder} from "src/business/Models/Update/Order";
import configAppSingleton from "src/business/Singletons/ConfigApp";
import AppError from "src/business/Tools/AppError";

type Props = NativeStackScreenProps<MainAppStackParamList, "OrderSummary">;

const Summary = ({navigation, route}: Props) => {
  const styles = customStyles();
  const [modalCard, setModalCard] = useState<boolean>(false);
  const [pixModal, setPixModal] = useState<boolean>(false);
  const {user} = useUser();
  const {routes, orders} = useTranslation();
  const queryClient = useQueryClient();
  const buttonSubmitRef = useRef<ChildRef>(null);

  const orderService = container.get<IOrderService>(TOKENS.OrderService);
  const transactionService = container.get<ITransactionService>(
    TOKENS.TransactionService,
  );
  const orderStatusService = container.get<IOrderStatusService>(
    TOKENS.OrderStatusService,
  );

  const invalidateQueryLastOrderPayment = () => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.GET_LAST_ORDER_PAYMENT],
    });
  };

  const onSuccess = async (response: {success: boolean; pixKey?: string}) => {
    showToastSuccess(orders.toasts.create_success);
    const {
      orderTransactionData,
      clearOrderData,
      setSelectOrderPaymentMethod,
      getStore,
      setOrderId,
      eraseOrderTransactionData,
    } = useOrder.getState();

    if (orderTransactionData?.paymentMethod !== EPaymentMethod.pix) {
      clearOrderData();
      invalidateQueryLastOrderPayment();
      rootNavigation("MainApp", {
        screen: "StoreList",
        params: {shouldClearFilters: true},
      });
    } else {
      if (response.pixKey) {
        setSelectOrderPaymentMethod({
          pixKey: response.pixKey,
          paymentMethod: EPaymentMethod.pix,
        });
        setPixModal(true);
        if (user) {
          const orderIdCreated =
            await orderService.getLastOrderIdByUserIdAndStoreId(
              user.id,
              getStore().storeId,
            );
          setOrderId(orderIdCreated as string);
        }
      }
    }
    buttonSubmitRef.current?.handleIsLoading();
    eraseOrderTransactionData();
  };

  const onError = async (err: AppError) => {
    Components.showFormErrors(err);
    const {getStore, setOrderId, eraseOrderTransactionData} =
      useOrder.getState();
    if (user) {
      const orderIdCreated =
        await orderService.getLastOrderIdByUserIdAndStoreId(
          user.id,
          getStore().storeId,
        );
      setOrderId(orderIdCreated as string);
    }
    eraseOrderTransactionData();
    buttonSubmitRef.current?.handleIsLoading();
  };

  const createOrder = useCreateOrder({onSuccess, onError});

  const handleCloseModal = () => {
    setModalCard(false);
  };

  const handleCloseModalPix = () => {
    const {clearOrderData} = useOrder.getState();
    clearOrderData();
    invalidateQueryLastOrderPayment();
    rootNavigation("MainApp", {
      screen: "StoreList",
      params: {shouldClearFilters: true},
    });
    setPixModal(false);
  };

  const handleSubmit = () => {
    const {orderTransactionData} = useOrder.getState();

    console.log("handleSubmit - orderTransactionData:", orderTransactionData);
    console.log(
      "handleSubmit - paymentMethod:",
      orderTransactionData?.paymentMethod,
    );
    console.log(
      "handleSubmit - hashCardPaymentPlatform:",
      orderTransactionData?.hashCardPaymentPlatform,
    );

    // Se o método de pagamento é PIX, criar pedido diretamente
    if (orderTransactionData?.paymentMethod === EPaymentMethod.pix) {
      console.log("handleSubmit - PIX selected, creating order directly");
      handleCreateOrder();
    }
    // Se é cartão mas não tem hash, abrir modal do cartão
    else if (!orderTransactionData?.hashCardPaymentPlatform) {
      console.log(
        "handleSubmit - Card selected but no hash, opening card modal",
      );
      setModalCard(true);
    }
    // Se tem hash do cartão, criar pedido
    else {
      console.log("handleSubmit - Card with hash, creating order");
      handleCreateOrder();
    }
  };

  const handleCreateOrder = useCallback(
    async (transactionData?: OrderTransactionData) => {
      const {
        deliveryTime,
        position,
        selectedAddress,
        orderTransactionData,
        cartList,
        orderId,
        getStore,
        setSelectAddress,
        getOrderPrice,
        getTotalPrice,
      } = useOrder.getState();
      const {shippingPrice} = configAppSingleton;
      const addressService = container.get<IAddressService>(
        TOKENS.AddressService,
      );
      setModalCard(false);
      buttonSubmitRef.current?.handleIsLoading();

      let data: CreateOrder | undefined;

      if (cartList && cartList.length > 0 && user) {
        const quantityItems = cartList.reduce(
          (acc, price) => acc + +price.orderItem.quantity,
          0,
        );

        const orderItem = cartList.map(cart => {
          delete cart.orderItem.orderId;
          return cart.orderItem;
        });

        const quantityProducts = cartList.length;

        const longestPreparationTime = orderItem.reduce((longest, current) => {
          if (longest.preparationTime && current.preparationTime) {
            return current.preparationTime > longest.preparationTime
              ? current
              : longest;
          }
          return longest;
        }, orderItem[0]);

        const estimatedDeliveryTime = longestPreparationTime.preparationTime
          ? Math.round(deliveryTime / 60) +
            longestPreparationTime.preparationTime
          : undefined;

        data = {
          addressId: selectedAddress?.id || "",
          userId: user.id,
          storeId: getStore().storeId,
          orderItem,
          quantityItems,
          quantityProducts,
          shippingPrice,
          totalPrice: getTotalPrice(),
          price: getOrderPrice(),
          orderTransactionData,
          estimatedDeliveryTime,
        };
      }

      if (!data) return;

      delete data.orderTransactionData?.savedCard?.cardData;

      if (transactionData) data.orderTransactionData = transactionData;

      if (data.orderTransactionData && !data.orderTransactionData?.amount) {
        data.orderTransactionData = {
          ...data.orderTransactionData,
          amount: data.totalPrice,
        };
      }

      if (data.addressId === "current") {
        if (position.currentAddress) {
          addressService
            .createWithoutRelating(position.currentAddress)
            .then(createdAddressId => {
              if (createdAddressId instanceof AppError) {
                Components.showFormErrors(createdAddressId);
              } else {
                if (data) data.addressId = createdAddressId;
              }
            });
        } else {
          showToastError(orders.toasts.invalid_address, {duration: 4000});
        }
      }

      if (orderId) {
        const updateOrder: UpdateOrder = {id: orderId, ...data};
        await orderService.updateOrder(updateOrder);
        await handleRetryTransaction(
          data.orderTransactionData!,
          user!.id,
          orderId,
        );

        return;
      }
      createOrder.mutate(data);
      setSelectAddress(undefined);
    },
    [],
  );

  const handleRetryTransaction = async (
    data: OrderTransactionData,
    userId: string,
    orderId: string,
  ) => {
    const {clearOrderData, eraseOrderTransactionData} = useOrder.getState();
    const result = await transactionService.createTransaction(
      userId,
      orderId,
      data,
    );
    if (result instanceof AppError) {
      Components.showFormErrors(result);
      buttonSubmitRef.current?.handleIsLoading();
      eraseOrderTransactionData();
      return false;
    }
    await orderStatusService.changeOrderStatus(
      orderId,
      {value: EOrderStatusValue.payment_made},
      EProfile.manager,
    );
    showToastSuccess(orders.toasts.payment_success);
    clearOrderData();
    rootNavigation("MainApp", {
      screen: "StoreList",
      params: {shouldClearFilters: true},
    });

    return result;
  };

  return (
    <>
      <ScrollView>
        <StackHeader
          navigation={navigation}
          route={route}
          title={routes.OrderSummary}
        />
        <Box {...styles.container}>
          <SummaryStoreInfo />
          <DeliveryAddress />
          <Divider {...styles.divider} />
          <View {...styles.nameContainer}>
            <Text {...styles.storeName}>{orders.label.order}</Text>
          </View>

          <SummaryListProduts />
          <Divider {...styles.divider} />
          <SummaryPaymentMethod />
          <Divider {...styles.divider} />
          <SummaryOrderTotals />
        </Box>
        {pixModal ? (
          <ModalPix visible={pixModal} onClose={handleCloseModalPix} />
        ) : null}

        {modalCard ? (
          <ModalSimple
            titleHeader={orders.orderPaymentMethods.label.cardData}
            onClose={handleCloseModal}
            visibility={modalCard}
            removeCloseButton
            useKeyboard>
            <CardForm
              callback={(transactionData?: OrderTransactionData) =>
                handleCreateOrder(transactionData)
              }
            />
          </ModalSimple>
        ) : null}
      </ScrollView>
      <SummaryButtonSubmit submit={handleSubmit} ref={buttonSubmitRef} />
    </>
  );
};

export default Summary;
