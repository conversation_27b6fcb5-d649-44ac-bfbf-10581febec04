import {RFValue} from "react-native-responsive-fontsize";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      // flex: 1,
    },

    containerCard: {
      // flex: 1,
      rounded: "xl",
      style: {
        borderColor: ThemesApp.getTheme().colors.muted[300],
        borderWidth: 1,
        marginHorizontal: wp("5%"),
      },
    },

    vstack: {
      style: {
        minHeight: moderateScale(280),
        marginHorizontal: wp("5%"),
        marginVertical: verticalScale(10),
      },
    },

    hstack: {
      style: {
        justifyContent: "flex-start",
        alignItems: "center",
      },
    },

    inputLabelGrayed: {
      style: {
        color: ThemesApp.getTheme().colors.muted[400],
      },
    },

    inputCvc: {
      minWidth: "70",
    },

    inputBoxExpiration: {
      flex: 2,
    },

    inputBoxCvc: {
      flex: 1,
    },

    submitButton: {
      style: {
        marginHorizontal: wp("5%"),
        marginVertical: verticalScale(10),
      },
    },

    selectPaymentMethod: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
      },
    },

    boxSelect: {
      style: {
        paddingTop: verticalScale(10),
        paddingHorizontal: wp("5%"),
      },
    },

    boxFlag: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        minWidth: horizontalScale(60),
        minHeight: verticalScale(60),
        marginLeft: wp("2%"),
      },
    },

    textCheckbox: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },

    checkedIcon: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.checkedIconBg,
        borderColor: ThemesApp.getTheme().colors.checkedIconBorder,
      },
    },
  };
};

export default customStyles;
