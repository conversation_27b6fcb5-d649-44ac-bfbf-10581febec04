import { IStyleProps } from "src/business/Interfaces/IStyleProps";

import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";

import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.background,
      },
    },
  };
};

export default customStyles;
