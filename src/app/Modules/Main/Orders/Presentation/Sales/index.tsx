import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {VStack} from "native-base";
import React from "react";
import DrawerHeader from "src/app/Components/Header/DrawerHeader";
import DeliverymanSales from "src/app/Modules/Main/Orders/Presentation/Sales/Components/Profile/Deliveryman";
import ShopkeeperSales from "src/app/Modules/Main/Orders/Presentation/Sales/Components/Profile/Shopkeeper";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Sales/styles";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import {EProfile} from "src/business/Models/Profile";

export type Props = NativeStackScreenProps<MainAppStackParamList, "SalesHome">;

const SalesHome: React.FC<Props> = ({navigation, route}) => {
  const styles = customStyles();
  const {selectedProfile} = useGeneralSettings();

  const handleSalesComponent = () => {
    if (selectedProfile === EProfile.deliveryman) return <DeliverymanSales />;
    if (selectedProfile === EProfile.shopkeeper) return <ShopkeeperSales />;
  };

  return (
    <VStack {...styles.container}>
      <DrawerHeader navigation={navigation} route={route} />
      {handleSalesComponent()}
    </VStack>
  );
};

export default SalesHome;
