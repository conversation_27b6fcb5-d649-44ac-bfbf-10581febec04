import {HStack, Pressable, Text, View} from "native-base";
import React, {useState} from "react";
import CalendarRN from "src/app/Components/Calendar";
import ModalSimple from "src/app/Components/ModalSimple";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/DateFilter/styles";
import {formatDate} from "src/app/Utils/FormatDate";
import {formatDateToDB} from "src/app/Utils/FormatDateToDB";

import {ArrowDropdown, Calendar, Filter} from "src/assets/Icons/Flaticon";

interface ISalesDateFilter {
  date: {from: Date; to: Date};
  setDate: ({from, to}: {from: Date; to: Date}) => void;
}

const SalesDateFilter = ({date, setDate}: ISalesDateFilter) => {
  const styles = customStyles();
  const [openCalendar, setOpenCalendar] = useState({from: false, to: false});
  const {sales: resources} = useTranslation();

  return (
    <View {...styles.box}>
      <HStack {...styles.filterByDateStack} space={2}>
        <Filter {...styles.filterByDateIcon.style} />
        <Text {...styles.filterByDateText}>{resources.labels.dateFilter}</Text>
      </HStack>
      <HStack {...styles.selectedDateStack}>
        <Pressable
          _pressed={{opacity: 0.8}}
          style={{...styles.dateButton.style}}
          onPress={() => setOpenCalendar(() => ({from: true, to: false}))}>
          <HStack {...styles.dateHStack} space={2}>
            <Calendar {...styles.dateIcon.style} />
            {date.from ? (
              <Text {...styles.dateText}>{formatDate(date.from)}</Text>
            ) : null}
            <ArrowDropdown {...styles.dateIcon.style} />
          </HStack>
        </Pressable>
        <Text {...styles.dateTextTo}>-</Text>

        <Pressable
          _pressed={{opacity: 0.8}}
          {...styles.dateButton}
          onPress={() => setOpenCalendar(() => ({from: false, to: true}))}>
          <HStack {...styles.dateHStack} space={2}>
            <Calendar {...styles.dateIcon.style} />
            {date.to ? (
              <Text {...styles.dateText}>{formatDate(date.to)}</Text>
            ) : null}
            <ArrowDropdown {...styles.dateIcon.style} />
          </HStack>
        </Pressable>
      </HStack>
      <ModalSimple
        titleHeader={resources.labels.calendar}
        visibility={openCalendar.from || openCalendar.to}
        onClose={() => setOpenCalendar({from: false, to: false})}>
        <CalendarRN
          setDate={newDate => {
            if (openCalendar.from) {
              setDate({...date, from: new Date(newDate)});
            }
            if (openCalendar.to) {
              setDate({...date, to: new Date(newDate)});
            }
            setOpenCalendar({from: false, to: false});
          }}
          selectedDate={
            openCalendar.from
              ? formatDateToDB(date.from)
              : formatDateToDB(date.to)
          }
          startInCurrentDay={false}
        />
      </ModalSimple>
    </View>
  );
};

export default SalesDateFilter;
