import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {Divider, HStack, Pressable, Text, VStack} from "native-base";
import React, {memo, PropsWithChildren} from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/Card/styles";
import getDateByLocale from "src/app/Utils/GetDateByLocale";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import {
  CancelCircle,
  CheckCircle,
  Motorcycle,
  Sale,
} from "src/assets/Icons/Flaticon";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {IBaseSales} from "src/business/Models/List/Sales/IBaseSale";

interface ISalesCard {
  sale: IBaseSales;
  status?: EOrderStatusValue;
}

const SalesCardRender = ({
  sale,
  status,
  children,
}: PropsWithChildren<ISalesCard>) => {
  const styles = customStyles();
  const {sales: resources} = useTranslation();

  return (
    <Pressable
      _pressed={{opacity: 0.5}}
      onPress={() =>
        rootNavigation("MainApp", {
          screen: "OrderStatusDetails",
          params: {orderId: sale.id},
        })
      }>
      {({isPressed}) => {
        return (
          <HStack
            style={
              isPressed
                ? {...styles.pressedMainHStack.style}
                : {...styles.mainHStack.style}
            }>
            <VStack {...styles.mainVStack}>
              <HStack {...styles.hStackOrder}>
                <Text {...styles.storeName}>{sale.store?.toUpperCase()}</Text>
                <VStack {...styles.stackAlignEnd}>
                  <HStack space={1}>
                    <Text
                      ellipsizeMode="tail"
                      numberOfLines={2}
                      {...styles.productText}>
                      {setResourceParameters(
                        resources.placeholder.order,
                        sale.orderCode,
                      )}
                    </Text>
                    {status === EOrderStatusValue.canceled ||
                    status === EOrderStatusValue.rejected ? (
                      <CancelCircle {...styles.cancelIcon.style} />
                    ) : (
                      <CheckCircle {...styles.checkIcon.style} />
                    )}
                  </HStack>
                  <Text {...styles.dateText}>
                    {getDateByLocale({date: sale.createdAt})}
                  </Text>
                </VStack>
              </HStack>
              <Divider />
              <VStack space={1} marginTop={2}>
                {children}

                <HStack {...styles.hStackInfo}>
                  <HStack space={1}>
                    <Sale {...styles.icons.style} />
                    <Text {...styles.text}>{resources.placeholder.price}</Text>
                  </HStack>
                  <Text {...styles.text}>
                    {
                      maskField({
                        value: sale.price,
                        mask: BRL_CURRENCY_DECIMAL(10),
                      }).masked
                    }
                  </Text>
                </HStack>

                <HStack {...styles.hStackInfo}>
                  <HStack space={1}>
                    <Motorcycle {...styles.icons.style} />
                    <Text {...styles.text}>
                      {resources.placeholder.shippingPrice}
                    </Text>
                  </HStack>
                  <Text {...styles.text}>
                    {
                      maskField({
                        value: sale.shippingPrice,
                        mask: BRL_CURRENCY_DECIMAL(10),
                      }).masked
                    }
                  </Text>
                </HStack>

                <HStack space={1} {...styles.stackEnd}>
                  <Text {...styles.productText}>
                    {resources.placeholder.totalPrice}
                  </Text>
                  <Text {...styles.productText}>
                    {
                      maskField({
                        value: sale.totalPrice,
                        mask: BRL_CURRENCY_DECIMAL(10),
                      }).masked
                    }
                  </Text>
                </HStack>
              </VStack>
            </VStack>
          </HStack>
        );
      }}
    </Pressable>
  );
};

const SalesCard = memo(SalesCardRender, (prevProps, nextProps) => {
  return (
    prevProps.sale.id === nextProps.sale.id &&
    prevProps.status === nextProps.status
  );
});

export default SalesCard;
