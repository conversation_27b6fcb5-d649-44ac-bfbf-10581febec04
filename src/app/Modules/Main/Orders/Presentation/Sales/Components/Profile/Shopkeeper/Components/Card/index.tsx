import { HStack, Text } from "native-base";
import React from "react";
import useTranslation from "src/app/Hooks/useTranslation";

import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import { IShopkeeperSales } from "src/business/Models/List/Sales/IShopkeeperSales";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Sales/Components/Profile/Shopkeeper/Components/Card/styles";

interface ISalesCard {
  sale: IShopkeeperSales;
}

const ShopkeeperSalesCard = ({ sale }: ISalesCard) => {
  const styles = customStyles();
  const {
    orders: { status: orderResources, label },
  } = useTranslation();

  return (
    <HStack {...styles.statusStack} space={1}>
      <Text {...styles.title}>{label.status_steps}:</Text>
      <Text {...(sale.orderStatus === EOrderStatusValue.delivered ? styles.deliveryText : styles.cancelText)}>
        {orderResources[sale.orderStatus]}
      </Text>
    </HStack>
  );
};

export default ShopkeeperSalesCard;
