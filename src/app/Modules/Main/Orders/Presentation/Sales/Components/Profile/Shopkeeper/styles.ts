import { IStyleProps } from "src/business/Interfaces/IStyleProps";

import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";

import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        height: hp("100%"),
        backgroundColor: ThemesApp.getTheme().colors.background,
      },
    },
    contentContainerFlatlist: {
      style: {
        paddingBottom: hp("10%"),
        flexGrow: 1,
      },
    },
    flatList: {
      style: {
        flex: 0.78,
        paddingHorizontal: hp("2%"),
      },
    },
    loadingContainer: {
      justifyContent: "center",
      alignItems: "center",
      height: "100%",
    },
    statusText: {
      style: {
        color: ThemesApp.getTheme().colors.white,
        textAlign: "center",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    boxFilter: {
      style: {
        paddingHorizontal: hp("1%"),
      },
    },
  };
};

export default customStyles;
