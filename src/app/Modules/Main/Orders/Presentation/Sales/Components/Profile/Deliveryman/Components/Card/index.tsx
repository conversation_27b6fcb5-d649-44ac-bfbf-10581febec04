import { <PERSON><PERSON>_K<PERSON>, maskField } from "input-mask-native-base-rhf";
import { HStack, Text, VStack } from "native-base";
import React from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Sales/Components/Profile/Deliveryman/Components/Card/styles";
import { MapMarkerClient, MapMarkerDeliveryman } from "src/assets/Icons/Flaticon";

import { IDeliverymanSales } from "src/business/Models/List/Sales/IDeliverymanSales";

interface ISalesCard {
  sale: IDeliverymanSales;
}

const DeliverymanSalesCard = ({ sale }: ISalesCard) => {
  const styles = customStyles();
  const { sales: resources } = useTranslation();

  return (
    <VStack>
      {sale.address ? (
        <HStack {...styles.vStackAddress}>
          <HStack space={1}>
            <MapMarkerClient {...styles.icons.style} />
            <Text {...styles.text}>{resources.placeholder.address}</Text>
          </HStack>
          <VStack {...styles.hStackAddress}>
            <Text ellipsizeMode="tail" numberOfLines={1} {...styles.text}>{`${sale.address.street}, ${sale.address.number} - ${sale.address.district}`}</Text>
          </VStack>
        </HStack>
      ) : null}

      {sale.distance > 0 ? (
        <HStack {...styles.hStackInfo}>
          <HStack space={1}>
            <MapMarkerDeliveryman {...styles.icons.style} />
            <Text {...styles.text}>{resources.placeholder.routeLength}</Text>
          </HStack>
          <Text {...styles.text}>
            {`${maskField({
              value: sale.distance,
              mask: BRL_KM(10),
            }).masked} Km`}
          </Text>
        </HStack>
      ) : null}
    </VStack>
  );
};

export default DeliverymanSalesCard;
