import {IStyleProps} from "src/business/Interfaces/IStyleProps";

import {moderateScale} from "src/app/Utils/Metrics";

import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    mainHStack: {
      style: {
        paddingHorizontal: hp("1%"),
        marginBottom: hp("4%"),
        alignItems: "flex-start",
        justifyContent: "space-between",
      },
    },
    title: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: "#000000",
      },
    },
    text: {
      style: {
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        textAlign: "center",
        fontWeight: "bold",
        color: "#000000",
      },
    },
    hStack: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: ThemesApp.getTheme().colors.blueGray[100],
        paddingHorizontal: hp("2%"),
        paddingVertical: hp("1%"),
        margin: RFValue(5),
        borderRadius: RFValue(5),
        elevation: 4,
      },
    },
    icons: {
      style: {
        fill: ThemesApp.getTheme().colors.primary[600],
        width: moderateScale(20),
        height: moderateScale(20),
      },
    },
    vStack: {
      style: {
        justifyContent: "center",
        alignItems: "center",
      },
    },
    titleBox: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        paddingHorizontal: hp("2%"),
        marginTop: hp("1%"),
        marginBottom: 0,
        fontWeight: "bold",
      },
    },
    divider: {
      style: {
        width: wp("94%"),
        alignSelf: "center",
        backgroundColor: ThemesApp.getTheme().colors.primary[600],
      },
    },
    contentContainer: {
      style: {
        paddingBottom: 5,
      },
    },
  };
};

export default customStyles;
