import {IStyleProps} from "src/business/Interfaces/IStyleProps";

import {moderateScale} from "src/app/Utils/Metrics";

import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    text: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    hStackInfo: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    icons: {
      style: {
        fill: ThemesApp.getTheme().colors.primary[600],
        width: moderateScale(15),
        height: moderateScale(15),
      },
    },
    hStackAddress: {
      style: {
        width: wp("61%"),
        justifyContent: "flex-end",
        alignItems: "flex-end",
      },
    },
    vStackAddress: {
      style: {
        justifyContent: "space-between",
        alignItems: "flex-start",
      },
    },
  };
};

export default customStyles;
