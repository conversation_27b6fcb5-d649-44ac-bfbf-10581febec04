import {IStyleProps} from "src/business/Interfaces/IStyleProps";

import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";

import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    dateButton: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.primary[100],
        padding: hp("1%"),
        borderRadius: RFValue(5),
        width: wp("42%"),
        justifyContent: "space-between",
      },
    },
    dateIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.primary[600],
        width: moderateScale(14),
        height: moderateScale(14),
      },
    },
    dateText: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
        color: "#000000",
        marginHorizontal: wp("3%"),
      },
    },
    dateHStack: {
      style: {
        justifyContent: "center",
        alignItems: "center",
      },
    },
    selectedDateStack: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    dateTextTo: {
      style: {
        fontSize: RFValue(20),
        lineHeight: RFValue(22),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
        textAlign: "center",
      },
    },
    filterByDateIcon: {
      style: {
        width: moderateScale(13),
        height: moderateScale(13),
        fill: ThemesApp.getTheme().colors.textColor,
      },
    },
    filterByDateText: {
      style: {
        fontSize: RFValue(11),
        lineHeight: RFValue(13),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    filterByDateStack: {
      style: {
        marginBottom: hp("1%"),
      },
    },
    box: {
      style: {
        padding: hp("2%"),
      },
    },
  };
};

export default customStyles;
