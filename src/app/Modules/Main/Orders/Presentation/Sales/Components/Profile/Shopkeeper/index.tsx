/* eslint-disable react-hooks/exhaustive-deps */
import {useFocusEffect} from "@react-navigation/native";
import {isBefore, isSameDay, set} from "date-fns";
import {Box, FlatList, Spinner, VStack} from "native-base";
import React, {useCallback, useRef, useState} from "react";
import ItemNotFound from "src/app/Components/ItemNotFound";
import showToastError from "src/app/Components/Toast/toastError";
import useFlatInfiniteQueryData from "src/app/Hooks/useFlatInfiniteQueryData";
import useTranslation from "src/app/Hooks/useTranslation";
import OrderStatusFilter from "src/app/Modules/Main/Orders/Presentation/Home/Components/StatusFilter";
import SalesCard from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/Card";
import SalesDateFilter from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/DateFilter";
import SalesTotalizer from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/Totalizers";
import ShopkeeperSalesCard from "src/app/Modules/Main/Orders/Presentation/Sales/Components/Profile/Shopkeeper/Components/Card";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Sales/Components/Profile/Shopkeeper/styles";
import useGetShopkeeperSalesPaginated from "src/app/Modules/Main/Orders/Query/useGetShopkeeperSalesPaginated";
import useUser from "src/app/Zustand/Store/useUser";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {IShopkeeperSales} from "src/business/Models/List/Sales/IShopkeeperSales";
import {EProfile} from "src/business/Models/Profile";

const ShopkeeperSales = () => {
  const styles = customStyles();
  const [filter, setFilter] = useState<{
    from: Date;
    to: Date;
    status: EOrderStatusValue | undefined;
  }>({
    from: set(new Date(), {hours: 0, minutes: 0, seconds: 0}),
    to: new Date(),
    status: undefined,
  });

  const renderItem = useCallback(({item}: {item: IShopkeeperSales}) => {
    return (
      <SalesCard sale={item} status={item.orderStatus} key={item.id}>
        <ShopkeeperSalesCard sale={item} />
      </SalesCard>
    );
  }, []);

  const {
    orders: {label: resources},
    generic: {errors: errorResources},
  } = useTranslation();

  const {user} = useUser();
  const currentPage = useRef(1);

  const shopkeeperSalesQuery = useGetShopkeeperSalesPaginated({
    userId: user!.id,
    dateFilter: {from: filter.from, to: filter.to},
    profile: EProfile.shopkeeper,
    statusValue: filter.status,
  });

  const totalizer = shopkeeperSalesQuery.data?.pages[0]?.totalizer;

  const shopkeeperSalesArray = useFlatInfiniteQueryData(shopkeeperSalesQuery);

  const {isLoading, isFetching, isRefetching} = shopkeeperSalesQuery;

  const handlePaginate = () => {
    if (shopkeeperSalesQuery.hasNextPage) {
      shopkeeperSalesQuery.fetchNextPage();
    }
  };

  useFocusEffect(
    useCallback(() => {
      setFilter(prev => ({...prev, status: undefined}));
    }, []),
  );

  useFocusEffect(
    useCallback(() => {
      shopkeeperSalesQuery.refetch();
    }, [filter]),
  );

  const handleDateFilter = async ({from, to}: {from: Date; to: Date}) => {
    currentPage.current = 1;
    if (isBefore(to, from) && !isSameDay(from, to)) {
      showToastError(errorResources.invalidDate, {placement: "top"});
    } else {
      setFilter(prev => ({...prev, from, to}));
    }
  };

  const handleStatusChange = async (statusValue?: EOrderStatusValue) => {
    if (statusValue === filter.status) return;

    setFilter(prev => ({...prev, status: statusValue}));
  };

  return !isLoading ? (
    <VStack {...styles.container}>
      <SalesDateFilter
        date={{from: filter.from, to: filter.to}}
        setDate={handleDateFilter}
      />
      <Box {...styles.boxFilter}>
        <OrderStatusFilter
          isSales
          selectedStatus={filter.status}
          onStatusChange={handleStatusChange}
        />
      </Box>
      <Box {...styles.flatList}>
        <FlatList
          data={shopkeeperSalesArray}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          onEndReachedThreshold={0.3}
          onEndReached={handlePaginate}
          refreshing={isRefetching}
          onRefresh={() => shopkeeperSalesQuery.refetch()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={
            shopkeeperSalesArray?.length === 0
              ? undefined
              : {...styles.contentContainerFlatlist.style}
          }
          ListEmptyComponent={
            shopkeeperSalesArray?.length === 0 && !isLoading ? (
              isFetching ? (
                <Box mt={5}>
                  <Spinner size="sm" />
                </Box>
              ) : (
                <ItemNotFound
                  flex={0}
                  title={resources.empty}
                  directionComponent="column"
                />
              )
            ) : null
          }
        />
      </Box>
      {totalizer && totalizer.length > 0 ? (
        <SalesTotalizer totalizers={totalizer} />
      ) : null}
    </VStack>
  ) : (
    <Box {...styles.loadingContainer}>
      <Spinner size="sm" />
    </Box>
  );
};
export default ShopkeeperSales;
