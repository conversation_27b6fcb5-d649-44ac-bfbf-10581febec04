import { IStyleProps } from "src/business/Interfaces/IStyleProps";

import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    cancelText: {
      style: {
        color: ThemesApp.getTheme().colors.red[600],
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    deliveryText: {
      style: {
        color: ThemesApp.getTheme().colors.green[600],
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    title: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    statusStack: {
      style: {
        justifyContent: "flex-end",
        alignItems: "flex-end",
      },
    },
  };
};

export default customStyles;
