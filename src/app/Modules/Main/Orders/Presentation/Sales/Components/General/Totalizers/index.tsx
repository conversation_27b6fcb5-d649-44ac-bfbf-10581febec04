import {
  BRL_CURRENCY_DECIMAL,
  BRL_KM,
  maskField,
  USA_KM,
} from "input-mask-native-base-rhf";
import {Divider, FlatList, HStack, Text, VStack} from "native-base";
import React from "react";
import useGetLanguage from "src/app/Hooks/useGetLanguage";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/Totalizers/styles";
import {MapMarkerDeliveryman, Receipt, Sale} from "src/assets/Icons/Flaticon";
import {Totalizer} from "src/business/DTOs/Totalizer";
import ESalesTotalizer from "src/business/Enums/ESalesTotalizer";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";

interface ISalesTotalizer {
  totalizers: Totalizer[];
}

const SalesTotalizer = ({totalizers}: ISalesTotalizer) => {
  const styles = customStyles();
  const {sales: resources} = useTranslation();
  const locale = useGetLanguage();

  const renderItem = ({item}: {item: Totalizer}) => {
    return (
      <HStack space={2} key={item.key} {...styles.hStack}>
        {item.type === ESalesTotalizer.currency ? (
          <Sale {...styles.icons.style} />
        ) : item.type === ESalesTotalizer.distance ? (
          <MapMarkerDeliveryman {...styles.icons.style} />
        ) : (
          <Receipt {...styles.icons.style} />
        )}
        <VStack space={1} {...styles.vStack}>
          <Text {...styles.text}>
            {
              resources.totalizers[
                item.key as keyof typeof resources.totalizers
              ]
            }
          </Text>
          <Text {...styles.title}>
            {item.type === ESalesTotalizer.currency
              ? maskField({
                  value: item.value.toFixed(2),
                  mask: BRL_CURRENCY_DECIMAL(10),
                }).masked
              : null}
            {item.type === ESalesTotalizer.distance
              ? `${
                  maskField({
                    value: item.value,
                    mask:
                      locale === LanguageOptions.PT ? BRL_KM(10) : USA_KM(10),
                  }).masked
                } Km`
              : null}
            {item.type === ESalesTotalizer.quantity ? item.value : null}
          </Text>
        </VStack>
      </HStack>
    );
  };

  return (
    <>
      <Divider thickness={1.2} {...styles.divider} />
      <Text {...styles.titleBox}>Total</Text>
      <VStack {...styles.mainHStack} space={4}>
        <FlatList
          data={totalizers}
          renderItem={renderItem}
          keyExtractor={item => item.key}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer.style}
          horizontal
        />
      </VStack>
    </>
  );
};

export default SalesTotalizer;
