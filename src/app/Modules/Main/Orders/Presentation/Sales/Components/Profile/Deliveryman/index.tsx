/* eslint-disable react-hooks/exhaustive-deps */
import {useFocusEffect} from "@react-navigation/native";
import {isBefore, isSameDay, set} from "date-fns";
import {Box, FlatList, Spinner, VStack} from "native-base";
import React, {useCallback, useRef, useState} from "react";
import ItemNotFound from "src/app/Components/ItemNotFound";
import showToastError from "src/app/Components/Toast/toastError";
import useFlatInfiniteQueryData from "src/app/Hooks/useFlatInfiniteQueryData";
import useTranslation from "src/app/Hooks/useTranslation";
import SalesCard from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/Card";
import SalesDateFilter from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/DateFilter";
import SalesTotalizer from "src/app/Modules/Main/Orders/Presentation/Sales/Components/General/Totalizers";
import DeliverymanSalesCard from "src/app/Modules/Main/Orders/Presentation/Sales/Components/Profile/Deliveryman/Components/Card";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Sales/Components/Profile/Deliveryman/styles";
import useGetDeliverymanSalesPaginated from "src/app/Modules/Main/Orders/Query/useGetDeliverymanSalesPaginated";
import useUser from "src/app/Zustand/Store/useUser";
import {IDeliverymanSales} from "src/business/Models/List/Sales/IDeliverymanSales";
import {EProfile} from "src/business/Models/Profile";

const DeliverymanSales = () => {
  const styles = customStyles();
  const [dateFilter, setDateFilter] = useState({
    from: set(new Date(), {hours: 0, minutes: 0, seconds: 0}),
    to: new Date(),
  });

  const {
    orders: {label: resources},
    generic: {errors: errorResources},
  } = useTranslation();

  const renderItem = useCallback(({item}: {item: IDeliverymanSales}) => {
    return (
      <SalesCard sale={item} key={item.id}>
        <DeliverymanSalesCard sale={item} />
      </SalesCard>
    );
  }, []);

  const {user} = useUser();
  const currentPage = useRef(1);

  const deliverymanSalesQuery = useGetDeliverymanSalesPaginated({
    userId: user!.id,
    dateFilter,
    profile: EProfile.deliveryman,
  });

  const totalizer = deliverymanSalesQuery.data?.pages[0]?.totalizer;

  const deliverymanSalesArray = useFlatInfiniteQueryData(deliverymanSalesQuery);

  const {isLoading, isFetching, isRefetching} = deliverymanSalesQuery;

  const handlePaginate = () => {
    if (deliverymanSalesQuery.hasNextPage) {
      deliverymanSalesQuery.fetchNextPage();
    }
  };

  useFocusEffect(
    useCallback(() => {
      deliverymanSalesQuery.refetch();
    }, [dateFilter]),
  );

  const handleDateFilter = async ({from, to}: {from: Date; to: Date}) => {
    currentPage.current = 1;
    if (isBefore(to, from) && !isSameDay(from, to)) {
      showToastError(errorResources.invalidDate, {placement: "top"});
    } else {
      setDateFilter({from, to});
    }
  };

  return !isLoading ? (
    <VStack {...styles.container}>
      <SalesDateFilter date={dateFilter} setDate={handleDateFilter} />
      <Box {...styles.flatList}>
        <FlatList
          data={deliverymanSalesArray}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          onEndReachedThreshold={0.3}
          onEndReached={handlePaginate}
          refreshing={isRefetching}
          onRefresh={() => deliverymanSalesQuery.refetch()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={
            deliverymanSalesArray?.length === 0
              ? undefined
              : {...styles.contentContainerFlatlist.style}
          }
          ListEmptyComponent={
            deliverymanSalesArray?.length === 0 && !isLoading ? (
              isFetching ? (
                <Box mt={5}>
                  <Spinner size="sm" />
                </Box>
              ) : (
                <ItemNotFound
                  flex={0}
                  title={resources.empty}
                  directionComponent="column"
                />
              )
            ) : null
          }
        />
      </Box>
      {totalizer && totalizer.length > 0 ? (
        <SalesTotalizer totalizers={totalizer} />
      ) : null}
    </VStack>
  ) : (
    <Box {...styles.loadingContainer}>
      <Spinner size="sm" />
    </Box>
  );
};
export default DeliverymanSales;
