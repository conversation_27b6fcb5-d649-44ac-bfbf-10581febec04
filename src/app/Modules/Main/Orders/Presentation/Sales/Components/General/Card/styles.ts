import {IStyleProps} from "src/business/Interfaces/IStyleProps";

import {moderateScale} from "src/app/Utils/Metrics";

import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    mainHStack: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        borderColor: ThemesApp.getTheme().colors.muted[200],
        flex: 1,
        borderRadius: moderateScale(5),
        borderWidth: moderateScale(1),
        elevation: 4,
        marginTop: hp("2%"),
        padding: hp("1%"),
        alignItems: "center",
      },
    },
    pressedMainHStack: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.muted[200],
        borderColor: ThemesApp.getTheme().colors.muted[200],
        flex: 1,
        borderRadius: moderateScale(5),
        borderWidth: moderateScale(1),
        marginTop: hp("2%"),
        padding: hp("1%"),
        alignItems: "center",
      },
    },
    mainVStack: {
      style: {
        flex: 1,
        margin: wp("1%"),
      },
    },
    productText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    text: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    statusType: {
      style: {
        fontWeight: "bold",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    hStackInfo: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },

    dateText: {
      style: {
        color: ThemesApp.getTheme().colors.priceDateText,
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        textAlign: "right",
        marginBottom: hp("1%"),
      },
    },
    hStackOrder: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    storeName: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    icons: {
      style: {
        fill: ThemesApp.getTheme().colors.primary[600],
        width: moderateScale(15),
        height: moderateScale(15),
      },
    },
    checkIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.green[600],
        width: moderateScale(12),
        height: moderateScale(12),
      },
    },
    hStackAddress: {
      style: {
        justifyContent: "flex-end",
        alignItems: "flex-end",
      },
    },
    vStackAddress: {
      style: {
        justifyContent: "space-between",
        alignItems: "flex-start",
      },
    },
    stackEnd: {
      style: {
        justifyContent: "flex-end",
      },
    },
    stackAlignEnd: {
      style: {
        alignItems: "flex-end",
      },
    },
    cancelIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.secondary[600],
        width: moderateScale(12),
        height: moderateScale(12),
      },
    },
  };
};

export default customStyles;
