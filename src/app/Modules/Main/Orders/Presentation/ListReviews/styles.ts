import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { moderateScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    spinnerBox: {
      style: {
        marginTop: hp("5%"),
      },
    },
    starIcon: {
      style: {
        width: moderateScale(15),
        height: moderateScale(15),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },
    textInfo: {
      style: {
        color: ThemesApp.getTheme().colors.yellow[500],
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        fontWeight: "bold",
        marginLeft: wp("1%"),
      },
    },
  };
};

export default customStyles;
