import {HStack, Pressable, Text} from "native-base";
import React, {useState} from "react";
import ItemNotFound from "src/app/Components/ItemNotFound";
import ModalSimple from "src/app/Components/ModalSimple";
import ReviewCard from "src/app/Components/Review/Card";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/ListReviews/styles";
import {PeopleCarryBox, Star} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {ReviewList} from "src/business/DTOs/ReviewList";
import {IReviewService} from "src/business/Interfaces/Services/IReview";
import AppError from "src/business/Tools/AppError";

type Props = {
  orderId: string;
  rating?: number;
  deliverymanReview?: boolean;
};

const OrderReviewsList: React.FC<Props> = ({
  orderId,
  rating,
  deliverymanReview,
}) => {
  const styles = customStyles();

  const [review, setReview] = useState<ReviewList>();
  const [modalVisibility, setModalVisibility] = useState(false);

  const {
    orders: {errors: resources},
  } = useTranslation();

  const getData = async () => {
    const reviewService = container.get<IReviewService>(TOKENS.ReviewService);

    const response = await reviewService.getOrdersReviews(
      orderId,
      deliverymanReview,
    );

    if (response instanceof AppError) {
      return;
    }

    setReview(response);
    setModalVisibility(!modalVisibility);
  };

  const changeModalVisibility = () => {
    setModalVisibility(false);
  };

  return (
    <>
      <Pressable onPress={() => getData()} {...styles.starBox}>
        <HStack>
          {deliverymanReview ? (
            <PeopleCarryBox {...styles.starIcon.style} />
          ) : (
            <Star {...styles.starIcon.style} />
          )}
          <Text {...styles.textInfo}>{rating}</Text>
        </HStack>
      </Pressable>

      <ModalSimple visibility={modalVisibility} onClose={changeModalVisibility}>
        {review ? (
          <ReviewCard data={review} />
        ) : (
          <ItemNotFound
            title={resources.load_failed}
            directionComponent="column"
            flex={undefined}
          />
        )}
      </ModalSimple>
    </>
  );
};

export default OrderReviewsList;
