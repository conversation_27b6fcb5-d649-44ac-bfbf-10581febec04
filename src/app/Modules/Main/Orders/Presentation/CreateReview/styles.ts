import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    loadingContainer: {
      justifyContent: "center",
      alignItems: "center",
      flex: 1,
    },
    container: {
      style: {
        flex: 1,
      },
    },

    mainContent: {
      style: {
        width: wp("90%"),
        marginTop: hp("10%"),
        alignSelf: "center",
        justifyContent: "center",
      },
    },

    starRating: {
      style: {
        alignSelf: "center",
        marginTop: hp("5%"),
      },
    },

    inputBox: {
      style: {
        width: wp("90%"),
        alignSelf: "center",
        marginTop: hp("5%"),
      },
    },

    button: {
      style: {
        width: wp("50%"),
        alignSelf: "center",
        marginTop: hp("4%"),
        marginBottom: hp("6%"),
      },
    },

    title: {
      style: {
        fontSize: RFValue(22),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    subtitle: {
      style: {
        color: ThemesApp.getTheme().colors.gray[400],
        fontSize: RFValue(18),
        marginTop: hp("1%"),
      },
    },
    starIcon: {
      size: moderateScale(35),
      style: {
        marginLeft: wp("2%"),
      },
    },
  };
};

export default customStyles;
