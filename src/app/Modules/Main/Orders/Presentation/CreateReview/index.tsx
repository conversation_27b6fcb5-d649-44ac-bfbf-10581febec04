import {ScrollView, VStack} from "native-base";
import React from "react";
import StackHeader from "src/app/Components/Header/StackHeader";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import useTranslation from "src/app/Hooks/useTranslation";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import CreateReview from "src/app/Components/Review/Create";
import setResourceParameters from "src/app/Utils/SetResourceParameters";

type Props = NativeStackScreenProps<MainAppStackParamList, "OrderCreateReview">;

const OrderCreateReview: React.FC<Props> = ({navigation, route}) => {
  const {orderId, storeId} = route.params;

  const {review: resources} = useTranslation();

  return (
    <ScrollView>
      <VStack flex={1}>
        <StackHeader
          title={setResourceParameters(
            resources.header.rating,
            resources.label.order,
          )}
          navigation={navigation}
          route={route}
        />
        <CreateReview
          title={resources.title.order}
          orderId={orderId}
          storeId={storeId}
        />
      </VStack>
    </ScrollView>
  );
};

export default OrderCreateReview;
