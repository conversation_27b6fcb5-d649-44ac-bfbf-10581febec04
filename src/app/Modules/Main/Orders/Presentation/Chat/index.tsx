/* eslint-disable react-hooks/exhaustive-deps */
import {useFocusEffect, useIsFocused} from "@react-navigation/native";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {Box, HStack, Text, VStack} from "native-base";
import React, {useCallback, useEffect, useState} from "react";
import Chat from "src/app/Components/Chat";
import StackHeader from "src/app/Components/Header/StackHeader";
import ModalProgress from "src/app/Components/ModalProgress";
import showToastError from "src/app/Components/Toast/toastError";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Chat/styles";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import {rootGoBack} from "src/app/Utils/RootNavigation";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useOrder from "src/app/Zustand/Store/useOrder";
import useUser from "src/app/Zustand/Store/useUser";
import {Message} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import socket from "src/business/Config/Socket";
import {GiftedChatMessages} from "src/business/DTOs/Chat/GiftedChatMessages";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import EChatEvents from "src/business/Enums/Socket/EChatEvents";
import {IOrderService} from "src/business/Interfaces/Services/IOrder";
import IHandleChat from "src/business/Interfaces/Utils/IHandleChat";
import {EProfile} from "src/business/Models/Profile";
import applicationSingleton from "src/business/Singletons/Application";
import AppError from "src/business/Tools/AppError";

export type Props = NativeStackScreenProps<MainAppStackParamList, "OrderChat">;

const OrderChat: React.FC<Props> = ({navigation, route}) => {
  const styles = customStyles();
  const {orderId, orderCode} = route.params;
  const [messages, setMessages] = useState<GiftedChatMessages[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [typingMessage, setTypingMessage] = useState<
    {message: string; profile: EProfile}[]
  >([]);
  const userId = useUser().user?.id;
  const {
    chat: resources,
    forms: {selectProfile: profileResources},
    routes,
  } = useTranslation();
  const orderService = container.get<IOrderService>(TOKENS.OrderService);
  const handleChat = container.get<IHandleChat>(TOKENS.HandleChat);
  const {currentStatus} = useOrder();
  const canConnectToChat = currentStatus !== EOrderStatusValue.delivered;
  const {selectedProfile} = useGeneralSettings();
  const isFocused = useIsFocused();

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      const response = await orderService.getOrderChatMessages(orderId);
      if (!(response instanceof AppError)) {
        const mappedResponse = handleChat.getGiftedChatMessages(response);
        setMessages(mappedResponse);
      } else {
        if (!canConnectToChat) {
          rootGoBack();
          showToastError(resources.labels.empty_chat, {placement: "top"});
        }
        setMessages([]);
      }
      setIsLoading(false);
    })();
  }, [isFocused]);

  useFocusEffect(
    useCallback(() => {
      if (canConnectToChat) {
        socket.auth = {
          token: `Bearer ${applicationSingleton.authenticationResult?.cognitoAuthenticationResult.AccessToken}`,
        };
        socket.connect();
      }
    }, []),
  );

  useEffect(() => {
    const unsubscribe = navigation.addListener("blur", () => {
      console.log("chat disconnected");
      socket.disconnect();
    });
    return unsubscribe;
  }, []);

  useEffect(() => {
    if (canConnectToChat) {
      socket.emit(EChatEvents.enter_chat, orderId, userId);
      socket.on(EChatEvents.get_messages, data => {
        const mappedData = handleChat.getOneGiftedChatMessage(data);
        setMessages(previousMessages => [mappedData].concat(previousMessages));
      });

      socket.on(EChatEvents.start_typing, data => {
        const profileName =
          data.profile as keyof typeof profileResources.profileNames;
        const newTypingMessage = `${profileResources.profileNames[profileName]} ${resources.labels.is_typing}`;

        setTypingMessage(prev => {
          return !prev.find(item => item.profile === data.profile) ||
            prev.length === 0
            ? prev.concat([{message: newTypingMessage, profile: data.profile}])
            : prev;
        });
      });

      socket.on(EChatEvents.stop_typing, () => {
        setTypingMessage(typingMessage.splice(typingMessage.length, 1));
      });

      socket.on(EChatEvents.connect_error, () => {
        rootGoBack();
      });
    }
    return () => {
      socket.off(EChatEvents.get_messages);
    };
  }, [isFocused]);

  const saveChatMessages = (newMessages: GiftedChatMessages[]) => {
    if (selectedProfile) newMessages[0].user.profile = selectedProfile;
    const mappedMessage = handleChat.getOneChatDBMessage(newMessages[0]);
    if (userId) mappedMessage.user.id = userId;
    setMessages(prevMessages => newMessages.concat(prevMessages));
    socket.emit(EChatEvents.send_message, mappedMessage, orderId);
  };

  const handleTypingStatus = (typing: boolean, userProfile: EProfile) => {
    if (typing === true) {
      socket.emit(
        EChatEvents.typing_message,
        orderId,
        userProfile,
        EChatEvents.start_typing,
      );
    }
    if (!typing) {
      socket.emit(
        EChatEvents.typing_message,
        orderId,
        userProfile,
        EChatEvents.stop_typing,
      );
    }
  };

  const getIsTypingMessage = () => {
    if (selectedProfile) {
      const key = Object.keys(profileResources.profileNames).filter(
        item => item !== selectedProfile,
      );
      return setResourceParameters(
        resources.labels.chat_users,
        profileResources.profileNames[
          key[0] as keyof typeof profileResources.profileNames
        ].toLowerCase(),
        profileResources.profileNames[
          key[1] as keyof typeof profileResources.profileNames
        ].toLowerCase(),
      );
    }
  };

  return (
    <Box {...styles.mainBox}>
      <StackHeader
        navigation={navigation}
        route={route}
        title={`${routes.OrderChat} - ${setResourceParameters(
          resources.labels.order_code,
          orderCode,
        )}`}
      />
      {isLoading ? (
        <ModalProgress />
      ) : (
        <>
          {messages.length <= 0 && canConnectToChat ? (
            <VStack space={2} {...styles.vStackBox}>
              <HStack {...styles.chatLabelBox}>
                <Message {...styles.chatLabelIcon.style} />
                <Text {...styles.chatLabelText}>
                  {resources.labels.no_chat_messages}
                </Text>
              </HStack>
              {selectedProfile ? (
                <HStack {...styles.chatLabelBox}>
                  <Message {...styles.chatLabelIcon.style} />
                  <Text {...styles.chatLabelText}>{getIsTypingMessage()}</Text>
                </HStack>
              ) : null}
            </VStack>
          ) : null}
          <Chat
            messages={messages}
            saveMessages={saveChatMessages}
            showInput={currentStatus !== EOrderStatusValue.delivered}
            handleIsTyping={handleTypingStatus}
            isTypingMessage={typingMessage.map(item => item.message)}
          />
        </>
      )}
    </Box>
  );
};

export default OrderChat;
