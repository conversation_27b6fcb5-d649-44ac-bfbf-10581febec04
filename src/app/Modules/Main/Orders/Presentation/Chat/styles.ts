import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale, verticalScale, widthPercentage as wp } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainBox: {
      position: "relative",
      flex: 1,
    },
    chatLabelBox: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.gray[200],
        marginHorizontal: wp("1%"),
        paddingVertical: verticalScale(5),
        borderRadius: moderateScale(8),
        alignItems: "center",
      },
    },
    chatLabelText: {
      style: {
        color: ThemesApp.getTheme().colors.gray[700],
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
        display: "flex",
        flex: 1,
      },
    },
    chatLabelIcon: {
      style: {
        height: moderateScale(20),
        width: moderateScale(20),
        fill: ThemesApp.getTheme().colors.primary[700],
        paddingHorizontal: wp("3%"),
      },
    },
    vStackBox: {
      style: {
        marginVertical: wp("1%"),
        marginHorizontal: wp("1%"),
        paddingHorizontal: wp("1%"),
      },
    },
  };
};

export default customStyles;
