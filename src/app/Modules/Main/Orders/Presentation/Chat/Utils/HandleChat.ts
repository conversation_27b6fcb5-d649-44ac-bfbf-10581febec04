import { injectable } from "inversify";
import mapperChatMessages from "src/business/Config/Automapper/Chat";
import { GiftedChatMessages } from "src/business/DTOs/Chat/GiftedChatMessages";
import IChatUtils from "src/business/Interfaces/Utils/IHandleChat";
import { ChatMessagesList } from "src/business/Models/List/ChatMessages";

@injectable()
class HandleChat implements IChatUtils {
  getGiftedChatMessages(data: ChatMessagesList[]): GiftedChatMessages[] {
    return mapperChatMessages.mapArray<ChatMessagesList, GiftedChatMessages>(
      data,
      "GiftedChatMessages",
      "ChatMessagesList",
    );
  }

  getOneChatDBMessage(data: GiftedChatMessages): ChatMessagesList {
    return mapperChatMessages.map<GiftedChatMessages, ChatMessagesList>(data, "ChatMessagesList", "GiftedChatMessages");
  }

  getOneGiftedChatMessage(data: ChatMessagesList): GiftedChatMessages {
    return mapperChatMessages.map<ChatMessagesList, GiftedChatMessages>(data, "GiftedChatMessages", "ChatMessagesList");
  }
}

export default HandleChat;
