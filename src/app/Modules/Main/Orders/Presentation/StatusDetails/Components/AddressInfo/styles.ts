import { IStyleProps } from "src/business/Interfaces/IStyleProps";

import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    textNickname: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
        flexWrap: "wrap",
      },
    },

    textAddress: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        flexWrap: "wrap",
      },
    },

    textComplement: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        flexWrap: "wrap",
        color: ThemesApp.getTheme().colors.accordionStatusSubtitle,
      },
    },
  };
};

export default customStyles;
