import { VStack, Text, HStack, Divider } from "native-base";
import React from "react";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/DeliveryTimeInfo/styles";
import moment from "moment";
import { Timer } from "src/assets/Icons/Flaticon";
import useTranslation from "src/app/Hooks/useTranslation";

interface Props {
  estimatedDeliveryTime: number
}


const DeliveryTimeInfo = ({ estimatedDeliveryTime }: Props) => {
  const styles = customStyles();
  const { orders: resources } = useTranslation();

  const formatedTime = moment.duration(estimatedDeliveryTime, "minutes");

  return (
    <VStack>
      <HStack {...styles.container}>
        <Text {...styles.statusText}>{resources.label.estimatedDeliveryTime}</Text>
        <HStack {...styles.timerContainer}>
          <Text {...styles.statusSubTitle}>{`${formatedTime.hours()}h ${formatedTime.minutes()}m`}</Text>
          <Timer {...styles.iconTimer.style} />
        </HStack>
      </HStack>
      <Divider
      />
    </VStack>
  );
};

export default DeliveryTimeInfo;
