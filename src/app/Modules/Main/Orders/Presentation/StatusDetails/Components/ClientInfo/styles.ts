import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    textInfo: {
      style: {
        color: ThemesApp.getTheme().colors.yellow[500],
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    container: {
      style: {
        marginVertical: 10,
        justifyContent: "space-between",
      },
    },
    subText: {
      color: ThemesApp.getTheme().colors.accordionStatusSubtitle,
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },
  };
};

export default customStyles;
