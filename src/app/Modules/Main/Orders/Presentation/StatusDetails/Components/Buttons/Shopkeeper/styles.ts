import { IStyleProps } from "src/business/Interfaces/IStyleProps";

import { moderateScale } from "src/app/Utils/Metrics";

import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    statusHStack: {
      style: {
        alignItems: "center",
      },
    },
    statusText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        marginLeft: wp("2%"),
        fontWeight: "bold",
        fontSize: moderateScale(14),
        lineHeight: moderateScale(16),
      },
    },
    statusRequired: {
      color: ThemesApp.getTheme().colors.secondary[500],
      style: {
        paddingVertical: hp("1%"),
        fontWeight: "bold",
        fontSize: moderateScale(14),
        lineHeight: moderateScale(16),
      },
    },
    radioText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        marginHorizontal: wp("1%"),
        fontSize: moderateScale(14),
        lineHeight: moderateScale(16),
      },
    },
    radioIcon: {
      size: moderateScale(18),
    },
    textArea: {
      style: {
        marginTop: moderateScale(2),
        alignItems: "flex-end",
      },
    },
    textAreaInput: {
      style: {
        fontSize: moderateScale(14),
        lineHeight: moderateScale(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    boxFooter: {
      style: {
        paddingHorizontal: wp("2%"),
        paddingBottom: wp("3%"),
        backgroundColor: ThemesApp.getTheme().colors.background,
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 99,
      },
    },
    vStackFooter: {
      style: {
        marginBottom: wp("3%"),
        justifyContent: "center",
      },
    },
    deliveryIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginRight: wp("2%"),
        fill: ThemesApp.getTheme().colors.white,
      },
    },
    deliveryText: {
      color: ThemesApp.getTheme().colors.white,
      fontSize: moderateScale(14),
      lineHeight: moderateScale(16),
    },

    menuContainer: {
      style: {
        marginVertical: hp("1%"),
        marginHorizontal: wp("1%"),
      },
    },
  };
};

export default customStyles;
