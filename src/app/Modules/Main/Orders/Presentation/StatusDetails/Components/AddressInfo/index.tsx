import {Text, VStack} from "native-base";
import React, {useState} from "react";
import Accordion from "src/app/Components/Accordion";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/AddressInfo/styles";
import {Address} from "src/business/Models/Address/Address";

interface IProps {
  address: Address;
}

const AddressInfo = ({address}: IProps) => {
  const styles = customStyles();
  const [openAddress, setOpenAddress] = useState(false);
  const {orders: resources} = useTranslation();
  return (
    <Accordion
      title={resources.label.deliveryAddress}
      setExpand={() => setOpenAddress(!openAddress)}
      isExpanded={openAddress}
      controlExpand={false}>
      <VStack>
        <Text {...styles.textNickname}>{address.nickname}</Text>
        <Text {...styles.textAddress}>{`${address.street}, ${
          address.number ? address.number : ""
        }`}</Text>
        <Text
          {...styles.textAddress}>{`${address.district} - ${address.city}, ${address.state}`}</Text>
        <Text {...styles.textComplement}>{address.complement}</Text>
      </VStack>
    </Accordion>
  );
};

export default AddressInfo;
