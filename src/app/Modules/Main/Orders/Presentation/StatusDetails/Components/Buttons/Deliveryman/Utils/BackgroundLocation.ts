/* eslint-disable no-async-promise-executor */
/* eslint-disable no-promise-executor-return */
/* eslint-disable no-await-in-loop */
import {injectable} from "inversify";
import BackgroundService from "react-native-background-actions";
import {check, PERMISSIONS, RESULTS} from "react-native-permissions";
import showToastModal from "src/app/Components/Toast/toastModal";
import {Resources} from "src/app/Context/Utils/Resources";
import backgroundOptions from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/Buttons/Deliveryman/Utils/BackgroundOption";
import getCurrentLocation from "src/app/Utils/GetCurrentLocation";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import IBackgroundTaskParams from "src/business/DTOs/BackgroundTaskParams";
import {Location} from "src/business/DTOs/Location";
import EGeolocationModel from "src/business/Enums/Models/EGeolocationModel";
import LanguageOptions from "src/business/Enums/Models/ELanguageOptions";
import {IDeliverymanService} from "src/business/Interfaces/Services/IDeliveryman";
import {ITrackingOrderService} from "src/business/Interfaces/Services/ITrackingOrder";
import IBackgroundLocation from "src/business/Interfaces/Utils/IBackgroundLocation";
import applicationSingleton from "src/business/Singletons/Application";

@injectable()
class BackgroundLocation implements IBackgroundLocation {
  private options;

  constructor() {
    this.options = backgroundOptions();
  }

  async startTrackingBackgroundLocation(orderId: string): Promise<void> {
    this.options.parameters.orderId = orderId;
    this.options.parameters.model = EGeolocationModel.trackingOrder;
    await BackgroundService.start(
      this.backgroundLocationIntensiveTask,
      this.options,
    );
  }

  async startTrackingDeliverymanBackgroundLocation(): Promise<void> {
    this.options.parameters.model = EGeolocationModel.deliveryman;
    const resources = Resources.get().orders;
    check(PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION).then(result => {
      if (result !== RESULTS.GRANTED) {
        showToastModal(
          resources.label.background_location_task_name,
          resources.text.deliverymanLocation,
        );
      }
    });
    await BackgroundService.start(
      this.backgroundLocationIntensiveTask,
      this.options,
    );
  }

  async stopTrackingBackgroundLocation(): Promise<void> {
    console.log("background action stopped");
    await BackgroundService.stop();
  }

  async backgroundLocationIntensiveTask(
    taskDataArguments?: IBackgroundTaskParams | undefined,
  ): Promise<void> {
    const timeoutInterval = (time: number) =>
      new Promise(resolve => setTimeout(resolve, time));

    const delay = taskDataArguments?.delay;
    if (delay) {
      await new Promise(async () => {
        while (BackgroundService?.isRunning()) {
          BackgroundLocation.saveGeoLocation(
            taskDataArguments.model,
            taskDataArguments.orderId,
          );
          await timeoutInterval(delay);
        }
      });
    }
  }

  static saveTrackingOrder = (info: Location, orderId: string) => {
    if (orderId !== "") {
      const trackingOrderService = container.get<ITrackingOrderService>(
        TOKENS.TrackingOrderService,
      );

      trackingOrderService.createTrackingOrder({
        latitude: info.latitude,
        longitude: info.longitude,
        orderId,
      });
    }
  };

  static updateDeliverymanLocation = (info: Location) => {
    const deliverymanService = container.get<IDeliverymanService>(
      TOKENS.DeliverymanService,
    );
    const userId = applicationSingleton.authenticationResult?.userInfo.id;
    if (userId) {
      deliverymanService.updateDeliverymanLocation(info, userId);
    }
  };

  static saveGeoLocation = (model: string, orderId: string) => {
    const timeoutId = setTimeout(() => {
      console.log("trying to retrieve geo-location, but timed out");
    }, 5000);

    getCurrentLocation({
      languageApp: LanguageOptions.PT,
      onLoadLocation: position => {
        clearTimeout(timeoutId);
        if (position.currentLocation) {
          const location = {
            latitude: position.currentLocation?.latitude,
            longitude: position.currentLocation?.longitude,
          };
          BackgroundLocation.saveTrackingOrder(location, orderId);
          BackgroundLocation.updateDeliverymanLocation(location);
        }
      },
      onError: () => {
        clearTimeout(timeoutId);
        BackgroundService.stop();
        console.log("Background action -> GetCurrentLocation error");
      },
    });
  };
}

export default BackgroundLocation;
