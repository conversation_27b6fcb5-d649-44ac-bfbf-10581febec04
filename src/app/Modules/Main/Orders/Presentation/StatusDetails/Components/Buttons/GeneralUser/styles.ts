import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxFooter: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.background,
        paddingHorizontal: wp("2%"),
        paddingBottom: wp("3%"),
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 99,
      },
    },
    lockIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginRight: wp("2%"),
        fill: ThemesApp.getTheme().colors.white,
      },
    },
    lockText: {
      color: ThemesApp.getTheme().colors.white,
      fontSize: moderateScale(14),
      lineHeight: moderateScale(16),
    },

    buttonCancel: {
      backgroundColor: ThemesApp.getTheme().colors.error,
    },

    buttonGroup: {
      style: {
        justifyContent: "flex-end",
        width: wp("100%"),
      },
    },

    buttonNo: {
      style: {
        width: wp("30%"),
      },
    },

    buttonYes: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.error,
      },
    },
  };
};

export default customStyles;
