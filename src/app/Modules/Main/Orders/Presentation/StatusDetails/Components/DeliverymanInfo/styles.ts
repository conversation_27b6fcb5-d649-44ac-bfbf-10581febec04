import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { horizontalScale, moderateScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { RFValue } from "react-native-responsive-fontsize";

const customStyles = (): IStyleProps => {
  return {
    starIcon: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.yellow[500],
        marginRight: horizontalScale(4),
      },
    },
    textInfo: {
      style: {
        color: ThemesApp.getTheme().colors.yellow[500],
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    container: {
      style: {
        marginVertical: 10,
        justifyContent: "space-between",
      },
    },
    subText: {
      color: ThemesApp.getTheme().colors.accordionStatusSubtitle,
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
      },
    },
  };
};

export default customStyles;
