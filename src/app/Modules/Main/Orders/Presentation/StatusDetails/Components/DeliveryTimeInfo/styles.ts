import { IStyleProps } from "src/business/Interfaces/IStyleProps";

import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        marginTop: verticalScale(10),
        marginBottom: verticalScale(10),
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    timerContainer: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    statusText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.accordionStatusText,
      },
    },
    statusSubTitle: {
      style: {
        color: ThemesApp.getTheme().colors.accordionStatusSubtitle,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    iconTimer: {
      style: {
        fill: ThemesApp.getTheme().colors.iconUpload,
        width: moderateScale(16),
        height: moderateScale(16),
        marginLeft: horizontalScale(5),
      },
    },
  };
};

export default customStyles;
