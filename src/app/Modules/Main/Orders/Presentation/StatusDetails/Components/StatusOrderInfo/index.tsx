import React, {useState} from "react";
import Accordion from "src/app/Components/Accordion";
import StatusOrder from "src/app/Components/StatusOrder";
import useTranslation from "src/app/Hooks/useTranslation";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {OrderStatusList} from "src/business/Models/List/OrderStatus";

interface IProps {
  orderId: string;
  currentStatus: EOrderStatusValue;
  createdAt: Date;
  orderStatusLabels: OrderStatusList[];
  userProfile: string;
}

const StatusOrderInfo = ({
  orderId,
  currentStatus,
  createdAt,
  orderStatusLabels,
  userProfile,
}: IProps) => {
  const [openStatus, setOpenStatus] = useState(true);
  const {orders: resources} = useTranslation();
  return (
    <Accordion
      title={resources.label.status_steps}
      setExpand={() => setOpenStatus(!openStatus)}
      isExpanded={openStatus}
      controlExpand={false}>
      <StatusOrder
        userType={userProfile}
        orderId={orderId}
        currentStatus={currentStatus}
        createdAt={createdAt}
        status={orderStatusLabels}
        detailedStatus={openStatus}
      />
    </Accordion>
  );
};

export default StatusOrderInfo;
