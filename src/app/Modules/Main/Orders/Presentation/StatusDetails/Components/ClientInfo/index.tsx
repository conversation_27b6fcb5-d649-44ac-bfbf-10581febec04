import React, {useState} from "react";
import {HStack, Text} from "native-base";
import useTranslation from "src/app/Hooks/useTranslation";
import Accordion from "src/app/Components/Accordion";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/ClientInfo/styles";

interface Props {
  name: string;
}

const ClientInfo = ({name}: Props) => {
  const styles = customStyles();
  const [openItems, setOpenItems] = useState(false);
  const {
    forms: {selectProfile: resources},
  } = useTranslation();

  return (
    <Accordion
      title={resources.profileNames.client}
      setExpand={() => setOpenItems(!openItems)}
      isExpanded={openItems}
      controlExpand={false}>
      <HStack {...styles.container}>
        <Text {...styles.subText}>{name}</Text>
      </HStack>
    </Accordion>
  );
};

export default ClientInfo;
