import { Resources } from "src/app/Context/Utils/Resources";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import configAppSingleton from "src/business/Singletons/ConfigApp";

const backgroundOptions = () => {
  const resources = Resources.get();

  return {
    taskName: resources.orders.label.background_location_task_name,
    taskTitle: resources.orders.label.background_location_task_title,
    taskDesc: resources.orders.placeholder.background_location_description,
    color: ThemesApp.getTheme().colors.primary[600],
    taskIcon: {
      name: "ic_launcher",
      type: "mipmap",
    },
    parameters: {
      delay: configAppSingleton.minTimeToGetLocation,
      orderId: "",
      model: "",
    },
  };
};

export default backgroundOptions;
