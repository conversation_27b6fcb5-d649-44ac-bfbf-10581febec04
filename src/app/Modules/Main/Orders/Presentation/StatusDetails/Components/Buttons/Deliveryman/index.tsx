import {Box, Button, HStack, Text} from "native-base";
import React, {useState} from "react";
import {PERMISSIONS} from "react-native-permissions";
import ModalRN from "src/app/Components/Modal";
import PopupMap from "src/app/Components/PopupMap";
import showToastError from "src/app/Components/Toast/toastError";
import {useLocation} from "src/app/Components/TrackingOrder/Hooks/useLocation";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/Buttons/Deliveryman/styles";
import ValidateCustomerCode from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/ValidateCustomerCode";
import {handleOneAppPermission} from "src/app/Utils/Permissions";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useOrder from "src/app/Zustand/Store/useOrder";
import useUser from "src/app/Zustand/Store/useUser";
import {MapMarker, Motorcycle} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {IOrderService} from "src/business/Interfaces/Services/IOrder";
import {IOrderStatusService} from "src/business/Interfaces/Services/IOrderStatus";
import IBackgroundLocation from "src/business/Interfaces/Utils/IBackgroundLocation";
import {OrderList} from "src/business/Models/List/Order";
import {EProfile} from "src/business/Models/Profile";
import AppError from "src/business/Tools/AppError";

interface IProps {
  data: OrderList;
}

const ButtonsDeliveryman = ({data}: IProps) => {
  const styles = customStyles();

  const {orders: resources} = useTranslation();
  const {user} = useUser();
  const [modalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const {currentStatus: zustandCurrentStatus, setShowStoreRoute} = useOrder();
  const {location} = useLocation(data.id);
  const [mapPopupVisibility, setMapPopupVisibility] = useState(false);
  const viewType: "client" | "store" =
    zustandCurrentStatus === EOrderStatusValue.on_route_to_store
      ? "store"
      : "client";

  const orderStatusService = container.get<IOrderStatusService>(
    TOKENS.OrderStatusService,
  );
  const orderService = container.get<IOrderService>(TOKENS.OrderService);
  const backgroundLocation = container.get<IBackgroundLocation>(
    TOKENS.BackgroundLocation,
  );

  const handleOpenMapList = () => {
    if (location) {
      setMapPopupVisibility(true);
    }
  };

  const handleOrdersAcceptedByDeliveryPerson = async () => {
    setIsLoading(true);

    backgroundLocation.startTrackingBackgroundLocation(data.id);

    const result = await orderService.updateCustomerCode(
      data.id,
      Math.floor(Math.random() * 10000)
        .toString()
        .padStart(4, "0"),
    );

    if (result instanceof AppError) {
      showToastError(result.message);
    } else {
      const resultOrderStatusChange =
        await orderStatusService.changeOrderStatus(
          data.id,
          {
            value: EOrderStatusValue.on_route_to_store,
          },
          EProfile.deliveryman,
        );

      if (resultOrderStatusChange instanceof AppError) {
        showToastError(resources.errors.order_already_taken);

        rootNavigation("MainApp", {
          screen: "OrdersHome",
          params: {
            refresh: true,
            deliverymanOrderStatusFilter:
              EOrderStatusValue.waiting_for_the_delivery_person,
          },
        });
        return;
      }

      const response = await orderService.updateByDeliverymanId(
        user!.id,
        data.id,
      );

      if (response instanceof AppError) {
        showToastError(resources.errors.load_order, {duration: 5000});
      }

      setIsLoading(false);

      setShowStoreRoute(true);

      rootNavigation("MainApp", {
        screen: "OrdersHome",
        params: {
          refresh: true,
          deliverymanOrderStatusFilter: EOrderStatusValue.on_route_to_store,
        },
      });
    }
  };

  return (
    <>
      {location ? (
        <PopupMap
          visibility={mapPopupVisibility}
          onAppPressed={() => setMapPopupVisibility(false)}
          onCancelPressed={() => setMapPopupVisibility(false)}
          setIsVisible={visi => setMapPopupVisibility(visi)}
          latitude={
            viewType === "client"
              ? location.client.latitude
              : location.store.latitude
          }
          longitude={
            viewType === "client"
              ? location.client.longitude
              : location.store.longitude
          }
        />
      ) : null}
      {zustandCurrentStatus ===
      EOrderStatusValue.waiting_for_the_delivery_person ? (
        <>
          {location ? (
            <PopupMap
              visibility={mapPopupVisibility}
              onAppPressed={() => setMapPopupVisibility(false)}
              onCancelPressed={() => setMapPopupVisibility(false)}
              setIsVisible={visi => setMapPopupVisibility(visi)}
              latitude={
                viewType === "client"
                  ? location.client.latitude
                  : location.store.latitude
              }
              longitude={
                viewType === "client"
                  ? location.client.longitude
                  : location.store.longitude
              }
            />
          ) : null}
          <Box {...styles.boxFooter}>
            <Button onPress={() => setModalVisible(!modalVisible)}>
              <HStack alignItems="center">
                <Motorcycle {...styles.deliveryIcon.style} />
                <Text {...styles.deliveryText}>
                  {resources.label.accept_order}
                </Text>
              </HStack>
            </Button>
          </Box>
          <ModalRN
            title={resources.label.background_location_task_name}
            visibility={modalVisible}
            onClose={() => setModalVisible(!modalVisible)}
            componentFooter={
              <Button.Group direction="row" {...styles.buttonGroup}>
                <Button
                  onPress={() => {
                    setModalVisible(!modalVisible);
                  }}
                  {...styles.btnNegative}>
                  {resources.button.cancel}
                </Button>
                <Button
                  onPress={() => {
                    handleOneAppPermission({
                      android: PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION,
                    });
                    handleOrdersAcceptedByDeliveryPerson();
                  }}
                  isLoading={isLoading}
                  {...styles.btnPositive}>
                  {resources.button.accept}
                </Button>
              </Button.Group>
            }>
            {resources.text.accept_permission}
          </ModalRN>
        </>
      ) : zustandCurrentStatus === EOrderStatusValue.on_delivery_route ? (
        <Box {...styles.boxCustomerCode}>
          <Button onPress={() => handleOpenMapList()}>
            <HStack alignItems="center">
              <MapMarker {...styles.deliveryIcon.style} />
              <Text {...styles.deliveryText}>
                {resources.placeholder.continue_tracking}
              </Text>
            </HStack>
          </Button>
          <ValidateCustomerCode orderId={data.id} />
        </Box>
      ) : zustandCurrentStatus === EOrderStatusValue.on_route_to_store ? (
        <Box {...styles.boxContinue}>
          <Button
            {...styles.buttonContinue}
            onPress={() => handleOpenMapList()}>
            <HStack alignItems="center">
              <MapMarker {...styles.deliveryIcon.style} />
              <Text {...styles.deliveryText}>
                {resources.placeholder.open_map}
              </Text>
            </HStack>
          </Button>
        </Box>
      ) : null}
    </>
  );
};

export default ButtonsDeliveryman;
