import {useNavigation} from "@react-navigation/native";
import {
  Box,
  Button,
  Divider,
  HStack,
  Radio,
  Text,
  TextArea,
  VStack,
} from "native-base";
import React, {useState} from "react";
import showToastError from "src/app/Components/Toast/toastError";
import {Components} from "src/app/Context/Utils/Components";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/Buttons/Shopkeeper/styles";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useOrder from "src/app/Zustand/Store/useOrder";
import {Motorcycle} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import queryClient from "src/business/Config/Query/query-client";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {IOrderStatusService} from "src/business/Interfaces/Services/IOrderStatus";
import {OrderList} from "src/business/Models/List/Order";
import AppError from "src/business/Tools/AppError";

interface IProps {
  data: OrderList;
  currentStatus: EOrderStatusValue | undefined;
}

interface ISelectedStatus {
  value: IRadioOptions | string;
  reason?: string;
}

type IRadioOptions = "cancel" | "accept";

const ButtonsShopkeeper = ({currentStatus, data}: IProps) => {
  const styles = customStyles();
  const [isLoading, setIsLoading] = useState(false);

  const {
    orders: resources,
    generic: {errors: genericErrors},
  } = useTranslation();
  const orderStatusService = container.get<IOrderStatusService>(
    TOKENS.OrderStatusService,
  );

  const {currentStatus: zustandCurrentStatus} = useOrder();

  const [selectedStatus, setSelectedStatus] = useState<ISelectedStatus>({
    value: "",
    reason: data.orderStatus[0].observation,
  });

  const handleRequestDeliveryman = async () => {
    setIsLoading(true);
    const response = await orderStatusService.changeOrderStatus(data.id, {
      value: EOrderStatusValue.waiting_for_the_delivery_person,
    });

    if (response instanceof AppError) {
      const entries = Object.entries(response.error?.fields || {});

      if (entries[0][1][0] === "not_allowed") {
        showToastError(resources.errors.not_allowed);
      } else {
        showToastError(genericErrors.generic_message);
      }
    } else {
      rootNavigation("MainApp", {
        screen: "OrdersHome",
        params: {
          refresh: true,
          shopkeeperOrderStatusFilter: EOrderStatusValue.preparing,
        },
      });
    }
    setIsLoading(false);
  };

  const handleReadyToDelivery = async () => {
    setIsLoading(true);
    const response = await orderStatusService.changeOrderStatus(data.id, {
      value: EOrderStatusValue.on_delivery_route,
    });

    if (response instanceof AppError) {
      const entries = Object.entries(response.error?.fields || {});

      if (entries[0][1][0] === "not_allowed") {
        showToastError(resources.errors.not_allowed);
      } else {
        showToastError(genericErrors.generic_message);
      }
    } else {
      rootNavigation("MainApp", {
        screen: "OrdersHome",
        params: {
          refresh: true,
          shopkeeperOrderStatusFilter: EOrderStatusValue.on_route_to_store,
        },
      });
    }
    setIsLoading(false);
  };

  const handleStatusChanges = async () => {
    setIsLoading(true);
    const response =
      selectedStatus.value === "accept"
        ? await orderStatusService.changeOrderStatus(data.id, {
            value: EOrderStatusValue.preparing,
          })
        : await orderStatusService.changeOrderStatus(data.id, {
            value: EOrderStatusValue.rejected,
            observation: selectedStatus.reason?.trim(),
          });

    if (response instanceof AppError) {
      Components.showFormErrors(response);
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.GET_STORE_ORDERS_PAGED],
      });

      setTimeout(() => {
        rootNavigation("MainApp", {
          screen: "OrdersHome",
          params: {
            refresh: true,
          },
        });
      }, 2000);

      return;
    }

    rootNavigation("MainApp", {
      screen: "OrdersHome",
      params: {
        refresh: true,
        shopkeeperOrderStatusFilter: EOrderStatusValue.payment_made,
      },
    });
    setIsLoading(false);
  };

  return zustandCurrentStatus === EOrderStatusValue.payment_made ? (
    <Box {...styles.boxFooter}>
      <Divider thickness={1} />
      <VStack {...styles.vStackFooter}>
        <HStack {...styles.statusHStack}>
          <Text {...styles.statusText}>{resources.label.choose_status}</Text>
          <Text {...styles.statusRequired}>*</Text>
        </HStack>

        <Radio.Group
          onChange={item => {
            setSelectedStatus({...selectedStatus, value: item});
          }}
          name={resources.label.radio_group}
          accessibilityLabel={resources.label.radio_group}>
          <HStack {...styles.menuContainer}>
            <Radio {...styles.radioIcon} value="accept" colorScheme="green">
              <Text {...styles.radioText}>{resources.label.accept_order}</Text>
            </Radio>
          </HStack>

          <Divider thickness={1} />

          <HStack {...styles.menuContainer}>
            <Radio {...styles.radioIcon} value="cancel" colorScheme="secondary">
              <Text {...styles.radioText}>{resources.label.reject_order}</Text>
            </Radio>
          </HStack>
        </Radio.Group>
        {selectedStatus.value === "cancel" ? (
          <Box {...styles.textArea}>
            <TextArea
              placeholder={resources.label.cancel_reason}
              autoCompleteType={undefined}
              _input={{...styles.textAreaInput}}
              value={selectedStatus.reason}
              onChangeText={textAreaValue =>
                setSelectedStatus({
                  ...selectedStatus,
                  reason: textAreaValue,
                })
              }
              maxLength={150}
            />
            <Text>
              {selectedStatus.reason?.length || 0}/{150}
            </Text>
          </Box>
        ) : null}
      </VStack>
      <Button
        onPress={handleStatusChanges}
        isDisabled={selectedStatus.value === ""}
        isLoading={isLoading}>
        {resources.button.save}
      </Button>
    </Box>
  ) : zustandCurrentStatus === EOrderStatusValue.preparing ? (
    <Box {...styles.boxFooter}>
      <Button onPress={handleRequestDeliveryman} isLoading={isLoading}>
        <HStack alignItems="center">
          <Motorcycle {...styles.deliveryIcon.style} />
          <Text {...styles.deliveryText}>
            {resources.label.request_deliveryman}
          </Text>
        </HStack>
      </Button>
    </Box>
  ) : zustandCurrentStatus === EOrderStatusValue.on_route_to_store ? (
    <Box {...styles.boxFooter}>
      <Button onPress={handleReadyToDelivery} isLoading={isLoading}>
        <HStack alignItems="center">
          <Motorcycle {...styles.deliveryIcon.style} />
          <Text {...styles.deliveryText}>{resources.label.delivery}</Text>
        </HStack>
      </Button>
    </Box>
  ) : null;
};

export default ButtonsShopkeeper;
