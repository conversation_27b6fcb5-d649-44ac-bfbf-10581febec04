import React from "react";
import {Box, Text, VStack, Center, Spinner} from "native-base";
import {Dimensions} from "react-native";
import {heightPercentageToDP as hp} from "react-native-responsive-screen";
import TrackingOrderComponent from "src/app/Components/TrackingOrder";
import useTranslation from "src/app/Hooks/useTranslation";
import {useLocation} from "src/app/Components/TrackingOrder/Hooks/useLocation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/DeliverymanLocationMap/styles";

const {width} = Dimensions.get("screen");

interface DeliverymanLocationMapProps {
  orderId: string;
}

const DeliverymanLocationMap: React.FC<DeliverymanLocationMapProps> = ({
  orderId,
}) => {
  const styles = customStyles();
  const {orders} = useTranslation();
  const {location} = useLocation(orderId);

  const mapDimensions = {
    width: width * 0.9,
    height: hp("25%"),
  };

  const zoomLevel = {
    min: 10,
    max: 18,
  };

  const hasLocationData =
    location?.deliveryman && location.deliveryman.length > 0;

  console.log("DeliverymanLocationMap - orderId:", orderId);
  console.log("DeliverymanLocationMap - hasLocationData:", hasLocationData);

  return (
    <VStack {...styles.container}>
      <Text {...styles.title}>
        {(orders.label as any).deliveryman_location ||
          "Localização do Entregador"}
      </Text>

      <Box {...styles.mapContainer}>
        {hasLocationData ? (
          <TrackingOrderComponent
            orderId={orderId}
            dimensionsMap={mapDimensions}
            zoomLevel={zoomLevel}
            showsMyLocationButton={false}
          />
        ) : (
          <Center height={hp("25%")}>
            <Spinner size="sm" />
            <Text mt={2} fontSize="sm" color="gray.500" textAlign="center">
              {location === undefined
                ? "Carregando localização..."
                : "Localização não disponível"}
            </Text>
          </Center>
        )}
      </Box>
    </VStack>
  );
};

export default DeliverymanLocationMap;
