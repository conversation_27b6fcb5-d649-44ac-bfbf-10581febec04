import React, {useEffect, useState} from "react";
import {Box, Text, VStack, Spinner, Center} from "native-base";
import {Dimensions} from "react-native";
import {heightPercentageToDP as hp} from "react-native-responsive-screen";
import TrackingOrderComponent from "src/app/Components/TrackingOrder";
import useTranslation from "src/app/Hooks/useTranslation";
import {loadDeliverymanLocation} from "src/app/Modules/Main/Orders/Presentation/TrackingOrder/Utils/loadDeliverymanLocation";
import {TrackingOrderList} from "src/business/Models/List/TrackingOrder";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/DeliverymanLocationMap/styles";

const {width} = Dimensions.get("screen");

interface DeliverymanLocationMapProps {
  orderId: string;
}

// Hook customizado com tratamento de erro
const useLocationWithErrorHandling = (orderId: string) => {
  const [location, setLocation] = useState<TrackingOrderList | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const fetchLocation = async () => {
      try {
        setIsLoading(true);
        setHasError(false);
        console.log("Fetching location for orderId:", orderId);

        const data = await loadDeliverymanLocation(orderId);
        console.log("Location data received:", data);

        if (data) {
          setLocation(data);
        } else {
          setHasError(true);
          console.log("No location data available");
        }
      } catch (error) {
        console.error("Error fetching location:", error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    if (orderId) {
      fetchLocation();
    }
  }, [orderId]);

  return {location, isLoading, hasError};
};

const DeliverymanLocationMap: React.FC<DeliverymanLocationMapProps> = ({
  orderId,
}) => {
  const styles = customStyles();
  const {orders} = useTranslation();
  const {location, isLoading, hasError} = useLocationWithErrorHandling(orderId);

  const mapDimensions = {
    width: width * 0.9,
    height: hp("25%"),
  };

  const zoomLevel = {
    min: 10,
    max: 18,
  };

  const hasLocationData =
    location?.deliveryman && location.deliveryman.length > 0;

  const renderContent = () => {
    if (isLoading) {
      return (
        <Center height={hp("25%")}>
          <Spinner size="lg" />
          <Text mt={2} fontSize="sm" color="gray.500">
            Carregando localização...
          </Text>
        </Center>
      );
    }

    if (hasError) {
      return (
        <Center height={hp("25%")}>
          <Text fontSize="sm" color="gray.500" textAlign="center">
            Localização não disponível{"\n"}O entregador ainda não iniciou o
            tracking
          </Text>
        </Center>
      );
    }

    if (hasLocationData) {
      return (
        <TrackingOrderComponent
          orderId={orderId}
          dimensionsMap={mapDimensions}
          zoomLevel={zoomLevel}
          showsMyLocationButton={false}
        />
      );
    }

    return (
      <Center height={hp("25%")}>
        <Text fontSize="sm" color="gray.500" textAlign="center">
          Aguardando localização do entregador...
        </Text>
      </Center>
    );
  };

  return (
    <VStack {...styles.container}>
      <Text {...styles.title}>
        {(orders.label as any).deliveryman_location ||
          "Localização do Entregador"}
      </Text>

      <Box {...styles.mapContainer}>{renderContent()}</Box>
    </VStack>
  );
};

export default DeliverymanLocationMap;
