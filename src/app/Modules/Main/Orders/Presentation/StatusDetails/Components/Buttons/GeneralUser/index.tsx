import {<PERSON>, <PERSON>ton, HStack, Text} from "native-base";
import React, {useState} from "react";
import ModalRN from "src/app/Components/Modal";
import ModalSimple from "src/app/Components/ModalSimple";
import CreateReview from "src/app/Components/Review/Create";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/Buttons/GeneralUser/styles";
import useCancelOrder from "src/app/Modules/Main/Orders/Query/useCancelOrder";
import {rootGoBack} from "src/app/Utils/RootNavigation";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import {PeopleCarryBox} from "src/assets/Icons/Flaticon";
import {OrderStatusStep} from "src/business/DTOs/OrdersStatusList";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {UserOrdersList} from "src/business/Models/List/UserOrdersList";

interface IProps {
  data: UserOrdersList;
  currentStatus: OrderStatusStep;
}

const ButtonsGeneralUser = ({data, currentStatus}: IProps) => {
  const styles = customStyles();
  const {orders: resources, review} = useTranslation();
  const [isCanceling, setIsCanceling] = useState(false);
  const [modalVisibility, setModalVisibility] = useState(false);
  const cancelOrderMutation = useCancelOrder();

  const showModal = () => {
    setModalVisibility(!modalVisibility);
  };

  const closeModal = () => {
    setModalVisibility(false);
  };

  const handleModalCancel = () => {
    setIsCanceling(!isCanceling);
  };

  const handleCancelOrder = async () => {
    cancelOrderMutation.mutate(data.id, {
      onSuccess: () => {
        handleModalCancel();
        rootGoBack();
        showToastSuccess(resources.toasts.cancel_success, {duration: 4000});
      },
    });
  };

  return currentStatus.value === EOrderStatusValue.payment_made ? (
    <Box {...styles.boxFooter}>
      <Button
        {...styles.buttonCancel}
        onPress={handleModalCancel}
        isLoading={cancelOrderMutation.isPending}>
        <Text {...styles.lockText}>{resources.label.cancel_order}</Text>
      </Button>
      <ModalRN
        title={resources.modalCancel.label.title}
        visibility={isCanceling}
        onClose={handleModalCancel}
        componentFooter={
          <Button.Group direction="row" {...styles.buttonGroup}>
            <Button onPress={handleModalCancel} {...styles.buttonNo}>
              {resources.modalCancel.button.no}
            </Button>
            <Button
              onPress={handleCancelOrder}
              {...styles.buttonYes}
              isLoading={cancelOrderMutation.isPending}>
              {resources.modalCancel.button.yes}
            </Button>
          </Button.Group>
        }>
        {resources.modalCancel.label.text}
      </ModalRN>
    </Box>
  ) : currentStatus.value === EOrderStatusValue.delivered ||
    currentStatus.value === EOrderStatusValue.canceled_delivery ? (
    <Box {...styles.boxFooter}>
      {!data.ratingDelivery || data.ratingDelivery === 0 ? (
        <>
          <Button onPress={() => showModal()}>
            <HStack alignItems="center">
              <PeopleCarryBox {...styles.lockIcon.style} />
              <Text {...styles.lockText}>{review.text.rateDeliveryman}</Text>
            </HStack>
          </Button>
          <ModalSimple
            visibility={modalVisibility}
            onClose={closeModal}
            titleHeader={setResourceParameters(
              review.header.rating,
              review.label.deliveryman,
            )}
            useKeyboard>
            <CreateReview
              title={setResourceParameters(
                review.title.deliveryman,
                `${data.deliveryman?.firstName} ${data.deliveryman?.lastName}`,
              )}
              orderId={data.id}
              deliverymanId={data.deliveryman?.id}
              storeId={data.storeId}
            />
          </ModalSimple>
        </>
      ) : null}
    </Box>
  ) : null;
};

export default ButtonsGeneralUser;
