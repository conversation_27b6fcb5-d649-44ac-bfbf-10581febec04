import {IStyleProps} from "src/business/Interfaces/IStyleProps";

import {moderateScale, verticalScale} from "src/app/Utils/Metrics";

import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    boxFooter: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.background,
        paddingHorizontal: wp("2%"),
        paddingBottom: wp("3%"),
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 99,
      },
    },

    tracking: {
      style: {
        display: "flex",
        flexDirection: "column",
        marginHorizontal: verticalScale(10),
      },
    },

    deliveryIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginRight: wp("2%"),
        fill: ThemesApp.getTheme().colors.white,
      },
    },
    deliveryText: {
      color: ThemesApp.getTheme().colors.white,
      fontSize: moderateScale(14),
      lineHeight: moderateScale(16),
    },

    boxCustomerCode: {
      style: {
        height: hp("9%"),
        paddingHorizontal: wp("2%"),
        backgroundColor: ThemesApp.getTheme().colors.background,
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 99,
      },
    },

    boxContinue: {
      style: {
        height: hp("5%"),
        paddingHorizontal: wp("2%"),
        backgroundColor: ThemesApp.getTheme().colors.background,
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 99,
      },
    },

    buttonContinue: {
      style: {
        height: hp("4%"),
      },
    },

    boxValidateCode: {
      style: {
        height: hp("10%"),
        paddingHorizontal: wp("2%"),
        position: "absolute",
        bottom: -4,

        left: 0,
        right: 0,
        zIndex: 99,
      },
    },

    btnPositive: {
      style: {
        width: wp("30%"),
      },
    },

    btnNegative: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.error,
      },
    },
  };
};

export default customStyles;
