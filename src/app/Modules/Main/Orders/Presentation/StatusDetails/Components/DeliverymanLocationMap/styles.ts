import {useTheme} from "native-base";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";

const useCustomStyles = () => {
  const {colors} = useTheme();

  return {
    container: {
      marginTop: wp("4%"),
      marginBottom: wp("2%"),
      paddingHorizontal: wp("4%"),
    },
    title: {
      fontSize: wp("4%"),
      fontWeight: "600" as const,
      color: colors.text?.[900] || colors.black,
      marginBottom: wp("3%"),
    },
    mapContainer: {
      borderRadius: wp("3%"),
      overflow: "hidden" as const,
      elevation: 3,
      shadowColor: colors.black,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      backgroundColor: colors.white,
    },
  };
};

export default useCustomStyles;
