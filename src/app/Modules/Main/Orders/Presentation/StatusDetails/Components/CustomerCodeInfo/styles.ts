import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { RFValue } from "react-native-responsive-fontsize";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        marginVertical: verticalScale(25),
        justifyContent: "space-between",
      },
    },
    codeText: {
      color: ThemesApp.getTheme().colors.primary[500],
      style: {
        fontSize: RFValue(18),
        lineHeight: RFValue(18),
        alignSelf: "center",
      },
    },
    statusText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.accordionStatusText,
      },
    },
  };
};

export default customStyles;
