import { Di<PERSON><PERSON>, HStack, Text } from "native-base";
import React from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/CustomerCodeInfo/styles";

interface Props {
  customerCode: string;
}

const CustomerCode = ({ customerCode }: Props) => {
  const { orders: resources } = useTranslation();
  const styles = customStyles();

  return (

    <>
      <HStack {...styles.container}>
        <Text {...styles.statusText}>{resources.label.customerCode}</Text>
        <Text {...styles.codeText}>
          {customerCode}
        </Text>
      </HStack>
      <Divider />
    </>
  );
};

export default CustomerCode;
