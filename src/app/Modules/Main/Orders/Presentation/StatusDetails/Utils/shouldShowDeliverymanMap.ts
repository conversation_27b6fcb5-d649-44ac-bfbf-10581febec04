import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import { EProfile } from "src/business/Models/Profile";

/**
 * Determina se o mapa de localização do entregador deve ser exibido
 * @param currentStatus - Status atual do pedido
 * @param userProfile - Perfil do usuário atual
 * @param hasDeliveryman - Se o pedido tem um entregador atribuído
 * @returns boolean - Se deve mostrar o mapa
 */
export const shouldShowDeliverymanMap = (
  currentStatus: EOrderStatusValue,
  userProfile: EProfile,
  hasDeliveryman: boolean = false
): boolean => {
  // Não mostrar para o próprio entregador (ele tem a tela de tracking dedicada)
  if (userProfile === EProfile.deliveryman) {
    return false;
  }

  // Só mostrar se há um entregador atribuído ao pedido
  if (!hasDeliveryman) {
    return false;
  }

  // Status onde faz sentido mostrar o mapa
  const trackingStatuses = [
    EOrderStatusValue.on_route_to_store,    // Entregador indo para a loja
    EOrderStatusValue.on_delivery_route,    // Entregador em rota de entrega
  ];

  return trackingStatuses.includes(currentStatus);
};

export default shouldShowDeliverymanMap;
