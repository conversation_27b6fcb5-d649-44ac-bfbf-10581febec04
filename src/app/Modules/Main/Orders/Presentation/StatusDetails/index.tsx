/* eslint-disable react-hooks/exhaustive-deps */
import {useIsFocused} from "@react-navigation/native";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {useQueryClient} from "@tanstack/react-query";
import {<PERSON>, ScrollView, Spinner, VStack} from "native-base";
import React, {useEffect} from "react";
import {RefreshControl} from "react-native";
import {heightPercentageToDP as hp} from "react-native-responsive-screen";
import StackHeader from "src/app/Components/Header/StackHeader";
import showToastError from "src/app/Components/Toast/toastError";
import useTranslation from "src/app/Hooks/useTranslation";
import AddressInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/AddressInfo";
import ButtonsDeliveryman from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/Buttons/Deliveryman";
import ButtonsGeneralUser from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/Buttons/GeneralUser";
import ButtonsShopkeeper from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/Buttons/Shopkeeper";
import ClientInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/ClientInfo";
import CustomerCode from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/CustomerCodeInfo";
import DeliverymanInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/DeliverymanInfo";
import DeliveryTimeInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/DeliveryTimeInfo";
import HeaderInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/HeaderInfo";
import OrderAmountInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/OrderAmountInfo";
import OrderItemInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/OrderItemInfo";
import PaymentInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/PaymentInfo";
import StatusOrderInfo from "src/app/Modules/Main/Orders/Presentation/StatusDetails/Components/StatusOrderInfo";
import customStyles from "src/app/Modules/Main/Orders/Presentation/StatusDetails/styles";
import useGetOrderDetails from "src/app/Modules/Main/Orders/Query/useGetOrderDetails";
import useGetTransactionByOrder from "src/app/Modules/Main/Orders/Query/useGetTransactionByOrder";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import CapitalizeTitleLetter from "src/app/Utils/CapitalizeTitleLetter";
import {getIndexEnum} from "src/app/Utils/GetIndexEnum";
import {handleInAppReview} from "src/app/Utils/HandleInAppReview";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useOrder from "src/app/Zustand/Store/useOrder";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {EProfile} from "src/business/Models/Profile";

export type Props = NativeStackScreenProps<
  MainAppStackParamList,
  "OrderStatusDetails"
>;

const OrderStatusDetails: React.FC<Props> = ({navigation, route}) => {
  const {orderId} = route.params;
  const queryClient = useQueryClient();
  const styles = customStyles();
  const {selectedProfile} = useGeneralSettings();
  const currentProfile = selectedProfile || EProfile.client;

  const {
    routes,
    orders: {errors},
  } = useTranslation();

  const isFocused = useIsFocused();

  const orderDetailsQuery = useGetOrderDetails(
    orderId,
    undefined,
    () => {
      showToastError(errors.load_failed);
    },
    isFocused,
  );

  const transactionByOrderQuery = useGetTransactionByOrder(
    orderId,
    undefined,
    () => {
      showToastError(errors.load_failed);
    },
  );

  const order = orderDetailsQuery.data;
  const {setCurrentStatus} = useOrder();
  const transaction =
    transactionByOrderQuery.data?.[transactionByOrderQuery.data.length - 1];

  const isLoading =
    orderDetailsQuery.isFetching || transactionByOrderQuery.isFetching;

  useEffect(() => {
    if (order && order.orderStatus?.[0].value === EOrderStatusValue.preparing) {
      handleInAppReview();
    }
    if (order) setCurrentStatus(order.orderStatus[0].value);
  }, [order]);

  useEffect(() => {
    const unsubscribe = navigation.addListener("blur", () => {
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.GET_ORDER_DETAILS, orderId],
        });
      }, 1000);
    });

    return unsubscribe;
  }, [orderId]);

  const getMarginByOrderStatusType = () => {
    return order?.orderStatus[0].value === EOrderStatusValue.payment_made
      ? hp("14%")
      : undefined;
  };

  const handleChatStatus = () => {
    const currentOrderStatusValueIndex = getIndexEnum(
      EOrderStatusValue,
      order!.orderStatus[0].value,
    );
    const placedOrderIndex = getIndexEnum(
      EOrderStatusValue,
      EOrderStatusValue.placed_order,
    );
    const onRouteToStore = getIndexEnum(
      EOrderStatusValue,
      EOrderStatusValue.on_route_to_store,
    );

    return currentProfile !== EProfile.deliveryman
      ? currentOrderStatusValueIndex >= placedOrderIndex
      : currentOrderStatusValueIndex >= onRouteToStore;
  };

  const onRefresh = () => {
    orderDetailsQuery.refetch();
  };

  return (
    <>
      <StackHeader
        navigation={navigation}
        route={route}
        title={routes.OrderDetails}
      />

      {!order || isLoading ? (
        <Box {...styles.loadingContainer}>
          <Spinner size="sm" />
        </Box>
      ) : (
        <VStack {...styles.container}>
          <ScrollView
            refreshControl={
              <RefreshControl refreshing={isLoading} onRefresh={onRefresh} />
            }
            showsVerticalScrollIndicator={true}
            {...styles.scrollview}
            marginBottom={getMarginByOrderStatusType()}>
            <HeaderInfo
              storeName={order.store.name}
              orderCode={order.code}
              hasChat={handleChatStatus()}
              orderId={order.id}
            />

            <StatusOrderInfo
              orderId={order.id}
              currentStatus={order?.orderStatus[0].value}
              createdAt={order.orderStatus[0].createdAt}
              orderStatusLabels={order.orderStatus}
              userProfile={currentProfile}
            />
            {order.customerCode &&
            currentProfile === EProfile.client &&
            order?.orderStatus[0].value ===
              EOrderStatusValue.on_delivery_route ? (
              <CustomerCode customerCode={order.customerCode} />
            ) : null}

            {currentProfile === EProfile.deliveryman && order?.user ? (
              <ClientInfo
                name={CapitalizeTitleLetter(
                  `${order.user.firstName} ${order.user.lastName}`,
                )}
              />
            ) : null}

            {order.deliveryman ? (
              <DeliverymanInfo deliveryman={order.deliveryman} />
            ) : null}

            <DeliveryTimeInfo
              estimatedDeliveryTime={order.estimatedDeliveryTime}
            />

            {order.address?.id && <AddressInfo address={order.address} />}

            <OrderItemInfo orderItem={order.orderItem} />

            <PaymentInfo
              transaction={transaction}
              userProfile={currentProfile}
              currentStatus={order?.orderStatus[0].value}
              orderId={orderId}
            />

            <OrderAmountInfo
              totalPrice={order.totalPrice}
              shippingPrice={order.shippingPrice}
              orderItem={order.orderItem}
            />
            <Box {...styles.auxBox} />
          </ScrollView>

          {/* // FIXME - Problemas com o data */}
          {currentProfile === EProfile.client ? (
            <ButtonsGeneralUser
              data={order}
              currentStatus={order.orderStatus[0]}
            />
          ) : currentProfile === EProfile.shopkeeper ? (
            <ButtonsShopkeeper
              data={order}
              currentStatus={order?.orderStatus[0].value}
            />
          ) : currentProfile === EProfile.deliveryman ? (
            <ButtonsDeliveryman data={order} />
          ) : null}
        </VStack>
      )}
    </>
  );
};

export default OrderStatusDetails;
