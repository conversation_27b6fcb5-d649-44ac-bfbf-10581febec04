import {useFocusEffect, useIsFocused} from "@react-navigation/native";
import {Alert, Box, Button, FlatList, HStack, VStack, Text} from "native-base";
import React, {useCallback, useEffect, useState} from "react";
import ItemNotFound from "src/app/Components/ItemNotFound";
import ListFooter from "src/app/Components/ListFooter";
import showToastError from "src/app/Components/Toast/toastError";
import useFlatInfiniteQueryData from "src/app/Hooks/useFlatInfiniteQueryData";
import useTranslation from "src/app/Hooks/useTranslation";
import OrderListItem from "src/app/Modules/Main/Orders/Presentation/Home/Components/ShopkeeperOrders/OrderListItem";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/ShopkeeperOrders/styles";
import OrderStatusFilter from "src/app/Modules/Main/Orders/Presentation/Home/Components/StatusFilter";
import useGetStoreOrdersPaginated from "src/app/Modules/Main/Orders/Query/useGetStoreOrdersPaginated";
import useGetStoresNames from "src/app/Modules/Main/Orders/Query/useGetStoresNames";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {StoreNameList} from "src/business/Models/List/StoreName";
import {StoreOrderList} from "src/business/Models/Order/PagedOrder";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import VerifyPixWarning from "src/app/Components/Warning";
import useUser from "src/app/Zustand/Store/useUser";
import useBottomTabs from "src/app/Zustand/Store/useBottomTabs";
import EBottomTabItems from "src/app/Components/BottomTab/EBottomTabItems";
import OrderStoreFilter from "./StoresFilter";
import useGetHasStoreMissingPix from "../../../../Query/useGetHasStoreMissingPix";

interface Props {
  orderStatusFilter: EOrderStatusValue | undefined;
  refresh?: boolean;
}

const ShopkeeperOrders: React.FC<Props> = ({
  orderStatusFilter,
  refresh,
}: Props) => {
  const styles = customStyles();

  const [storeOptions, setStoreOptions] = useState<StoreNameList[]>([]);
  const [filters, setFilters] = useState<{
    storeId: string;
    status?: EOrderStatusValue;
  }>({
    storeId: "",
    status: EOrderStatusValue.payment_made,
  });
  const {user} = useUser();
  const storesMissingPixKey = useGetHasStoreMissingPix(user?.id || "");
  useEffect(() => {
    if (orderStatusFilter) {
      setFilters(prev => ({
        ...prev,
        status: orderStatusFilter,
      }));
    }
  }, [orderStatusFilter]);

  const isFocused = useIsFocused();

  useFocusEffect(
    useCallback(() => {
      storesMissingPixKey.refetch();
      if (isFocused && refresh) {
        storeOrdersQuery.refetch();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [refresh]),
  );

  const {
    orders: {label: resources, errors: resourcesErrors},
    forms: {
      stores: {toasts: storeErrors},
    },
  } = useTranslation();

  const renderItem = useCallback(({item}: {item: StoreOrderList}) => {
    return <OrderListItem data={item} key={item.id} />;
  }, []);

  const storeNamesQuery = useGetStoresNames(
    data => {
      if (!filters.storeId && data.length === 1) {
        const option = data[0];

        setFilters(prev => ({
          ...prev,
          storeId: option.id,
        }));
      }

      setStoreOptions(data);
    },
    () => {
      showToastError(storeErrors.not_found);
    },
  );

  const storeOrdersQuery = useGetStoreOrdersPaginated(
    {
      storeId: filters.storeId,
      statusValue: filters.status,
    },
    undefined,
    () => {
      showToastError(resourcesErrors.load_order);
    },
    !!filters.storeId,
  );

  const isLoading = storeNamesQuery.isLoading || storeOrdersQuery.isLoading;
  const {isFetching} = storeOrdersQuery;

  const storeOrdersArray = useFlatInfiniteQueryData(storeOrdersQuery);

  const handlePaginate = () => {
    if (storeOrdersQuery.hasNextPage) {
      storeOrdersQuery.fetchNextPage();
    }
  };

  const handleStoreFilterSelect = async (option: StoreNameList) => {
    if (filters.storeId !== option.id) {
      setFilters(prev => ({
        ...prev,
        storeId: option.id,
      }));
    } else {
      setFilters(prev => ({
        ...prev,
        storeId: "",
      }));
    }
  };

  const handleRefresh = async () => {
    storeOrdersQuery.refetch();
  };

  const handleStatusChange = async (statusValue?: EOrderStatusValue) => {
    setFilters(prev => ({
      ...prev,
      status: statusValue,
    }));
  };
  const {setIsNavigationFromOutsideBottomMenu, pushTabsStack} = useBottomTabs();
  const handleMissingPixKey = () => {
    setIsNavigationFromOutsideBottomMenu();
    pushTabsStack(EBottomTabItems.STORES);
    rootNavigation("MainApp", {screen: "StoreHome"});
  };

  return (
    <Box {...styles.container}>
      <Box {...styles.dropDownContainer}>
        <OrderStoreFilter
          selectedOption={storeOptions.find(
            item => item.id === filters.storeId,
          )}
          options={storeOptions}
          onSelect={handleStoreFilterSelect}
        />
        {filters.storeId && storeOptions.length > 0 ? (
          <OrderStatusFilter
            isStore
            onStatusChange={handleStatusChange}
            selectedStatus={filters.status}
          />
        ) : null}
      </Box>

      {storesMissingPixKey.data ? (
        <VerifyPixWarning
          isDeliveryman={false}
          handleMissingPix={handleMissingPixKey}
        />
      ) : null}

      <FlatList
        data={
          storeNamesQuery.data && storeNamesQuery.data.length > 0
            ? storeOrdersArray
            : []
        }
        renderItem={renderItem}
        keyExtractor={item => item.id}
        onEndReachedThreshold={0.3}
        onEndReached={handlePaginate}
        refreshing={storeOrdersQuery.isRefetching}
        onRefresh={handleRefresh}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerFlatlist.style}
        ListEmptyComponent={
          storeOrdersArray?.length === 0 && !isLoading ? (
            !isFetching ? (
              <ItemNotFound
                flex={0}
                title={resources.empty}
                directionComponent="column"
              />
            ) : null
          ) : null
        }
        ListFooterComponent={
          <ListFooter
            isVisible={
              (isLoading || storeOrdersQuery.isFetchingNextPage) &&
              !!filters.storeId
            }
          />
        }
      />
    </Box>
  );
};

export default ShopkeeperOrders;
