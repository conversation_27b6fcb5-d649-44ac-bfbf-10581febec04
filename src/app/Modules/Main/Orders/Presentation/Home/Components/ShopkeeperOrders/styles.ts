import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {horizontalScale, moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      height: "full",
      style: {
        marginHorizontal: wp("4%"),
      },
    },

    dropDownContainer: {
      style: {},
    },
    endIconSelect: {
      size: moderateScale(20),
      style: {
        fill: ThemesApp.getTheme().colors.checkedIcon,
        position: "absolute",
        right: 0,
      },
    },
    selectItem: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.background,
        borderRadius: moderateScale(5),
      },
    },
    selectText: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    menuContainer: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    pressableMenu: {
      style: {
        borderColor: ThemesApp.getTheme().colors.gray[50],
        padding: wp("2%"),
        borderWidth: moderateScale(1),
      },
    },
    menuIcons: {
      size: moderateScale(22),
    },
    menuItemBox: {
      style: {
        padding: wp("1%"),
        width: wp("83%"),
        alignItems: "center",
      },
    },
    cancelOrderIcon: {
      size: moderateScale(30),
      style: {
        color: ThemesApp.getTheme().colors.error,
        marginRight: horizontalScale(20),
      },
    },
    acceptOrderIcon: {
      size: moderateScale(30),
      style: {
        color: ThemesApp.getTheme().colors.green[400],
        marginRight: horizontalScale(20),
      },
    },
    emptyContainer: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.muted[100],
        justifyContent: "center",
        alignItems: "center",
        marginTop: hp("10%"),
      },
    },

    contentContainerFlatlist: {
      style: {
        flexGrow: 1,
        paddingBottom: hp("15%"),
        paddingTop: hp("1%"),
      },
    },

    loadingContainer: {
      justifyContent: "center",
      alignItems: "center",
      height: "100%",
    },
  };
};

export default customStyles;
