import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    pressableDetails: {
      style: { marginTop: hp("2%") },
    },
    mainHStack: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        borderColor: ThemesApp.getTheme().colors.muted[400],
        flex: 1,
        borderRadius: moderateScale(5),
        borderWidth: moderateScale(1),
        elevation: 4,
        marginBottom: hp("2%"),
        padding: hp("1%"),
        alignItems: "center",
      },
    },
    mainVStack: {
      style: {
        flex: 1,
        margin: wp("1%"),
      },
    },
    orderText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        paddingVertical: verticalScale(6),
        fontWeight: "bold",
      },
    },
    productText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },

    textTitle: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },

    text: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    statusHStack: {
      style: {
        paddingVertical: RFValue(5),
        alignItems: "center",
        marginBottom: RFValue(5),
      },
    },
    statusType: {
      style: {
        fontWeight: "bold",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    hStackInfo: {
      justifyContent: "space-between",
      alignItems: "center",
    },

    priceDateText: {
      style: {
        color: ThemesApp.getTheme().colors.priceDateText,
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        textAlign: "right",
      },
    },
    hStackOrder: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    hStackProductName: {
      justifyContent: "space-between",
    },

    textMoreDetails: {
      style: {
        color: ThemesApp.getTheme().colors.white,
      },
    },
  };
};

export default customStyles;
