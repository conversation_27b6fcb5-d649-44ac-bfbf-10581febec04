import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {cloneDeep, uniqueId} from "lodash";
import {
  Badge,
  Box,
  Divider,
  HStack,
  Pressable,
  Text,
  VStack,
} from "native-base";
import React, {memo} from "react";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/ShopkeeperOrders/OrderListItem/styles";
import getDateByLocale from "src/app/Utils/GetDateByLocale";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {
  StoreOrderList,
  StoreOrderListOrderItem,
} from "src/business/Models/Order/PagedOrder";

interface IProps {
  data: StoreOrderList;
}

const OrderListItemRender = ({data}: IProps) => {
  const styles = customStyles();
  const {
    orders: {label: resources, status: statusResource},
    generic: {button: genericResources},
  } = useTranslation();

  const handleOpenStatusDetails = () => {
    rootNavigation("MainApp", {
      screen: "OrderStatusDetails",
      params: {orderId: data.id},
    });
  };

  const statusColor = (status: EOrderStatusValue) => {
    return status === EOrderStatusValue.placed_order ||
      status === EOrderStatusValue.payment_made ||
      status === EOrderStatusValue.delivered
      ? ThemesApp.getTheme().colors.iconContainerOwnProfile
      : status === EOrderStatusValue.canceled ||
        status === EOrderStatusValue.rejected ||
        status === EOrderStatusValue.canceled_delivery ||
        status === EOrderStatusValue.canceled_payment_failure
      ? ThemesApp.getTheme().colors.heartIcon
      : ThemesApp.getTheme().colors.iconHelp;
  };

  const organizeProducts = () => {
    const deepCopy = cloneDeep(data.orderItem);
    const noDuplicates = deepCopy.reduce((acc, item) => {
      const foundItemIndex = acc.findIndex(
        obj => obj.productName === item.productName,
      );
      if (foundItemIndex < 0) acc.push(item);
      else acc[foundItemIndex].quantity += item.quantity;
      return acc;
    }, [] as StoreOrderListOrderItem[]);
    return noDuplicates;
  };

  return (
    <HStack {...styles.mainHStack}>
      <VStack {...styles.mainVStack}>
        <HStack {...styles.hStackOrder}>
          <Text ellipsizeMode="tail" numberOfLines={2} {...styles.productText}>
            {resources.orderNumber}
            {data.code}
          </Text>

          <Text {...styles.priceDateText}>
            {getDateByLocale({date: data.lastStatus.createdAt})}
          </Text>
        </HStack>

        <Box my={3}>
          {organizeProducts().map(item => (
            <Box key={uniqueId()}>
              <HStack {...styles.hStackProductName}>
                <Text
                  ellipsizeMode="tail"
                  numberOfLines={2}
                  {...styles.textTitle}>
                  {item.productName}
                </Text>

                <Text {...styles.text}>x{item.quantity}</Text>
              </HStack>
              <Divider />
            </Box>
          ))}
        </Box>

        <HStack {...styles.hStackInfo}>
          <Text {...styles.text}>{resources.shipping}</Text>
          <Text {...styles.text}>
            {
              maskField({
                value: data.shippingPrice,
                mask: BRL_CURRENCY_DECIMAL(10),
              }).masked
            }
          </Text>
        </HStack>

        <HStack {...styles.hStackInfo}>
          <Text {...styles.text}>{resources.total_price}</Text>
          <Text {...styles.text}>
            {
              maskField({
                value: data.totalPrice,
                mask: BRL_CURRENCY_DECIMAL(10),
              }).masked
            }
          </Text>
        </HStack>

        <HStack mt={3} {...styles.hStackInfo}>
          <Text bold {...styles.text}>
            {resources.status_steps}
          </Text>
          <Text
            ellipsizeMode="tail"
            numberOfLines={2}
            color={statusColor(data.lastStatus.value)}
            {...styles.statusType}>
            {statusResource[data.lastStatus.value]}
          </Text>
        </HStack>
        <Pressable
          onPress={handleOpenStatusDetails}
          _pressed={{opacity: 0.5}}
          {...styles.pressableDetails}>
          <Badge variant="solid" _text={{...styles.textMoreDetails}}>
            {genericResources.moreDetails}
          </Badge>
        </Pressable>
      </VStack>
    </HStack>
  );
};

const OrderListItem = memo(OrderListItemRender, (prevProps, nextProps) => {
  return (
    prevProps.data.id === nextProps.data.id &&
    prevProps.data.lastStatus === nextProps.data.lastStatus
  );
});

export default OrderListItem;
