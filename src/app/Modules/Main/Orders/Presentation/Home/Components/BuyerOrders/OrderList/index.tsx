import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {cloneDeep} from "lodash";
import {
  Avatar,
  Box,
  Divider,
  HStack,
  Pressable,
  Text,
  VStack,
} from "native-base";
import React, {memo} from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/BuyerOrders/OrderList/styles";
import StatusBox from "src/app/Modules/Main/Orders/Presentation/Home/Components/BuyerOrders/StatusBox";
import OrderReviewsList from "src/app/Modules/Main/Orders/Presentation/ListReviews";
import getDateByLocale from "src/app/Utils/GetDateByLocale";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {PeopleCarryBox, Review, Store} from "src/assets/Icons/Flaticon";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {
  UserOrders,
  UserOrdersOrderItem,
} from "src/business/Models/List/UserOrders";

type Props = {
  order: UserOrders;
};

const OrderListRender: React.FC<Props> = ({order}: Props) => {
  const styles = customStyles();
  const {review: resources, orders: resourcesOrders} = useTranslation();

  const handleNavigate = () => {
    rootNavigation("MainApp", {
      screen: "OrderStatusDetails",
      params: {orderId: order.id},
    });
  };

  const handleCreateReview = () => {
    rootNavigation("MainApp", {
      screen: "OrderCreateReview",
      params: {orderId: order.id, storeId: order.store.id},
    });
  };

  const handleCreateReviewDelivery = () => {
    rootNavigation("MainApp", {
      screen: "UserDeliveryCreateReview",
      params: {
        orderId: order.id,
        // deliveryman: order.deliveryman,
      },
    });
  };

  const organizeProducts = () => {
    const deepCopy = cloneDeep(order.orderItem);
    const noDuplicates = deepCopy.reduce((acc, item) => {
      const foundItemIndex = acc.findIndex(
        obj => obj.productName === item.productName,
      );
      if (foundItemIndex < 0) acc.push(item);
      return acc;
    }, [] as UserOrdersOrderItem[]);
    return noDuplicates;
  };

  return (
    <Pressable onPress={() => handleNavigate()} _pressed={{opacity: 0.5}}>
      {({isPressed}) => {
        return (
          <HStack
            style={
              isPressed
                ? {...styles.pressedContainer.style}
                : {...styles.container.style}
            }>
            <VStack space={1} {...styles.orderInfoContainer}>
              <HStack space={2} {...styles.hStackHeader}>
                <Avatar
                  source={{
                    uri: undefined /* order.store?.files?.find(
                  file => file.usage === EUsageFile.icon,
                )?.url, */,
                  }}
                  {...styles.avatar}>
                  <Store {...styles.avatarIcon.style} />
                </Avatar>
                <VStack flex={1}>
                  <HStack {...styles.boxInfo}>
                    <Text
                      {...styles.orderCodeText}>{`${resourcesOrders.label.orderNumber}${order.code}`}</Text>

                    <Text {...styles.priceDateText}>
                      {getDateByLocale({date: new Date(order.createdAt)})}
                    </Text>
                  </HStack>
                  <HStack {...styles.storeNameBox}>
                    <Text {...styles.storeNameText}>{order.store.name}</Text>
                    {order.lastStatus === EOrderStatusValue.canceled ||
                    order.lastStatus !==
                      EOrderStatusValue.delivered ? null : !order.rating ||
                      order.rating === 0 ? (
                      <Pressable
                        onPress={() => handleCreateReview()}
                        {...styles.starBox}>
                        <HStack {...styles.rateVStack}>
                          <Review {...styles.starIcon.style} />
                          <Text {...styles.text}>{resources.text.rate}</Text>
                        </HStack>
                      </Pressable>
                    ) : (
                      <OrderReviewsList
                        orderId={order.id}
                        rating={order.rating}
                      />
                    )}
                  </HStack>
                </VStack>
              </HStack>
              {order.lastStatus &&
                order.lastStatus === EOrderStatusValue.delivered && (
                  <HStack {...styles.deliverymanReviewBox}>
                    {!order.ratingDelivery || order.ratingDelivery === 0 ? (
                      <Pressable
                        onPress={() => handleCreateReviewDelivery()}
                        {...styles.starBox}>
                        <HStack {...styles.rateVStack}>
                          <PeopleCarryBox {...styles.starIcon.style} />
                          <Text {...styles.text}>
                            {resources.text.rateDeliveryman}
                          </Text>
                        </HStack>
                      </Pressable>
                    ) : (
                      <OrderReviewsList
                        orderId={order.id}
                        rating={order.ratingDelivery}
                        deliverymanReview={true}
                      />
                    )}
                  </HStack>
                )}
              <HStack {...styles.productNameContainer}>
                <VStack {...styles.observationVStack}>
                  {organizeProducts().map(item => {
                    return (
                      <Box key={item.id}>
                        <HStack {...styles.hStackInfo}>
                          <Text
                            ellipsizeMode="tail"
                            numberOfLines={2}
                            style={
                              item.observation
                                ? {...styles.textProdSmall.style}
                                : {...styles.textProdBig.style}
                            }>
                            {item.productName}
                          </Text>

                          {/* {item.observation ? (
                        <HStack space={1} {...styles.observationHStack}>
                          <Text
                            ellipsizeMode="tail"
                            numberOfLines={2}
                            {...styles.textObsTitle}>
                            {resourcesOrders.cart.label.observation}
                            {item.observation}
                          </Text>
                        </HStack>
                      ) : null} */}
                        </HStack>
                        <Divider {...styles.divider} />
                      </Box>
                    );
                  })}
                </VStack>
              </HStack>

              <HStack {...styles.hStackInfo}>
                <Text {...styles.textTitle}>{resources.text.total_price}</Text>
                <Text {...styles.textTitle}>
                  {
                    maskField({
                      value: order.totalPrice,
                      mask: BRL_CURRENCY_DECIMAL(10),
                    }).masked
                  }
                </Text>
              </HStack>

              {order.lastStatus && <StatusBox status={order.lastStatus} />}
            </VStack>
          </HStack>
        );
      }}
    </Pressable>
  );
};

const OrderList = memo(OrderListRender, (prevProps, nextProps) => {
  return (
    prevProps.order.id === nextProps.order.id &&
    prevProps.order.lastStatus === nextProps.order.lastStatus
  );
});

export default OrderList;
