/* eslint-disable react/no-unstable-nested-components */
import {HStack, Menu, Pressable, Text} from "native-base";
import React from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/ShopkeeperOrders/StoresFilter/styles";
import {ArrowDropdown, Filter} from "src/assets/Icons/Flaticon";
import {StoreNameList} from "src/business/Models/List/StoreName";

interface Props {
  options: StoreNameList[];
  selectedOption: StoreNameList | undefined;
  onSelect: (option: StoreNameList) => void;
}

const OrderStoreFilter = ({selectedOption, options, onSelect}: Props) => {
  const {
    orders: {label},
  } = useTranslation();

  const styles = customStyles({});

  const handleItemPress = (store: StoreNameList) => {
    onSelect(store);
  };

  return (
    <Menu
      placement="top"
      trigger={triggerProps => {
        return (
          <Pressable
            accessibilityLabel={label.status_menu}
            {...triggerProps}
            {...styles.pressableMenu}>
            <HStack {...styles.menuContainer}>
              <Filter {...styles.menuIcons.style} />
              <Text {...styles.textMenu}>
                {selectedOption ? selectedOption.name : label.selectStore}
              </Text>

              <ArrowDropdown {...styles.menuIconDrop.style} />
            </HStack>
          </Pressable>
        );
      }}
      {...styles.menu}>
      {options?.map(store => {
        return (
          <Menu.Item
            key={store.id}
            onPress={() => handleItemPress(store)}
            _dark={{
              ...customStyles({id: store.id, storeId: selectedOption?.id})
                .menuItemDark,
            }}
            {...customStyles({id: store.id, storeId: selectedOption?.id})
              .menuItem}>
            <HStack {...styles.menuItemBox}>
              <Text {...styles.textMenu}>{store.name}</Text>
            </HStack>
          </Menu.Item>
        );
      })}
    </Menu>
  );
};

export default OrderStoreFilter;
