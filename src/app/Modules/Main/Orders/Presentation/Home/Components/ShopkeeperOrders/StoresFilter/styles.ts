import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {horizontalScale, moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

interface IProps {
  id?: string;
  storeId?: string;
}

const customStyles = ({id, storeId}: IProps) => {
  const stylesCustom: IStyleProps = {
    container: {
      style: {
        marginHorizontal: wp("4%"),
      },
    },

    menu: {
      style: {
        marginTop: moderateScale(26),
      },
    },

    menuContainer: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        justifyContent: "space-between",
        alignItems: "center",
      },
    },

    pressableMenu: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        borderColor: ThemesApp.getTheme().colors.muted[300],
        padding: horizontalScale(8),
        borderWidth: moderateScale(1),
        marginVertical: hp("1%"),
        marginHorizontal: wp("1%"),
      },
    },

    menuIcons: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.accordionStatusText,
      },
    },

    menuIconDrop: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.accordionStatusText,
      },
    },

    menuItemBox: {
      style: {
        width: wp("75%"),
        justifyContent: "center",
      },
    },

    textMenu: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    menuItem: {
      backgroundColor:
        id === storeId
          ? ThemesApp.getTheme().colors.gray[200]
          : ThemesApp.getTheme().colors.cardList,
    },

    menuItemDark: {
      backgroundColor:
        id === storeId
          ? ThemesApp.getTheme().colors.muted[500]
          : ThemesApp.getTheme().colors.cardList,
    },
  };
  return stylesCustom;
};
export default customStyles;
