import {RFValue} from "react-native-responsive-fontsize";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale, verticalScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        borderColor: ThemesApp.getTheme().colors.muted[400],
        width: wp("95%"),
        alignSelf: "center",
        borderRadius: moderateScale(10),
        elevation: 5,
        marginBottom: verticalScale(8),
        borderWidth: moderateScale(1),
      },
    },

    pressedContainer: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.muted[200],
        borderColor: ThemesApp.getTheme().colors.muted[400],
        width: wp("95%"),
        alignSelf: "center",
        borderRadius: moderateScale(10),
        elevation: 0,
        marginBottom: verticalScale(8),
        borderWidth: moderateScale(1),
      },
    },

    textInfo: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        marginLeft: wp("1%"),
      },
    },
    avatar: {
      size: moderateScale(40),
      style: {
        backgroundColor: ThemesApp.getTheme().colors.white,
        borderColor: ThemesApp.getTheme().colors.muted[600],
        borderWidth: RFValue(1),
      },
    },
    orderInfoContainer: {
      style: {
        flex: 1,
        padding: moderateScale(10),
      },
    },
    boxInfo: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },
    storeNameBox: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
        marginVertical: verticalScale(6),
      },
    },
    deliverymanReviewBox: {
      style: {
        justifyContent: "flex-end",
        alignItems: "center",
      },
    },
    storeNameText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    orderCodeText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    priceDateText: {
      style: {
        color: ThemesApp.getTheme().colors.priceDateText,
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        textAlign: "right",
      },
    },

    productNameContainer: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },

    starRating: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },
    starIcon: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        marginLeft: wp("2%"),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },

    deliveryIcon: {
      style: {
        width: moderateScale(15),
        height: moderateScale(15),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },

    textDeliveryInfo: {
      style: {
        color: ThemesApp.getTheme().colors.yellow[500],
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        fontWeight: "bold",
        marginLeft: wp("1%"),
      },
    },

    rateVStack: {
      style: {
        alignItems: "center",
        justifyContent: "center",
      },
    },

    text: {
      style: {
        color: ThemesApp.getTheme().colors.yellow[500],
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        marginLeft: wp("2%"),
        fontWeight: "bold",
      },
    },
    avatarIcon: {
      style: {
        width: moderateScale(22),
        height: moderateScale(22),
        fill: ThemesApp.getTheme().colors.muted[400],
      },
    },

    textTitle: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    textObsTitle: {
      style: {
        width: wp("28%"),
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    textProdSmall: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        width: wp("60%"),
      },
    },

    textProdBig: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        width: wp("90%"),
      },
    },

    divider: {
      style: {
        marginVertical: verticalScale(2),
      },
    },

    hStackInfo: {
      justifyContent: "space-between",
      alignItems: "center",
    },

    textBadgeButton: {
      style: {
        color: ThemesApp.getTheme().colors.white,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    hStackHeader: {
      style: {
        alignItems: "center",
      },
    },

    observationVStack: {
      style: {
        flex: 1,
      },
    },

    observationHStack: {
      style: {
        flex: 0.9,
        justifyContent: "flex-end",
      },
    },
  };
};

export default customStyles;
