import {FlatList, VStack} from "native-base";
import React, {useCallback} from "react";
import ItemNotFound from "src/app/Components/ItemNotFound";
import ListFooter from "src/app/Components/ListFooter";
import useFlatInfiniteQueryData from "src/app/Hooks/useFlatInfiniteQueryData";
import useTranslation from "src/app/Hooks/useTranslation";
import OrderList from "src/app/Modules/Main/Orders/Presentation/Home/Components/BuyerOrders/OrderList";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/BuyerOrders/styles";
import useGetUserOrdersPaginated from "src/app/Modules/Main/Orders/Query/useGetUserOrdersPaginated";
import resetInfiniteQueryPagination from "src/app/Utils/ResetInfiniteQueryPagination";
import useUser from "src/app/Zustand/Store/useUser";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {UserOrders} from "src/business/Models/List/UserOrders";
import {UserOrdersList} from "src/business/Models/List/UserOrdersList";

export type IPagedUserOrders = {
  result: UserOrdersList[];
  totalCount: number;
  totalPages: number;
};

const BuyerOrders: React.FC = () => {
  const styles = customStyles();

  const {user} = useUser();

  const {orders: resources} = useTranslation();

  const userOrdersQuery = useGetUserOrdersPaginated(
    {userId: user!.id},
    undefined,
    undefined,
    !!user?.id,
  );

  const userOrdersArray = useFlatInfiniteQueryData(userOrdersQuery);

  const handlePaginate = async () => {
    if (userOrdersQuery.hasNextPage) {
      userOrdersQuery.fetchNextPage();
    }
  };

  const renderItem = useCallback(
    ({item}: {item: UserOrders}) => <OrderList order={item} />,
    [],
  );

  const onRefresh = async () => {
    const queryKey = [QUERY_KEYS.GET_USER_ORDERS_PAGED, user!.id];

    resetInfiniteQueryPagination(queryKey);
    userOrdersQuery.refetch();
  };

  return (
    <VStack {...styles.container}>
      <FlatList
        data={userOrdersArray}
        keyExtractor={order => order.id}
        onEndReachedThreshold={0.5}
        onEndReached={handlePaginate}
        renderItem={renderItem}
        refreshing={userOrdersQuery.isRefetching}
        onRefresh={onRefresh}
        contentContainerStyle={{
          ...styles.contentContainerUserOrder.style,
        }}
        ListEmptyComponent={
          !userOrdersQuery.isLoading &&
          userOrdersArray &&
          userOrdersArray.length === 0 ? (
            <ItemNotFound
              flex={1}
              title={resources.label.empty}
              directionComponent="column"
            />
          ) : null
        }
        ListFooterComponent={
          <ListFooter
            isVisible={
              (userOrdersQuery.isLoading ||
                userOrdersQuery.isFetchingNextPage) &&
              !userOrdersQuery.isRefetching
            }
          />
        }
        {...styles.mainContent}
      />
    </VStack>
  );
};

export default BuyerOrders;
