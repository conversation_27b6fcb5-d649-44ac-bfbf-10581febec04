import {heightPercentageToDP as hp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      height: "full",
      style: {
        backgroundColor: ThemesApp.getTheme().colors.background,
      },
    },
    mainContent: {
      style: {},
    },
    contentContainerUserOrder: {
      style: {
        flexGrow: 1,
        paddingBottom: hp("15%"),
        paddingTop: hp("1%"),
      },
    },
  };
};

export default customStyles;
