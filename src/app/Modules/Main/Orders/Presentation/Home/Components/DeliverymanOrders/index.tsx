import {useFocusEffect, useIsFocused} from "@react-navigation/native";
import {FlatList, VStack} from "native-base";
import React, {useCallback, useEffect, useState} from "react";
import ItemNotFound from "src/app/Components/ItemNotFound";
import ListFooter from "src/app/Components/ListFooter";
import VerifyPixWarning from "src/app/Components/Warning";
import useFlatInfiniteQueryData from "src/app/Hooks/useFlatInfiniteQueryData";
import useTranslation from "src/app/Hooks/useTranslation";
import OrderListItem from "src/app/Modules/Main/Orders/Presentation/Home/Components/DeliverymanOrders/OrderListItem";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/DeliverymanOrders/styles";
import OrderStatusFilter from "src/app/Modules/Main/Orders/Presentation/Home/Components/StatusFilter";
import useGetDeliverymanCheckPixKey from "src/app/Modules/Main/Orders/Query/useGetDeliverymanCheckPix";
import useGetDeliverymanOrdersPaginated from "src/app/Modules/Main/Orders/Query/useGetDeliverymanOrdersPaginated";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useUser from "src/app/Zustand/Store/useUser";
import {StoreOrderList} from "src/business/Models/Order/PagedOrder";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import useBottomTabs from "src/app/Zustand/Store/useBottomTabs";
import EBottomTabItems from "src/app/Components/BottomTab/EBottomTabItems";

interface Props {
  orderStatusFilter: EOrderStatusValue | undefined;
  refresh?: boolean;
}

const DeliveryManOrders: React.FC<Props> = ({
  orderStatusFilter,
  refresh,
}: Props) => {
  const styles = customStyles();
  const {pushTabsStack, setIsNavigationFromOutsideBottomMenu} = useBottomTabs();
  const {
    orders: {label: resources},
  } = useTranslation();

  const [filters, setFilters] = useState({
    status: EOrderStatusValue.waiting_for_the_delivery_person,
  });
  useEffect(() => {
    if (orderStatusFilter) {
      setFilters(prev => ({
        ...prev,
        status: orderStatusFilter,
      }));
    }
  }, [orderStatusFilter]);

  const isFocused = useIsFocused();
  const {user} = useUser();
  const deliverymanHasPixKey = useGetDeliverymanCheckPixKey(user!.id);

  useFocusEffect(
    useCallback(() => {
      deliverymanHasPixKey.refetch();
      if (isFocused && refresh) {
        deliverymanOrdersQuery.refetch();
      }

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [refresh]),
  );

  const deliverymanOrdersQuery = useGetDeliverymanOrdersPaginated({
    userId: user!.id,
    statusValue: filters.status,
  });

  const deliverymanOrdersArray = useFlatInfiniteQueryData(
    deliverymanOrdersQuery,
  );

  const {isLoading, isFetching} = deliverymanOrdersQuery;

  const handleRefresh = async () => {
    deliverymanOrdersQuery.refetch();
  };

  const handleStatusFilterChange = async (statusValue?: EOrderStatusValue) => {
    if (statusValue === filters.status) return;

    setFilters({
      ...filters,
      status: statusValue || EOrderStatusValue.waiting_for_the_delivery_person,
    });
  };

  const renderItem = useCallback(({item}: {item: StoreOrderList}) => {
    return <OrderListItem data={item} key={item.id} />;
  }, []);

  const handlePaginate = () => {
    if (deliverymanOrdersQuery.hasNextPage) {
      deliverymanOrdersQuery.fetchNextPage();
    }
  };
  const handleMissingPix = () => {
    rootNavigation("MainApp", {
      screen: "UserEdit",
      params: {id: user!.id, data: user!},
    });
    setIsNavigationFromOutsideBottomMenu();
    pushTabsStack(EBottomTabItems.SETTINGS);
  };

  return (
    <VStack {...styles.container}>
      {user && user.id ? (
        <OrderStatusFilter
          onStatusChange={handleStatusFilterChange}
          selectedStatus={filters.status}
        />
      ) : null}

      {deliverymanHasPixKey.data ? null : (
        <VerifyPixWarning handleMissingPix={handleMissingPix} />
      )}

      <FlatList
        data={deliverymanOrdersArray}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        refreshing={deliverymanOrdersQuery.isRefetching}
        onRefresh={handleRefresh}
        onEndReachedThreshold={0.3}
        onEndReached={handlePaginate}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerFlatlist.style}
        ListEmptyComponent={
          deliverymanOrdersArray?.length === 0 && !isLoading ? (
            !isFetching ? (
              <ItemNotFound
                flex={0}
                title={resources.empty}
                directionComponent="column"
              />
            ) : null
          ) : null
        }
        ListFooterComponent={
          <ListFooter
            isVisible={
              deliverymanOrdersQuery.isLoading ||
              deliverymanOrdersQuery.isFetchingNextPage
            }
          />
        }
      />
    </VStack>
  );
};

export default DeliveryManOrders;
