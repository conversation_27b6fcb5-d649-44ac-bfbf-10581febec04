import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {horizontalScale, moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        marginHorizontal: wp("4%"),
      },
    },
    menu: {
      style: {
        marginTop: moderateScale(26),
      },
    },
    selectText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(18),
        lineHeight: RFValue(18),
      },
    },
    menuContainer: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        justifyContent: "space-between",
        alignItems: "center",
      },
    },

    pressableMenu: {
      style: {
        borderColor: ThemesApp.getTheme().colors.muted[300],
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        padding: horizontalScale(8),
        borderWidth: moderateScale(1),
        marginVertical: hp("1%"),
        marginHorizontal: wp("1%"),
      },
    },
    menuIcons: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        fill: ThemesApp.getTheme().colors.accordionStatusText,
      },
    },
    menuItemBox: {
      style: {
        width: wp("75%"),
        justifyContent: "center",
      },
    },
    cancelOrderIcon: {
      style: {
        width: moderateScale(30),
        height: moderateScale(30),
        fill: ThemesApp.getTheme().colors.error,
        marginRight: horizontalScale(20),
      },
    },
    acceptOrderIcon: {
      style: {
        width: moderateScale(30),
        height: moderateScale(30),
        fill: ThemesApp.getTheme().colors.green[400],
        marginRight: horizontalScale(20),
      },
    },

    menuIconDrop: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.accordionStatusText,
      },
    },

    textMenu: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
  };
};
export default customStyles;
