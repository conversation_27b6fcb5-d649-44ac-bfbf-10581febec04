/* eslint-disable react/no-unstable-nested-components */
import { HStack, Menu, Pressable, Text } from "native-base";
import React, { useMemo } from "react";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/StatusFilter/styles";
import getStatusTypeEnumValue from "src/app/Utils/GetStatusTypeEnumValue";
import { ArrowDropdown, Filter } from "src/assets/Icons/Flaticon";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";

interface IOrderStatusFilter {
  selectedStatus?: EOrderStatusValue;
  isStore?: boolean;
  isSales?: boolean;
  onStatusChange?: (statusValue?: EOrderStatusValue) => void;
}

const OrderStatusFilter = ({
  selectedStatus,
  isStore,
  isSales,
  onStatusChange,
}: IOrderStatusFilter) => {
  const styles = customStyles();

  const {
    orders: { status: resources, label },
  } = useTranslation();

  const handleStatusChanges = (statusValue?: EOrderStatusValue) => {
    if (statusValue === selectedStatus) return;

    onStatusChange && onStatusChange(statusValue);
  };

  const statusItems = useMemo(
    () => [
      {
        status: undefined,
        resources: resources.all_status,
        shouldRender: isStore || isSales,
      },
      {
        status: EOrderStatusValue.payment_made,
        resources: resources.payment_made,
        shouldRender: isStore,
      },
      {
        status: EOrderStatusValue.preparing,
        resources: resources.preparing,
        shouldRender: isStore,
      },
      {
        status: EOrderStatusValue.rejected,
        resources: resources.rejected,
        shouldRender: isStore || isSales,
      },
      {
        status: EOrderStatusValue.waiting_for_the_delivery_person,
        resources: resources.waiting_for_the_delivery_person,
        shouldRender: isStore || !isSales,
      },
      {
        status: EOrderStatusValue.on_route_to_store,
        resources: resources.on_route_to_store,
        shouldRender: !isSales,
      },
      {
        status: EOrderStatusValue.on_delivery_route,
        resources: resources.on_delivery_route,
        shouldRender: !isSales,
      },
      {
        status: EOrderStatusValue.canceled_delivery,
        resources: resources.canceled_delivery,
        shouldRender: !isSales,
      },
      {
        status: EOrderStatusValue.canceled,
        resources: resources.canceled,
        shouldRender: isSales,
      },
      {
        status: EOrderStatusValue.delivered,
        resources: resources.delivered,
        shouldRender: true,
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  return (
    <Menu
      placement="top"
      trigger={triggerProps => {
        return (
          <Pressable
            accessibilityLabel={label.status_menu}
            {...triggerProps}
            {...styles.pressableMenu}>
            <HStack {...styles.menuContainer}>
              <Filter {...styles.menuIcons.style} />
              <Text {...styles.textMenu}>
                {getStatusTypeEnumValue(selectedStatus)}
              </Text>

              <ArrowDropdown {...styles.menuIconDrop.style} />
            </HStack>
          </Pressable>
        );
      }}
      {...styles.menu}>
      {statusItems.map(item => {
        return item.shouldRender ? (
          <Menu.Item
            key={item.resources}
            onPress={() => handleStatusChanges(item.status)}
            backgroundColor={
              selectedStatus === item.status
                ? ThemesApp.getTheme().colors.menuItemOrderStatusFilterBg
                : ThemesApp.getTheme().colors.cardList
            }>
            <HStack {...styles.menuItemBox}>
              <Text {...styles.textMenu}>{item.resources}</Text>
            </HStack>
          </Menu.Item>
        ) : null;
      })}
    </Menu>
  );
};

export default OrderStatusFilter;
