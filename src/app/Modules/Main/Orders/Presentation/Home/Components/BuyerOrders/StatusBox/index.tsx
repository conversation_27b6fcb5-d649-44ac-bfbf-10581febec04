import { Box, Text } from "native-base";
import React from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";

import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/BuyerOrders/StatusBox/styles";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

type Props = {
  status: EOrderStatusValue;
};

const StatusBox: React.FC<Props> = ({ status }) => {
  const styles = customStyles();
  const { orders: resourcesOrders } = useTranslation();

  const bgColor =
    EOrderStatusValue[status].includes(EOrderStatusValue.canceled) ||
    EOrderStatusValue[status].includes(EOrderStatusValue.rejected)
      ? ThemesApp.getTheme().colors.deleteAccountText
      : EOrderStatusValue[status] === EOrderStatusValue.delivered
      ? ThemesApp.getTheme().colors.success[500]
      : ThemesApp.getTheme().colors.badgeStatus;

  return (
    <Box backgroundColor={bgColor} {...styles.container}>
      <Text {...styles.text}>{resourcesOrders.status[status]}</Text>
    </Box>
  );
};

export default StatusBox;
