import { IStyleProps } from "src/business/Interfaces/IStyleProps";

import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { RFValue } from "react-native-responsive-fontsize";
import { moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    productImage: {
      style: {
        width: wp("20%"),
        height: hp("20%"),
        borderRadius: RFValue(12),
        marginHorizontal: hp("1%"),
      },
    },
    pressableDetails: {
      marginTop: hp("2%"),
    },
    mainHStack: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        borderColor: ThemesApp.getTheme().colors.muted[400],
        flex: 1,
        borderRadius: moderateScale(5),
        borderWidth: moderateScale(1),
        elevation: 4,
        marginBottom: hp("2%"),
        padding: hp("1%"),
        alignItems: "center",
      },
    },
    mainVStack: {
      style: {
        flex: 1,
        margin: wp("1%"),
      },
    },
    orderText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        paddingVertical: verticalScale(6),
        fontWeight: "bold",
      },
    },
    productText: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    textTitle: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        fontWeight: "bold",
      },
    },
    text: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    statusHStack: {
      style: {
        paddingVertical: RFValue(5),
        alignItems: "center",
        marginBottom: RFValue(5),
      },
    },
    statusType: {
      style: {
        fontWeight: "bold",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },
    avatarIcon: {
      style: {
        height: moderateScale(60),
        width: moderateScale(60),
        fill: ThemesApp.getTheme().colors.muted[400],
      },
    },

    hStackInfo: {
      justifyContent: "space-between",
      alignItems: "center",
    },

    avatarIconBox: {
      style: {
        overflow: "hidden",
        height: hp("20%"),
        width: wp("20%"),
        borderRadius: RFValue(12),
        marginHorizontal: hp("1%"),
        backgroundColor: ThemesApp.getTheme().colors.muted[200],
        alignItems: "center",
        justifyContent: "center",
      },
    },

    tracking: {
      style: {
        marginTop: verticalScale(10),
      },
    },

    textButton: {
      style: {
        color: ThemesApp.getTheme().colors.white,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    priceDateText: {
      style: {
        color: ThemesApp.getTheme().colors.priceDateText,
        fontSize: RFValue(10),
        lineHeight: RFValue(12),
        textAlign: "right",
      },
    },

    hStackOrder: {
      style: {
        justifyContent: "space-between",
        alignItems: "center",
      },
    },

    hStackProductName: {
      justifyContent: "space-between",
    },

    textMoreDetails: {
      style: {
        color: ThemesApp.getTheme().colors.white,
      },
    },
  };
};

export default customStyles;
