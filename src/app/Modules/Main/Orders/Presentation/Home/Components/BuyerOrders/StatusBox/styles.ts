import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        alignItems: "center",
        paddingVertical: verticalScale(2),
      },
    },
    text: {
      style: {
        color: ThemesApp.getTheme().colors.textOrderStatus,
        fontSize: RFValue(11),
        lineHeight: RFValue(15),
        fontWeight: "bold",
        textShadowColor: "#000",
        textShadowRadius: 1,
        textShadowOffset: {
          height: 1,
          width: 1,
        },
      },
    },
  };
};

export default customStyles;
