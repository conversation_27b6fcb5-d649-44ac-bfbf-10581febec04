import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {cloneDeep, uniqueId} from "lodash";
import {
  Badge,
  Box,
  Button,
  Divider,
  HStack,
  Pressable,
  Text,
  VStack,
} from "native-base";
import React, {memo, useEffect, useState} from "react";
import PopupMap from "src/app/Components/PopupMap";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/Components/DeliverymanOrders/OrderListItem/styles";
import {loadDeliverymanLocation} from "src/app/Modules/Main/Orders/Presentation/TrackingOrder/Utils/loadDeliverymanLocation";
import getDateByLocale from "src/app/Utils/GetDateByLocale";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useOrder from "src/app/Zustand/Store/useOrder";
import EOrderStatusValue from "src/business/Enums/Models/EOrderStatusValue";
import {TrackingOrderList} from "src/business/Models/List/TrackingOrder";
import {
  StoreOrderList,
  StoreOrderListOrderItem,
} from "src/business/Models/Order/PagedOrder";

interface IProps {
  data: StoreOrderList;
}

const OrderListItemRender = ({data}: IProps) => {
  const styles = customStyles();
  const {
    orders: resources,
    generic: {button: genericResources},
  } = useTranslation();
  const [mapPopupVisibility, setMapPopupVisibility] = useState(false);
  const viewType: "client" | "store" =
    data.lastStatus.value === EOrderStatusValue.on_route_to_store
      ? "store"
      : "client";
  const [location, setLocation] = useState<TrackingOrderList>();

  const {setShowStoreRoute, setCurrentStatus} = useOrder();

  const organizeProducts = () => {
    const deepCopy = cloneDeep(data.orderItem);
    const noDuplicates = deepCopy.reduce((acc, item) => {
      const foundItemIndex = acc.findIndex(
        obj => obj.productName === item.productName,
      );
      if (foundItemIndex < 0) acc.push(item);
      else acc[foundItemIndex].quantity += item.quantity;
      return acc;
    }, [] as StoreOrderListOrderItem[]);
    return noDuplicates;
  };

  const handleOpenStatusDetails = () => {
    rootNavigation("MainApp", {
      screen: "OrderStatusDetails",
      params: {orderId: data.id},
    });
  };

  const handleOpenMap = () => {
    if (data.lastStatus.value === EOrderStatusValue.on_route_to_store) {
      setShowStoreRoute(true);
      if (location) {
        setMapPopupVisibility(true);
      }
    } else if (data.lastStatus.value === EOrderStatusValue.on_delivery_route) {
      setCurrentStatus(EOrderStatusValue.on_delivery_route);
      if (location) {
        setMapPopupVisibility(true);
      }
    }
  };

  const statusColor = (status: EOrderStatusValue) => {
    return status === EOrderStatusValue.placed_order ||
      status === EOrderStatusValue.payment_made ||
      status === EOrderStatusValue.delivered
      ? ThemesApp.getTheme().colors.iconContainerOwnProfile
      : status === EOrderStatusValue.canceled ||
        status === EOrderStatusValue.rejected ||
        status === EOrderStatusValue.canceled_delivery
      ? ThemesApp.getTheme().colors.heartIcon
      : ThemesApp.getTheme().colors.iconHelp;
  };

  useEffect(() => {
    if (
      data.lastStatus.value !==
      EOrderStatusValue.waiting_for_the_delivery_person
    ) {
      (async () => {
        const response = await loadDeliverymanLocation(data.id);
        setLocation(response);
      })();
    }
  }, [data.id, data.lastStatus.value]);

  return (
    <HStack {...styles.mainHStack}>
      <VStack {...styles.mainVStack}>
        <HStack {...styles.hStackOrder}>
          <Text ellipsizeMode="tail" numberOfLines={2} {...styles.productText}>
            {resources.label.orderNumber}
            {data.code}
          </Text>

          <Text {...styles.priceDateText}>
            {getDateByLocale({date: data.createdAt})}
          </Text>
        </HStack>

        <Box my={3}>
          {organizeProducts().map(item => (
            <Box key={uniqueId()}>
              <HStack {...styles.hStackProductName}>
                <Text
                  ellipsizeMode="tail"
                  numberOfLines={2}
                  {...styles.textTitle}>
                  {item.productName}
                </Text>

                <Text {...styles.text}>x{item.quantity}</Text>
              </HStack>
              <Divider />
            </Box>
          ))}
        </Box>

        <HStack {...styles.hStackInfo}>
          <Text {...styles.text}>{resources.label.shipping}</Text>
          <Text {...styles.text}>
            {
              maskField({
                value: data.shippingPrice,
                mask: BRL_CURRENCY_DECIMAL(10),
              }).masked
            }
          </Text>
        </HStack>

        <HStack {...styles.hStackInfo}>
          <Text {...styles.text}>{resources.label.total_price}</Text>
          <Text {...styles.text}>
            {
              maskField({
                value: data.totalPrice,
                mask: BRL_CURRENCY_DECIMAL(10),
              }).masked
            }
          </Text>
        </HStack>

        <HStack mt={3} {...styles.hStackInfo}>
          <Text bold {...styles.text}>
            {resources.label.status_steps}
          </Text>
          <Text
            ellipsizeMode="tail"
            numberOfLines={2}
            color={statusColor(data.lastStatus.value)}
            {...styles.statusType}>
            {resources.status[data.lastStatus.value]}
          </Text>
        </HStack>

        <Pressable
          onPress={handleOpenStatusDetails}
          _pressed={{opacity: 0.5}}
          {...styles.pressableDetails}>
          <Badge
            variant="solid"
            _text={{...styles.textMoreDetails}}
            colorScheme="blueGray">
            {genericResources.moreDetails}
          </Badge>
        </Pressable>

        {data.lastStatus.value === EOrderStatusValue.on_delivery_route ||
        data.lastStatus.value === EOrderStatusValue.on_route_to_store ? (
          <Button
            {...styles.tracking}
            onPress={handleOpenMap}
            _text={{...styles.textButton}}>
            {data.lastStatus.value === EOrderStatusValue.on_route_to_store
              ? resources.placeholder.open_map
              : resources.placeholder.continue_tracking}
          </Button>
        ) : null}
      </VStack>
      {location ? (
        <PopupMap
          visibility={mapPopupVisibility}
          onAppPressed={() => setMapPopupVisibility(false)}
          onCancelPressed={() => setMapPopupVisibility(false)}
          setIsVisible={visi => setMapPopupVisibility(visi)}
          latitude={
            viewType === "client"
              ? location.client.latitude
              : location.store.latitude
          }
          longitude={
            viewType === "client"
              ? location.client.longitude
              : location.store.longitude
          }
        />
      ) : null}
    </HStack>
  );
};

const OrderListItem = memo(OrderListItemRender, (prevProps, nextProps) => {
  return (
    prevProps.data.id === nextProps.data.id &&
    prevProps.data.lastStatus === nextProps.data.lastStatus
  );
});

export default OrderListItem;
