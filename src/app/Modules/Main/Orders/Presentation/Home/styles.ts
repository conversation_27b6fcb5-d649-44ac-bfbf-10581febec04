import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {verticalScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.background,
      },
    },
    mainContent: {
      style: {
        marginBottom: verticalScale(60),
        marginTop: verticalScale(8),
      },
    },

    loadingContainer: {
      justifyContent: "center",
      alignItems: "center",
      height: "100%",
    },
  };
};

export default customStyles;
