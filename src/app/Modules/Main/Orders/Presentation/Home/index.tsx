import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {Box, Spinner, VStack} from "native-base";
import React, {useCallback, useEffect, useLayoutEffect} from "react";
import DrawerHeader from "src/app/Components/Header/DrawerHeader";
import StackHeader from "src/app/Components/Header/StackHeader";
import BuyerOrders from "src/app/Modules/Main/Orders/Presentation/Home/Components/BuyerOrders";
import DeliveryManOrders from "src/app/Modules/Main/Orders/Presentation/Home/Components/DeliverymanOrders";
import ShopkeeperOrders from "src/app/Modules/Main/Orders/Presentation/Home/Components/ShopkeeperOrders";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Home/styles";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import useLoggedUser from "src/app/Modules/Main/Users/<USER>/useLoggedUser";
import saveProfileOption from "src/app/Utils/SaveProfileOption";
import useGeneralSettings from "src/app/Zustand/Store/useGeneralSettings";
import useUser from "src/app/Zustand/Store/useUser";
import {UserOrders} from "src/business/Models/List/UserOrders";
import {EProfile} from "src/business/Models/Profile";

type Props = NativeStackScreenProps<MainAppStackParamList, "OrdersHome">;

export type IPagedUserOrders = {
  result: UserOrders[];
  totalCount: number;
  totalPages: number;
};

const OrdersHome: React.FC<Props> = ({navigation, route}) => {
  const styles = customStyles();
  const {getLoggerUser} = useLoggedUser();
  const {selectedProfile} = useGeneralSettings();
  const {user, userProfiles} = useUser();

  const checkUserProfiles = useCallback(() => {
    if (userProfiles && userProfiles.length === 1 && !selectedProfile) {
      saveProfileOption(EProfile.client);
    }
  }, [userProfiles, selectedProfile]);

  useLayoutEffect(() => {
    checkUserProfiles();
  }, [checkUserProfiles]);

  useEffect(() => {
    if (!user && route.params?.shouldLoadUserData) {
      getLoggerUser();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [route.params?.shouldLoadUserData, user]);

  const handleUserComponent = () => {
    if (selectedProfile === EProfile.client) return <BuyerOrders />;
    if (selectedProfile === EProfile.shopkeeper) {
      return (
        <ShopkeeperOrders
          orderStatusFilter={route.params?.shopkeeperOrderStatusFilter}
          refresh={route.params?.refresh}
        />
      );
    }
    if (selectedProfile === EProfile.deliveryman) {
      return (
        <DeliveryManOrders
          orderStatusFilter={route.params?.deliverymanOrderStatusFilter}
          refresh={route.params?.refresh}
        />
      );
    }
  };

  return user ? (
    <VStack {...styles.container}>
      {selectedProfile === EProfile.client ? (
        <StackHeader navigation={navigation} route={route} />
      ) : (
        <DrawerHeader navigation={navigation} route={route} />
      )}
      {handleUserComponent()}
    </VStack>
  ) : (
    <Box {...styles.loadingContainer}>
      <Spinner size="sm" />
    </Box>
  );
};

export default OrdersHome;
