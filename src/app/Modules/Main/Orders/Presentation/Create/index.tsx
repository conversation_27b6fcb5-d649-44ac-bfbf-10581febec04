import {useIsFocused} from "@react-navigation/native";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {isArray} from "lodash";
import {
  Divider,
  HStack,
  ScrollView,
  Spinner,
  Text,
  Toast,
  View,
  VStack,
} from "native-base";
import React, {useCallback, useEffect, useState} from "react";
import {Image, TextInput} from "react-native";
import Gallery from "src/app/Components/Gallery";
import StackHeader from "src/app/Components/Header/StackHeader";
import ModalSimple from "src/app/Components/ModalSimple";
import useTranslation from "src/app/Hooks/useTranslation";
import OrderAttributes from "src/app/Modules/Main/Orders/Presentation/Create/Components/OrderAttributes";
import OrdersCreateDetails from "src/app/Modules/Main/Orders/Presentation/Create/Components/OrderCreateDetails";
import OrderFooter from "src/app/Modules/Main/Orders/Presentation/Create/Components/OrderFooter";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Create/styles";
import CreateOrderUtils from "src/app/Modules/Main/Orders/Presentation/Create/Utils/OrderUtils";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import {rootGoBack} from "src/app/Utils/RootNavigation";
import {MessageOutline} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {
  AttributeList,
  AttributeOptionList,
} from "src/business/DTOs/AttributeList";
import {AttributeSectionList} from "src/business/DTOs/AttributeSectionList";
import {IAttributeService} from "src/business/Interfaces/Services/IAttribute";
import {IFileService} from "src/business/Interfaces/Services/IFile";
import AppError from "src/business/Tools/AppError";

export type Props = NativeStackScreenProps<
  MainAppStackParamList,
  "OrdersCreate"
>;

const OrdersCreate: React.FC<Props> = ({navigation, route}) => {
  const styles = customStyles();
  const {id, data, storeReview, isUpdate} = route.params;

  const [orderAttributes, setOrderAttributes] = useState<
    AttributeSectionList[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [openGallery, setOpenGallery] = useState(false);
  const [orderInfo, setOrderInfo] = useState(data.orderItem?.observation);

  const attributeService = container.get<IAttributeService>(
    TOKENS.AttributeService,
  );

  const createOrder = container.get<CreateOrderUtils>(TOKENS.CreateOrderUtils);
  const fileService = container.get<IFileService>(TOKENS.FileService);
  const isFocused = useIsFocused();

  const {
    orders: resources,
    imagePicker: {
      button: {gallery},
    },
  } = useTranslation();

  const openGalleryModal = () => {
    setOpenGallery(!openGallery);
  };

  const loadAttributes = useCallback(async () => {
    const attributeResponse =
      await attributeService.getWithProductAttributeOption(id);

    if (attributeResponse instanceof AppError) {
      handleError(attributeResponse);
    } else {
      if (data.orderItem.orderId) {
        const selectedAttributes = createOrder.setInitialOrderAttributes(
          data.orderItem.attributes!,
          attributeResponse,
        );
        setOrderAttributes(selectedAttributes);
      } else {
        setOrderAttributes(attributeResponse);
      }
    }
    setIsLoading(false);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [attributeService, fileService, id]);

  useEffect(() => {
    setIsLoading(true);
    loadAttributes();
    isUpdate
      ? setOrderInfo(data.orderItem?.observation)
      : setOrderInfo(undefined);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFocused, id]);

  const handleSelectOption = (
    attributeOption: AttributeOptionList,
    attribute: AttributeList,
  ) => {
    const optionsChecked = createOrder.handleSelectOption(
      attributeOption,
      attribute,
      orderAttributes,
    );
    setOrderAttributes(optionsChecked);
  };

  const handleError = (error: any) => {
    console.log(error);
    Toast.show({
      description: resources.toasts.create_error,
      bgColor: "secondary.400",
    });
    rootGoBack();
  };

  const {files, icon} = data.orderProduct;
  const photos = icon ? [icon] : isArray(files) ? files : [];

  return (
    <>
      <StackHeader navigation={navigation} route={route} />
      <ScrollView>
        <Image source={{uri: files?.[0]?.url}} />
        <OrdersCreateDetails
          data={data}
          storeReview={storeReview}
          openGalleryModal={openGalleryModal}
        />

        {orderAttributes.length > 0 ? (
          <OrderAttributes
            orderAttributes={orderAttributes}
            handleSelectOption={handleSelectOption}
          />
        ) : (
          isLoading && <Spinner size="sm" />
        )}

        <View>
          <Divider {...styles.dividerFooter} />
          <VStack {...styles.mainVStackFooter}>
            <HStack space={2} alignItems="center" {...styles.hStackObsevation}>
              <MessageOutline {...styles.observationIcon.style} />
              <Text {...styles.textObservation}>
                {resources.label.observation}
              </Text>
            </HStack>
            <TextInput
              placeholderTextColor={styles.textArea.style?.color}
              placeholder={resources.placeholder.observation}
              value={orderInfo}
              onChangeText={text => {
                setOrderInfo(text);
              }}
              maxLength={140}
              // keyboardType="ascii-capable"
              multiline
              style={styles.textArea.style}
            />
          </VStack>
        </View>
      </ScrollView>
      <OrderFooter
        initialOrder={{...data.orderItem, observation: orderInfo}}
        orderAttributes={orderAttributes}
        loading={isLoading}
        product={data.orderProduct}
        photoUrl={photos?.[0] ? photos[0].url : undefined}
        isUpdate={isUpdate}
        storeId={data.orderProduct.storeId}
      />

      <ModalSimple
        titleHeader={gallery}
        visibility={openGallery}
        onClose={openGalleryModal}>
        <View {...styles.modalContainer}>
          <Gallery photos={photos} showCarousel={false} />
        </View>
      </ModalSimple>
    </>
  );
};

export default OrdersCreate;
