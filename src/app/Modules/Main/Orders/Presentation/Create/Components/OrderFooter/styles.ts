import { RFValue } from "react-native-responsive-fontsize";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";
import { moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    mainVStack: {
      style: {
        paddingVertical: "2%",
        paddingHorizontal: "5%",
      },
    },

    hStackObsevation: {
      alignItems: "center",
      style: {
        marginBottom: verticalScale(10),
      },
    },

    textObservation: {
      style: {
        textAlign: "center",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    observationIcon: {
      style: {
        width: moderateScale(22),
        height: moderateScale(22),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },

    divider: {
      style: {
        height: verticalScale(2),
        marginVertical: verticalScale(8),
      },
    },

    footer: {
      style: {
        borderColor: ThemesApp.getTheme().colors.muted[300],
        borderTopWidth: moderateScale(2),
        alignItems: "center",
        justifyContent: "space-between",
        paddingHorizontal: "5%",
        paddingVertical: "2%",
      },
    },

    footerAddProduct: {
      px: "3%",
      py: "3%",
      w: "65%",
      flexDirection: "row",
      justifyContent: "space-between",
      style: {
        borderRadius: moderateScale(5),
        backgroundColor: ThemesApp.getTheme().colors.primary[400],
      },
    },
    footerTextAddAddProduct: {
      style: {
        fontSize: RFValue(14),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.white,
      },
    },

    footerDecreaseAmount: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
      },
    },

    footerIncreaseAmount: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
      },
    },

    footerTextAmount: {
      fontSize: RFValue(14),
    },

    footerAmountStack: {
      w: "30%",
      style: {
        alignItems: "center",
        justifyContent: "space-between",
      },
    },

    pressableDecreaseAmount: {
      style: {
        paddingRight: "12%",
      },
    },

    pressableIncreaseAmount: {
      style: {
        paddingLeft: "12%",
      },
    },

    textArea: {
      height: "10%",
      style: {
        fontSize: RFValue(16),
      },
    },
    toastSuccess: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.green[600], // green.600 or success.600
      },
    },
  };
};

export default customStyles;
