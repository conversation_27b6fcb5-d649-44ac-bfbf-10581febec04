import {RFValue} from "react-native-responsive-fontsize";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {horizontalScale, moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainVStack: {
      style: {
        paddingVertical: hp("2%"),
        paddingHorizontal: wp("5%"),
      },
    },
    hStackProductName: {
      style: {
        justifyContent: "space-between",
      },
    },
    productName: {
      style: {
        fontSize: RFValue(16),
        fontWeight: "bold",
        textTransform: "uppercase",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    productDescription: {
      style: {
        flexWrap: "wrap",
        marginVertical: "3%",
        fontSize: RFValue(14),
        lineHeight: RFValue(18),
        textAlign: "justify",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    viewDetails: {
      justifyContent: "space-between",
      alignItems: "flex-end",
    },
    viewPhoto: {
      style: {
        borderColor: ThemesApp.getTheme().colors.arrowDropdown,
        backgroundColor: ThemesApp.getTheme().colors.muted[400],
        alignItems: "center",
        justifyContent: "center",
        borderRadius: moderateScale(5),
        borderWidth: moderateScale(1),
      },
    },
    productImage: {
      style: {
        width: "100%",
        height: "100%",
        borderRadius: moderateScale(5),
      },
    },
    productIcon: {
      style: {
        width: moderateScale(45),
        height: moderateScale(45),
        fill: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },
    textDetailsButton: {
      style: {
        color: ThemesApp.getTheme().colors.iconHelp,
        fontSize: RFValue(11),
      },
    },
    buttonDetails: {
      p: 0,
      my: hp("1%"),
      alignItems: "center",
    },
    boxStoreDetails: {
      style: {
        justifyContent: "space-between",
        borderRadius: moderateScale(5),
        padding: "3%",
        borderWidth: moderateScale(1),
        borderColor: ThemesApp.getTheme().colors.lineGray,
      },
    },
    hStackStoreDetails: {
      style: {
        justifyContent: "space-between",
      },
    },
    hStackBoxStore: {
      alignItems: "center",
      justifyContent: "flex-start",
    },
    storeIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },
    textDetails: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(18),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
    starIcon: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        marginLeft: horizontalScale(5),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },
    textDetailsStar: {
      style: {
        fontSize: RFValue(12),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.yellow[500],
      },
    },
    starIconVoid: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        marginLeft: horizontalScale(5),
        fill: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },
    textDetailsStarVoid: {
      style: {
        textAlign: "left",
        flexWrap: "wrap",
        fontSize: RFValue(10),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },
  };
};

export default customStyles;
