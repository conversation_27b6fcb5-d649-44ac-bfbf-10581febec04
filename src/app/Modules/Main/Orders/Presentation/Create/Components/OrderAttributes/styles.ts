import {IStyleProps} from "src/business/Interfaces/IStyleProps";
import {RFValue} from "react-native-responsive-fontsize";
import {
  horizontalScale,
  moderateScale,
  verticalScale,
} from "src/app/Utils/Metrics";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";

const customStyles = (): IStyleProps => {
  return {
    sectionList: {
      style: {
        marginRight: "2%",
      },
    },
    mainVStack: {
      style: {
        paddingVertical: hp("2%"),
        paddingHorizontal: wp("5%"),
      },
    },
    divider: {
      style: {
        height: verticalScale(2),
        marginVertical: verticalScale(8),
      },
    },

    hStackStoreDetails: {
      style: {
        justifyContent: "space-between",
      },
    },

    boxStoreDetails: {
      style: {
        justifyContent: "space-between",
        borderRadius: moderateScale(5),
        padding: "3%",
        borderWidth: moderateScale(1),
        borderColor: ThemesApp.getTheme().colors.lineGray,
      },
    },

    textDetails: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(18),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    hStackDelivery: {
      style: {
        justifyContent: "space-between",
      },
    },

    deliveryIcon: {
      style: {
        width: moderateScale(22),
        height: moderateScale(22),
        fill: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },

    starIcon: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        marginLeft: horizontalScale(5),
        fill: ThemesApp.getTheme().colors.yellow[500],
      },
    },

    starIconVoid: {
      style: {
        width: moderateScale(18),
        height: moderateScale(18),
        marginLeft: horizontalScale(5),
        fill: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },

    storeIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },

    textDetailsStar: {
      style: {
        fontSize: RFValue(12),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.yellow[500],
      },
    },

    textDetailsStarVoid: {
      style: {
        textAlign: "left",
        flexWrap: "wrap",
        fontSize: RFValue(10),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },

    deliveryPrice: {
      style: {
        fontSize: RFValue(10),
        fontWeight: "bold",
        textTransform: "uppercase",
        color: ThemesApp.getTheme().colors.white,
      },
    },

    requiredText: {
      style: {
        fontSize: RFValue(10),
        fontWeight: "bold",
        textTransform: "uppercase",
        color: ThemesApp.getTheme().colors.white,
      },
    },

    productName: {
      style: {
        fontSize: RFValue(16),
        fontWeight: "bold",
        textTransform: "uppercase",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    productDescription: {
      style: {
        flexWrap: "wrap",
        marginVertical: "3%",
        fontSize: RFValue(14),
        lineHeight: RFValue(18),
        textAlign: "justify",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    hStackAttributeHeader: {
      style: {
        width: "100%",
        justifyContent: "space-between",
        paddingLeft: "5%",
      },
    },

    textOneOption: {
      style: {
        fontSize: RFValue(15),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    toastSuccess: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.green[600],
      },
    },

    attributeHeader: {
      flex: 1,

      style: {
        backgroundColor: ThemesApp.getTheme().colors.gray[200],
        margin: wp("3%"),
        paddingVertical: wp("2%"),
        alignItems: "center",
        justifyContent: "space-between",
      },
    },

    viewRequired: {
      marginX: moderateScale(20),

    },

    textHeader: {
      style: {
        fontWeight: "bold",
        marginBottom: "2%",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    textRequiredHeader: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(16),
        fontWeight: "bold",
      },
    },

    boxRequiredAttribute: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.error,
        alignSelf: "flex-end",
        justifyContent: "center",
        alignItems: "center",
        borderRadius: moderateScale(5),
        height: verticalScale(30),
        width: verticalScale(120),
      },
    },

    attributeVStack: {
      style: {
        marginRight: "2%",
        width: "72%",
      },
    },

    attributeOption: {
      style: {
        padding: "1%",
        justifyContent: "space-between",
      },
    },

    attributeOptionAmount: {
      style: {
        alignItems: "center",
        justifyContent: "space-between",
        width: "20%",
        padding: "1%",
        borderRadius: moderateScale(5),
      },
    },

    infoIcon: {
      style: {
        fill: ThemesApp.getTheme().colors.iconHelp,
        width: moderateScale(19),
        height: moderateScale(19),
        marginLeft: wp("3%"),
      },
    },

    hStackProductName: {
      style: {
        justifyContent: "space-between",
      },
    },

    modalContainer: {
      style: {
        paddingHorizontal: wp("2%"),
        justifyContent: "center",
        alignItems: "center",
      },
    },

    textDetailsButton: {
      style: {
        color: ThemesApp.getTheme().colors.iconHelp,
        fontSize: RFValue(11),
      },
    },

    buttonDetails: {
      p: 0,
      my: hp("1%"),
      alignItems: "center",
    },

    viewDetails: {
      justifyContent: "space-between",
      alignItems: "flex-end",
    },

    productImage: {
      style: {
        width: "100%",
        height: "100%",
        borderRadius: moderateScale(5),
      },
    },

    hStackBoxStore: {
      alignItems: "center",
      justifyContent: "flex-start",
    },

    mainVStackFooter: {
      style: {
        paddingVertical: hp("3%"),
        paddingHorizontal: wp("5%"),
      },
    },

    hStackObsevation: {
      alignItems: "center",
      style: {
        marginBottom: verticalScale(10),
      },
    },

    textObservation: {
      style: {
        textAlign: "center",
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    observationIcon: {
      style: {
        width: moderateScale(22),
        height: moderateScale(22),
        fill: ThemesApp.getTheme().colors.heartIcon,
      },
    },

    dividerFooter: {
      style: {
        marginVertical: verticalScale(8),
      },
    },

    textArea: {
      style: {
        paddingHorizontal: horizontalScale(10),
        fontSize: RFValue(12),
        textAlignVertical: "top",
        borderColor: ThemesApp.getTheme().colors.muted[400],
        color: ThemesApp.getTheme().colors.textColor,
        borderWidth: 1,
      },
    },
    productIcon: {
      style: {
        width: moderateScale(45),
        height: moderateScale(45),
        fill: ThemesApp.getTheme().colors.arrowDropdown,
      },
    },

    viewPhoto: {
      style: {
        borderColor: ThemesApp.getTheme().colors.arrowDropdown,
        backgroundColor: ThemesApp.getTheme().colors.muted[400],
        alignItems: "center",
        justifyContent: "center",
        borderRadius: moderateScale(5),
        borderWidth: moderateScale(1),
      },
    },
  };
};

export default customStyles;
