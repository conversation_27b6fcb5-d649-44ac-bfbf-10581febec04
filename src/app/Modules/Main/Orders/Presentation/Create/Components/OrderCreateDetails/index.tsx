import {isArray} from "lodash";
import {Box, Button, HStack, Pressable, Text, View, VStack} from "native-base";
import React from "react";
import {Dimensions, Image, ScaledSize} from "react-native";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Create/Components/OrderCreateDetails/styles";
import CapitalizeTitleLetter from "src/app/Utils/CapitalizeTitleLetter";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {
  ProductPhoto,
  Star,
  StarOutline,
  Store,
} from "src/assets/Icons/Flaticon";
import {CartList} from "src/business/Models/List/CartList";

export type Props = {
  data: CartList;
  storeReview?: number;
  openGalleryModal: () => void;
};

const OrdersCreateDetails = ({data, storeReview, openGalleryModal}: Props) => {
  const styles = customStyles();
  const window: ScaledSize = Dimensions.get("window");

  const widthBiggerPhoto = window.width;
  const heightBiggerPhoto = widthBiggerPhoto;
  const widthSmallPhoto = Math.floor(widthBiggerPhoto * 0.25);
  const heightSmallPhoto = Math.floor(heightBiggerPhoto * 0.25);

  const {
    orders: resources,
    generic: {button: genericResources},
  } = useTranslation();

  const navigateToProductDetails = () => {
    rootNavigation("MainApp", {
      screen: "ProductDetails",
      params: {id: data.orderProduct.id},
    });
  };

  const {files, icon} = data.orderProduct;
  const photos = icon ? [icon] : isArray(files) ? files : [];

  return (
    <VStack {...styles.mainVStack}>
      <HStack flex={1} space={4} {...styles.hStackProductName}>
        <View flex={1}>
          <Text ellipsizeMode="tail" numberOfLines={2} {...styles.productName}>
            {data.orderProduct.name}
          </Text>
          <Text
            ellipsizeMode="tail"
            numberOfLines={3}
            android_hyphenationFrequency="full"
            {...styles.productDescription}>
            {data.orderProduct.shortDescription}
          </Text>
        </View>

        <View {...styles.viewDetails}>
          {photos.length > 0 ? (
            <Pressable
              onPress={openGalleryModal}
              _pressed={{opacity: 0.8}}
              style={{
                width: widthSmallPhoto,
                height: heightSmallPhoto,
                ...styles.viewPhoto.style,
              }}>
              <Image
                source={{uri: photos[0].url}}
                resizeMode="cover"
                style={{...styles.productImage.style}}
              />
            </Pressable>
          ) : (
            <View
              style={{
                width: widthSmallPhoto,
                height: heightSmallPhoto,
                ...styles.viewPhoto.style,
              }}>
              <ProductPhoto {...styles.productIcon.style} />
            </View>
          )}

          <Button
            variant="unstyled"
            _text={{
              style: styles.textDetailsButton.style,
            }}
            _pressed={{opacity: 0.8}}
            onPress={navigateToProductDetails}
            {...styles.buttonDetails}>
            {genericResources.moreDetails}
          </Button>
        </View>
      </HStack>
      <Box {...styles.boxStoreDetails}>
        <HStack {...styles.hStackStoreDetails}>
          <HStack flex={1} space={2} {...styles.hStackBoxStore}>
            <Store {...styles.storeIcon.style} />
            {data?.orderProduct?.storeName ? (
              <Text
                flex={1}
                ellipsizeMode="tail"
                numberOfLines={2}
                {...styles.textDetails}>
                {CapitalizeTitleLetter(data?.orderProduct?.storeName)}
              </Text>
            ) : null}
          </HStack>
          <View>
            {storeReview ? (
              <HStack space={1} {...styles.hStackBoxStore}>
                <Star {...styles.starIcon.style} />
                <Text {...styles.textDetailsStar}>{storeReview}</Text>
              </HStack>
            ) : (
              <HStack space={1} {...styles.hStackBoxStore}>
                <StarOutline {...styles.starIconVoid.style} />
                <Text {...styles.textDetailsStarVoid}>
                  {resources.label.noReview}
                </Text>
              </HStack>
            )}
          </View>
        </HStack>
      </Box>
    </VStack>
  );
};

export default OrdersCreateDetails;
