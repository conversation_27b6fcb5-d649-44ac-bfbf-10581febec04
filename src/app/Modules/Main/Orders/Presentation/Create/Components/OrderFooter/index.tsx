import {BRL_CURRENCY_DECIMAL, maskField} from "input-mask-native-base-rhf";
import {now} from "lodash";
import {HStack, Pressable, Text} from "native-base";
import React, {useEffect, useState} from "react";
import EBottomTabItems from "src/app/Components/BottomTab/EBottomTabItems";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import showToastWarning from "src/app/Components/Toast/toastWarning";
import VerifyEmailModal from "src/app/Components/VerificationEmailModal";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import useTranslation from "src/app/Hooks/useTranslation";
import HandleOrders from "src/app/Modules/Main/Orders/Presentation/Cart/Utils/handleOrders";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Create/Components/OrderFooter/styles";
import CreateOrderUtils from "src/app/Modules/Main/Orders/Presentation/Create/Utils/OrderUtils";
import useGetStoreAvailability from "src/app/Modules/Main/Stores/Query/useGetStoreAvailability";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import useBottomTabs from "src/app/Zustand/Store/useBottomTabs";
import useOrder from "src/app/Zustand/Store/useOrder";
import usePendingData from "src/app/Zustand/Store/usePendingData";
import {Minus, Plus} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {AttributeSectionList} from "src/business/DTOs/AttributeSectionList";
import {CartList} from "src/business/Models/List/CartList";
import {OrderItem} from "src/business/Models/OrderItem";
import {OrderProduct} from "src/business/Models/OrderProduct";

interface IProps {
  orderAttributes: AttributeSectionList[];
  loading: boolean;
  initialOrder: OrderItem;
  product: OrderProduct;
  photoUrl?: string;
  isUpdate?: boolean;
  storeId: string;
}

const OrderFooter = ({
  orderAttributes,
  loading,
  initialOrder,
  product,
  photoUrl,
  isUpdate,
  storeId,
}: IProps) => {
  const styles = customStyles();
  const createOrder = container.get<CreateOrderUtils>(TOKENS.CreateOrderUtils);
  const [orderInfo, setOrderInfo] = useState({
    quantity: initialOrder?.quantity || 1,
    totalPrice: initialOrder?.totalPrice || product.salePrice,
  });

  useEffect(() => {
    if (!loading) {
      setOrderInfo({
        quantity: initialOrder?.quantity || 1,
        totalPrice: initialOrder?.totalPrice || product.salePrice,
      });
    }
  }, [
    initialOrder?.quantity,
    initialOrder?.totalPrice,
    loading,
    product.salePrice,
  ]);
  // const { orders: resources, stores: { home: resourcesHome  } = useTranslation();
  const {
    stores: {home: resourcesHome},
    orders: resources,
  } = useTranslation();
  const [openEmailVerify, setOpenEmailVerify] = useState(false);
  const {cartList, saveMultipleOrders} = useOrder();
  const {emailStatus} = usePendingData();
  const {pushTabsStack} = useBottomTabs();
  const handleOrders = container.get<HandleOrders>(TOKENS.HandleOrders);

  const handleVerifyEmailPress = () => {
    rootNavigation("MainApp", {
      screen: "VerifyEmail",
      params: {},
    });
  };

  const handleOrderObj = () => {
    const orderItemObj: OrderItem = {
      orderId: initialOrder?.orderId || now().toString(),
      attributes: createOrder.filterSelectedAttributes(orderAttributes),
      quantity: orderInfo.quantity,
      totalPrice: orderInfo.totalPrice,
      unitPrice: product.salePrice,
      observation: initialOrder.observation,
      productId: product.id,
      preparationTime: product.preparationTime,
    };

    const cartOrder: CartList = {
      orderItem: orderItemObj,
      orderProduct: {...product, photoUrl},
    };

    if (initialOrder.orderId) {
      updateOrderItem(cartOrder);
    } else {
      createOrderItem(cartOrder);
    }
  };

  const createOrderItem = (orderItemObj: CartList) => {
    const orderObj = createOrder.formatOrder(
      orderItemObj,
      cartList || [],
      product.salePrice,
    );
    saveMultipleOrders(orderObj);
    handleNavigationToCart();
  };

  const updateOrderItem = (orderObj: CartList) => {
    const updatedOrders = [...cartList!];

    updatedOrders?.splice(
      updatedOrders.findIndex(
        item => item.orderItem.orderId === orderObj.orderItem.orderId,
      ),
      1,
      orderObj,
    );

    saveMultipleOrders(updatedOrders);
    handleNavigationToCart();
  };

  const handleNavigationToCart = () => {
    rootNavigation("MainApp", {
      screen: "Cart",
    });
    showToastSuccess(resources.toasts.added_cart, {duration: 4000});
  };

  const storeAvailabilityQuery = useGetStoreAvailability(storeId, response => {
    if (response === false) {
      showToastWarning(resourcesHome.text.toleranceTimeEnd, {
        duration: 4000,
        placement: "top",
      });
    }
  });
  return (
    <HStack {...styles.footer}>
      <HStack {...styles.footerAmountStack}>
        <Pressable
          {...styles.pressableDecreaseAmount}
          onPress={() => {
            setOrderInfo(prevState => {
              return {
                ...prevState,
                quantity: prevState.quantity - 1,
                totalPrice: handleOrders.handleCalculateOrderPrice(
                  prevState.totalPrice - product.salePrice,
                ),
              };
            });
          }}
          isDisabled={orderInfo.quantity <= 1}>
          <Minus
            fill={
              orderInfo.quantity <= 1
                ? "transparent"
                : ThemesApp.getTheme().colors.secondary[400]
            }
            {...styles.footerDecreaseAmount.style}
          />
        </Pressable>
        <Text {...styles.footerTextAmount}>{orderInfo.quantity}</Text>
        <Pressable
          {...styles.pressableIncreaseAmount}
          onPress={() => {
            setOrderInfo(prevState => {
              return {
                ...prevState,
                quantity: prevState.quantity + 1,
                totalPrice: handleOrders.handleCalculateOrderPrice(
                  product.salePrice * (prevState.quantity + 1),
                ),
              };
            });
          }}
          isDisabled={orderInfo.quantity > 99}>
          <Plus
            fill={
              orderInfo.quantity > 99
                ? "transparent"
                : ThemesApp.getTheme().colors.secondary[400]
            }
            {...styles.footerIncreaseAmount.style}
          />
        </Pressable>
      </HStack>
      <Pressable
        {...styles.footerAddProduct}
        onPress={emailStatus ? handleOrderObj : () => setOpenEmailVerify(true)}
        _disabled={{opacity: 0.5}}
        _pressed={{opacity: 0.5}}
        isDisabled={
          createOrder.handleDisableAddButton(orderAttributes, loading) ||
          storeAvailabilityQuery.data === false
        }>
        <Text {...styles.footerTextAddAddProduct}>
          {isUpdate && isUpdate === true
            ? resources.button.updateOrder
            : resources.button.addOrder}
        </Text>
        <Text {...styles.footerTextAddAddProduct}>
          {
            maskField({
              value: orderInfo.totalPrice,
              mask: BRL_CURRENCY_DECIMAL(12),
            }).masked
          }
        </Text>
      </Pressable>
      {emailStatus ? null : (
        <VerifyEmailModal
          visibility={openEmailVerify}
          onClose={() => setOpenEmailVerify(false)}
          onPressContinue={() => handleVerifyEmailPress()}
        />
      )}
    </HStack>
  );
};

export default OrderFooter;
