import {<PERSON>ge, Box, HStack, View} from "native-base";
import React from "react";
import AttributeProduct from "src/app/Components/Attribute";
import AttributeOption from "src/app/Components/AttributeOption";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Orders/Presentation/Create/Components/OrderAttributes/styles";
import {
  AttributeList,
  AttributeOptionList,
} from "src/business/DTOs/AttributeList";
import {AttributeSectionList} from "src/business/DTOs/AttributeSectionList";

export type Props = {
  orderAttributes: AttributeSectionList[];
  handleSelectOption: (
    attributeOption: AttributeOptionList,
    attribute: AttributeList,
  ) => void;
};

const OrderAttributes = ({orderAttributes, handleSelectOption}: Props) => {
  const styles = customStyles();

  const {orders: resources} = useTranslation();

  return (
    <Box>
      {orderAttributes.map(item => {
        return (
          <Box key={item.attribute.id}>
            <HStack {...styles.attributeHeader}>
              <AttributeProduct data={item.attribute} onPress={() => {}} />

              <View {...styles.viewRequired}>
                {item.attribute.required && (
                  <Badge
                    variant="solid"
                    _text={{...styles.requiredText}}>
                    {resources.label.required}
                  </Badge>
                )}
              </View>
            </HStack>

            {item.data.map(option => {
              return (
                <HStack {...styles.attributeOption} key={option.id}>
                  <AttributeOption
                    title={option.value}
                    onPress={() => handleSelectOption(option, item.attribute)}
                    checked={option.checked}
                    isOrderCreate={true}
                  />
                </HStack>
              );
            })}
          </Box>
        );
      })}
    </Box>
  );
};

export default OrderAttributes;
