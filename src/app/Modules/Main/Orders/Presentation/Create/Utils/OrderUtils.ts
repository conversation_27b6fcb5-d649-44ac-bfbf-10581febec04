import {injectable} from "inversify";
import {isEqual} from "lodash";
import {
  AttributeOptionList,
  AttributeList,
} from "src/business/DTOs/AttributeList";
import {AttributeSectionList} from "src/business/DTOs/AttributeSectionList";
import EAttributeType from "src/business/Enums/Models/EAttributeType";
import {CartList} from "src/business/Models/List/CartList";

@injectable()
class CreateOrderUtils {
  handleSelectOption = (
    attributeOption: AttributeOptionList,
    attribute: AttributeList,
    orderAttributes: AttributeSectionList[],
  ) => {
    const optionsChecked = orderAttributes?.map(item => {
      if (item.attribute.id === attribute.id) {
        const data = item.data?.map(attOpt => {
          if (item.attribute.type === EAttributeType.simpleSelection) {
            attOpt.checked = false;
          }
          if (attOpt.id === attributeOption.id) {
            attOpt.checked = !attributeOption.checked;
          }
          return attOpt;
        });
        item.data = data;
      }
      return item;
    });
    return optionsChecked;
  };

  setInitialOrderAttributes = (
    selectedAttribute: AttributeList[],
    orderAttributes: AttributeSectionList[],
  ) => {
    const optionsChecked = orderAttributes?.map(item => {
      const selectedAttributeItem = selectedAttribute.find(
        attribute => item.attribute.id === attribute.id,
      );
      if (selectedAttributeItem) {
        const data = item.data?.map(attOpt => {
          const selectedAttributeOption =
            selectedAttributeItem?.attributeOption?.find(
              attributeOpt => attOpt.id === attributeOpt.id,
            );
          if (selectedAttributeOption) {
            attOpt.checked = selectedAttributeOption.checked;
          }

          return attOpt;
        });
        item.data = data;
      }
      return item;
    });

    return optionsChecked;
  };

  handleDisableAddButton = (
    orderAttributes: AttributeSectionList[],
    loading: boolean,
  ) => {
    const data = orderAttributes?.map(item => {
      if (item.attribute.required) {
        return item.data.find(option => option.checked === true);
      }
      return true;
    });
    return data.some(value => value === undefined) || loading;
  };

  filterSelectedAttributes = (orderAttributes: AttributeSectionList[]) => {
    const result: AttributeList[] = [];
    orderAttributes?.forEach(item => {
      const attributeOption = item.data.filter(
        option => option.checked === true,
      );
      if (attributeOption.length > 0) {
        const attributeList: AttributeList = {
          id: item.attribute.id,
          name: item.attribute.name,
          shortDescription: item.attribute.shortDescription,
          required: item.attribute.required,
          type: item.attribute.type,
          checked: item.attribute.checked,
          attributeOption,
        };
        result.push(attributeList);
      }
    });
    return result;
  };

  formatOrder = (
    selectedOrder: CartList,
    orders: CartList[],
    sale_price: number,
  ) => {
    const orderData = [...orders];
    const equalOrder = orders.find(
      item =>
        item.orderProduct.id === selectedOrder.orderProduct.id &&
        isEqual(
          item.orderItem.attributes,
          selectedOrder.orderItem.attributes,
        ) &&
        item.orderItem.observation === selectedOrder.orderItem.observation,
    );

    if (equalOrder) {
      selectedOrder.orderItem.quantity += equalOrder.orderItem.quantity;
      selectedOrder.orderItem.totalPrice = (sale_price *
        selectedOrder.orderItem.quantity) as unknown as number;
      orderData.splice(
        orders.findIndex(item => isEqual(item, equalOrder)),
        1,
        selectedOrder,
      );
      return orderData;
    }

    return orders.length > 0 ? orders.concat(selectedOrder) : [selectedOrder];
  };
}

export default CreateOrderUtils;
