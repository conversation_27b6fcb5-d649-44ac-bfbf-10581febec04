import {useMutation} from "@tanstack/react-query";
import handleQueryError from "src/app/Utils/HandleQueryError";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {IAddressCreate} from "src/business/DTOs/Forms/Create/BaseAddress";
import {IAddressService} from "src/business/Interfaces/Services/IAddress";

const useCreateAddress = () => {
  const addressService = container.get<IAddressService>(TOKENS.AddressService);

  const createAddressMutation = useMutation({
    mutationFn: (data: IAddressCreate) => {
      return addressService.create(data);
    },
    onError: e => handleQueryError(e),
  });

  return createAddressMutation;
};

export default useCreateAddress;
