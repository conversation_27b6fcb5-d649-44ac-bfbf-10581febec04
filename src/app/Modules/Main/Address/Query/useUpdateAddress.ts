import {useMutation} from "@tanstack/react-query";
import handleQueryError from "src/app/Utils/HandleQueryError";
import mapperAddress from "src/business/Config/Automapper/Address";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {IFormEditAddress} from "src/business/DTOs/Forms/Edit/Address";
import {IAddressEdit} from "src/business/DTOs/Forms/Edit/BaseAddress";
import {IAddressService} from "src/business/Interfaces/Services/IAddress";

const useUpdateAddress = () => {
  const addressService = container.get<IAddressService>(TOKENS.AddressService);

  const updateAddressMutation = useMutation({
    mutationFn: (data: IFormEditAddress) => {
      const input = mapperAddress.map<IFormEditAddress, IAddressEdit>(
        data,
        "IAddressEdit",
        "IFormEditAddress",
      );

      return addressService.update(input);
    },
    onError: e => handleQueryError(e),
  });

  return updateAddressMutation;
};

export default useUpdateAddress;
