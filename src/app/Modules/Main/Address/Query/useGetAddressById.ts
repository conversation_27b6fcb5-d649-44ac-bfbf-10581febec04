import {useQuery} from "@tanstack/react-query";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {IQueryError, IQuerySuccess} from "src/business/Interfaces/Query";
import {IAddressService} from "src/business/Interfaces/Services/IAddress";
import {Address} from "src/business/Models/Address/Address";
import AppError from "src/business/Tools/AppError";

const useGetAddressById = (
  id: string,
  onSuccess?: IQuerySuccess<Address>,
  onError?: IQueryError,
) => {
  const addressService = container.get<IAddressService>(TOKENS.AddressService);

  const addressQuery = useQuery({
    queryKey: [QUERY_KEYS.GET_ADDRESS_BY_ID, id],
    queryFn: async () => {
      const response = await addressService.getById(id);

      if (response instanceof AppError) {
        onError && onError(response);

        return;
      }

      onSuccess && onSuccess(response);

      return response;
    },
  });

  return addressQuery;
};

export default useGetAddressById;
