import {useQuery} from "@tanstack/react-query";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {IQueryError, IQuerySuccess} from "src/business/Interfaces/Query";
import {IAddressService} from "src/business/Interfaces/Services/IAddress";
import {AddressOption} from "src/business/Models/Address/AddressOption";
import AppError from "src/business/Tools/AppError";

const useGetAddressOptions = (
  onSuccess?: IQuerySuccess<AddressOption[]>,
  onError?: IQueryError,
) => {
  const addressService = container.get<IAddressService>(TOKENS.AddressService);

  const addresseOptionsQuery = useQuery({
    queryKey: [QUERY_KEYS.GET_USER_ADDRESSES_OPTIONS],
    queryFn: async () => {
      const response = await addressService.getAddresseOptionsByUserId();

      if (response instanceof AppError) {
        onError && onError(response);

        return;
      }

      onSuccess && onSuccess(response);

      return response;
    },
  });

  return addresseOptionsQuery;
};

export default useGetAddressOptions;
