import {useMutation} from "@tanstack/react-query";
import handleQueryError from "src/app/Utils/HandleQueryError";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {IAddressService} from "src/business/Interfaces/Services/IAddress";

const useDeleteAddress = () => {
  const addressService = container.get<IAddressService>(TOKENS.AddressService);

  const deleteAddressMutation = useMutation({
    mutationFn: (addressId: string) => {
      return addressService.delete(addressId);
    },
    onError: e => handleQueryError(e),
  });

  return deleteAddressMutation;
};

export default useDeleteAddress;
