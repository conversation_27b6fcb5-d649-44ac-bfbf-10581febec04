import { Resources } from "src/app/Context/Utils/Resources";
import formSettings from "src/app/Modules/Main/Address/Components/FormBase/formSettings";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import ZodString from "src/app/Utils/Zod/ZodString";
import ZodStringRequired from "src/app/Utils/Zod/ZodStringRequired";
import { boolean, number } from "zod";

const getSchemaCreateAddress = () => {
  const resources = Resources.get();

  const { fieldLength } = formSettings;
  const schema = {
    nickname: ZodString()
      .max(fieldLength.nickname.max, {
        message: setResourceParameters(resources.generic.errors.long_field, fieldLength.nickname.max),
      })
      .optional(),
    postcode: ZodStringRequired()
      .min(fieldLength.postcode.min, {
        message: setResourceParameters(resources.generic.errors.short_field, fieldLength.postcode.min),
      })
      .max(fieldLength.postcode.max, {
        message: setResourceParameters(resources.generic.errors.exactly_number, fieldLength.postcode.max),
      }),
    country: ZodStringRequired().max(fieldLength.country.max, {
      message: setResourceParameters(resources.generic.errors.long_field, fieldLength.country.max),
    }),
    state: ZodStringRequired().length(fieldLength.state.max, {
      message: setResourceParameters(resources.generic.errors.exactly_number, fieldLength.state.max),
    }),
    city: ZodStringRequired().max(fieldLength.city.max, {
      message: setResourceParameters(resources.generic.errors.long_field, fieldLength.city.max),
    }),
    district: ZodStringRequired().max(fieldLength.district.max, {
      message: setResourceParameters(resources.generic.errors.long_field, fieldLength.district.max),
    }),
    street: ZodStringRequired().max(fieldLength.street.max, {
      message: setResourceParameters(resources.generic.errors.long_field, fieldLength.street.max),
    }),
    complement: ZodString()
      .max(fieldLength.complement.max, {
        message: setResourceParameters(resources.generic.errors.long_field, fieldLength.complement.max),
      })
      .optional(),
    number: ZodStringRequired().max(fieldLength.number.max, {
      message: resources.generic.errors.smaller_value,
    }),

    // TODO on create address the map screen is crashing the app, those fields aren't getting filled
    setOnMap: boolean({ required_error: resources.address.components.confirm_address.error_required }),
    latitude: number(),
    longitude: number(),
  };
  return schema;
};

export default getSchemaCreateAddress;
