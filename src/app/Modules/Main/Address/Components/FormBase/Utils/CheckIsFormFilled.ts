import { IFormCreateAddress } from "src/business/DTOs/Forms/Create/Address";

const checkIsFormFilled = (formData: IFormCreateAddress) => {
  const data = {
    street: formData.street,
    number: formData.number,
    complement: formData.complement,
    district: formData.district,
    country: formData.country,
    city: formData.city,
    state: formData.state,
    postcode: formData.postcode,
    nickname: formData.nickname,
  };

  return Object.entries(data).some(
    ([_, value]) => value === "" || value === undefined
  );
};

export default checkIsFormFilled;
