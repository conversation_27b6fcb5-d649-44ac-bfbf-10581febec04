import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {Platform} from "react-native";

import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (width: number): IStyleProps => {
  return {
    container: {
      style: {
        marginHorizontal: wp("5%"),
        marginTop: wp("5%")
      },
    },
    buttonBox: {
      style: {
        width: "100%",
        justifyContent: "center",
        marginHorizontal: wp("5%"),
      },
    },
    submitButton: {
      style: {
        marginHorizontal: wp("5%"),
      },
    },

    box: {
      bgColor: "primary.500",
      style: {
        elevation: 6,
        padding: moderateScale(10),
        position: "absolute",
      },
    },
    boxText: {
      style: {
        textAlign: "center",
        color: "white",
      },
    },
    confirmButton: {
      bgColor: ThemesApp.getTheme().colors.lime[500],
      style: {
        position: "absolute",
        bottom: Platform.OS === "ios" ? hp("6%") : hp("3%"),
        left: Platform.OS === "ios" ? "45%" : "50%",
        transform: [{translateX: -60}],
        height: 56,
        borderRadius: 10,
        width: wp("33%"),
      },
    },
    pressable: {
      style: {
        padding: moderateScale(6),
        marginHorizontal: wp("5%"),
      },
    },
    alertContainer: {
      width: "60%",
      position: "absolute",
      left: width * 0.2,
      top: 5,
      zIndex: 1,
    },
    alertStack: {
      justifyContent: "center",
      padding: 2,
      ml: 2,
    },
    alertTitle: {
      textAlign: "center",
      fontSize: "sm",
    },

    input:{
      height: "16",
      borderRadius: "lg",
      backgroundColor: "gray.100",
      padding: "3",
      fontSize: "md",
      borderWidth: "2"
    },

   
  };
};

export default customStyles;
