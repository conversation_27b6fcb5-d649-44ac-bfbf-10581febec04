import cep, {CEP} from "cep-promise";
import {Input, ZIP_CODE_BRL} from "input-mask-native-base-rhf";
import {
  Alert,
  Box,
  Button,
  HStack,
  ScrollView,
  Text,
  VStack,
} from "native-base";
import React, {useEffect, useRef, useState} from "react";
import {useFormContext} from "react-hook-form";
import {
  Dimensions,
  NativeSyntheticEvent,
  TextInputEndEditingEventData,
} from "react-native";
import Config from "react-native-config";
import GeoCoder from "react-native-geocoding";
import {MapPressEvent, Region} from "react-native-maps";
import InputOrderTab from "src/app/Components/InputOrderTab";
import Maps from "src/app/Components/Maps";
import showToastError from "src/app/Components/Toast/toastError";
import {Components} from "src/app/Context/Utils/Components";
import {Resources} from "src/app/Context/Utils/Resources";
import ConfirmAddress from "src/app/Modules/Main/Address/Components/ConfirmAddress";
import formSettings from "src/app/Modules/Main/Address/Components/FormBase/formSettings";
import customStyles from "src/app/Modules/Main/Address/Components/FormBase/styles";
import {
  LATITUDE_DELTA,
  LONGITUDE_DELTA,
} from "src/app/Utils/Address/CoordsUtilities";
import getFormattedAddress from "src/app/Utils/GetFormattedAddress";
import {IFormCreateAddress} from "src/business/DTOs/Forms/Create/Address";
import {Location} from "src/business/DTOs/Location";
import {Address} from "src/business/Models/Address/Address";
import container from "src/business/Config/Inversify/InversifyConfig";
import IntegrationHubService from "src/business/Services/IntegrationHub";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import Zipcode from "src/business/Models/Zipcode";

GeoCoder.init(Config.ANDROID_MAPS_KEY!);

const {width} = Dimensions.get("screen");

interface AddressFormBaseProps {
  /**
   * @default true
   */
  hasNickname?: boolean;

  onMapOpenInfoMessage?: string;

  onMapIsOpen?: () => void;

  onMapIsClose?: () => void;
}

const AddressFormBase = ({
  hasNickname = true,
  onMapOpenInfoMessage,
  onMapIsOpen,
  onMapIsClose,
}: AddressFormBaseProps) => {
  const {
    setValue,
    formState: {errors},
    getValues,
    clearErrors,
    setFocus,
    watch,
  } = useFormContext<IFormCreateAddress>();
  const resources = Resources.get();
  const {
    address: {create: addressResources},
  } = resources;

  const {fieldLength} = formSettings;

  const latitude = watch("latitude");
  const longitude = watch("longitude");

  const [mapVisibility, setMapVisibility] = useState(false);
  const [marker, setMarker] = useState<Location>({} as Location);
  const [region, setRegion] = useState<Region | undefined>();
  const [mapButtonLoading, setMapButtonLoading] = useState<boolean>(false);
  const scrollViewRef = useRef<any>(null);
  const contentHeight = useRef(0);

  const styles = customStyles(width);

  const searchCep = async (postcode: string) => {
    const IntegrationHubService = container.get<IntegrationHubService>(
      TOKENS.IntegrationHubService,
    );
    postcode = postcode.replace("-", "");
    setValue("postcode", postcode);

    console.log("searchCep - CEP to search:", postcode);

    if (postcode.length === 8) {
      try {
        console.log("searchCep - Calling IntegrationHubService.getByZipCode");
        const result = await IntegrationHubService.getByZipCode(postcode);
        console.log("searchCep - Result:", result);
        return result;
      } catch (err) {
        console.log("searchCep - Error:", err);
        throw err; // Re-throw para ser capturado no handleEndEditing
      }
    } else {
      console.log("searchCep - Invalid CEP length:", postcode.length);
    }
  };

  useEffect(() => {
    setRegion({
      latitude,
      longitude,
      latitudeDelta: LATITUDE_DELTA,
      longitudeDelta: LONGITUDE_DELTA,
    });
    setMarker({
      latitude,
      longitude,
    });
  }, [getValues, latitude, longitude]);

  const addMarker = (event: MapPressEvent) => {
    const newMarker: Location = {
      latitude: event.nativeEvent.coordinate.latitude,
      longitude: event.nativeEvent.coordinate.longitude,
    };

    setMarker(newMarker);
    setRegion({
      latitude: newMarker.latitude,
      longitude: newMarker.longitude,
      latitudeDelta: LATITUDE_DELTA,
      longitudeDelta: LONGITUDE_DELTA,
    });

    setValue("latitude", newMarker.latitude);
    setValue("longitude", newMarker.longitude);
  };

  const setFieldsCEP = (result: Zipcode) => {
    if (result) {
      setValue("city", result.city);
      setValue("district", result.neighborhood);
      setValue("state", result.state);
      setValue("street", result.street);
      setValue("country", addressResources.label.brasil);
    } else {
      showToastError(resources.address.create.toasts.postcode_error, {
        duration: 4000,
      });
      setValue("city", "");
      setValue("district", "");
      setValue("state", "");
      setValue("street", "");
    }
  };

  const setFieldsMap = (address: Address) => {
    if (address) {
      setValue("city", address.city);
      setValue("district", address.district);
      setValue("state", address.state);
      setValue("street", address.street);
      setValue("country", address.country);
    } else {
      setValue("city", "");
      setValue("district", "");
      setValue("state", "");
      setValue("street", "");
      setValue("country", "");
    }
  };

  const handleInitialRegion = (result: CEP | undefined) => {
    if (result) {
      GeoCoder.from(
        `${result.street}, ${result.neighborhood}, ${result.city}, ${result.state}`,
      )
        .then(json => {
          if (json.results && json.results.length > 0) {
            setRegion({
              latitude: json.results[0].geometry.location.lat,
              longitude: json.results[0].geometry.location.lng,
              latitudeDelta: LATITUDE_DELTA,
              longitudeDelta: LONGITUDE_DELTA,
            });
          }
        })
        .catch(error => {
          console.log("Error in GeoCoder:", error);
        });
    } else {
      console.log("Error handle initial region");
    }
  };

  const handleMapFocus = (coords: Location) => {
    if (coords) {
      setMapButtonLoading(true);
      GeoCoder.from({latitude: coords.latitude, longitude: coords.longitude})
        .then(json => {
          const address = getFormattedAddress(json.results[0]);
          setFieldsMap(address);
          setMapVisibility(false);
          setMapButtonLoading(false);
          setValue("setOnMap", true);
          clearErrors("setOnMap");
          if (onMapIsClose) {
            onMapIsClose();
          }

          scrollViewRef.current?.scrollTo({
            y: contentHeight.current,
            animated: false,
          });
        })
        .catch(err => {
          console.log(err);
          setMapButtonLoading(false);
        });
    } else {
      console.log("result vazio");
    }
  };

  const handleEndEditing = async (
    value: NativeSyntheticEvent<TextInputEndEditingEventData>,
  ) => {
    // Persist the event to avoid React Native event pooling issues
    value.persist();

    const subscriber = Components.showLoading();

    try {
      const cepText = value.nativeEvent?.text;
      if (!cepText) {
        console.log("handleEndEditing - No CEP text provided");
        return;
      }

      console.log("handleEndEditing - Processing CEP:", cepText);
      const result = await searchCep(cepText);

      if (result && "street" in result) {
        setFieldsCEP(result);
        handleInitialRegion({
          cep: cepText,
          street: result.street,
          neighborhood: result.neighborhood,
          city: result.city,
          state: result.state,
          service: result.service,
        });
      }
    } catch (error) {
      console.log("Error in handleEndEditing:", error);
      showToastError(resources.address.create.toasts.postcode_error, {
        duration: 4000,
      });
    } finally {
      subscriber.remove();
    }
  };

  const handleConfirmAddressPress = () => {
    if (getValues("postcode")) {
      if (onMapIsOpen) {
        onMapIsOpen();
      }
      setMapVisibility(true);
    }
  };

  return mapVisibility ? (
    <>
      {onMapOpenInfoMessage ? (
        <Alert status="info" {...styles.alertContainer}>
          <HStack space={2} {...styles.alertStack}>
            <Alert.Icon />
            <Text {...styles.alertTitle}>{onMapOpenInfoMessage}</Text>
          </HStack>
        </Alert>
      ) : null}
      <Maps
        initialRegion={region}
        marker={marker}
        onPress={event => addMarker(event)}
      />
      <Button
        onPress={() => handleMapFocus({latitude, longitude})}
        isDisabled={marker.latitude === undefined}
        isLoading={mapButtonLoading}
        {...styles.confirmButton}>
        {resources.address.create.button.confirm}
      </Button>
    </>
  ) : (
    <ScrollView
      ref={scrollViewRef}
      onContentSizeChange={(w, h) => {
        contentHeight.current = h;
      }}>
      <VStack>
        <Box {...styles.container}>
          <InputOrderTab setFocus={setFocus}>
            {hasNickname ? (
              <Input
                name="nickname"
                label={addressResources.label.nickname}
                errorMessage={errors.nickname?.message}
                placeholder={addressResources.placeholder.nickname}
                maxLength={formSettings.fieldLength.nickname.max}
                {...styles.input}
                _focus={{
                  borderColor: "#95c11f",
                  backgroundColor: "gray.200",
                }}
              />
            ) : null}
            <Input
              name="postcode"
              label={addressResources.label.postcode}
              errorMessage={errors.postcode?.message}
              placeholder={addressResources.placeholder.postcode}
              keyboardType="numeric"
              type="text"
              onEndEditing={handleEndEditing}
              mask={ZIP_CODE_BRL}
              isRequired={true}
              {...styles.input}
              _focus={{
                borderColor: "#95c11f",
                backgroundColor: "gray.200",
              }}
            />
            <Input
              name="country"
              label={addressResources.label.country}
              errorMessage={errors.country?.message}
              placeholder={addressResources.placeholder.country}
              maxLength={formSettings.fieldLength.country.max}
              isRequired={true}
              {...styles.input}
              _focus={{
                borderColor: "#95c11f",
                backgroundColor: "gray.200",
              }}
            />
            <Input
              name="state"
              label={addressResources.label.state}
              errorMessage={errors.state?.message}
              placeholder={addressResources.placeholder.state}
              maxLength={formSettings.fieldLength.state.max}
              isRequired={true}
              {...styles.input}
              _focus={{
                borderColor: "#95c11f",
                backgroundColor: "gray.200",
              }}
            />
            <Input
              name="city"
              label={addressResources.label.city}
              errorMessage={errors.city?.message}
              placeholder={addressResources.placeholder.city}
              maxLength={formSettings.fieldLength.city.max}
              isRequired={true}
              {...styles.input}
              _focus={{
                borderColor: "#95c11f",
                backgroundColor: "gray.200",
              }}
            />
            <Input
              name="district"
              label={addressResources.label.district}
              errorMessage={errors.district?.message}
              placeholder={addressResources.placeholder.district}
              maxLength={formSettings.fieldLength.district.max}
              isRequired={true}
              {...styles.input}
              _focus={{
                borderColor: "#95c11f",
                backgroundColor: "gray.200",
              }}
            />
            <Input
              name="street"
              label={addressResources.label.street}
              errorMessage={errors.street?.message}
              placeholder={addressResources.placeholder.street}
              maxLength={formSettings.fieldLength.street.max}
              isRequired={true}
              {...styles.input}
              _focus={{
                borderColor: "#95c11f",
                backgroundColor: "gray.200",
              }}
            />
            <Input
              name="complement"
              label={addressResources.label.complement}
              errorMessage={errors.complement?.message}
              placeholder={addressResources.placeholder.complement}
              maxLength={formSettings.fieldLength.complement.max}
              isRequired={false}
              {...styles.input}
              _focus={{
                borderColor: "#95c11f",
                backgroundColor: "gray.200",
              }}
            />
            <Input
              name="number"
              label={addressResources.label.number}
              errorMessage={errors.number?.message}
              placeholder={addressResources.placeholder.number}
              keyboardType="default"
              maxLength={fieldLength.number.max}
              isRequired={true}
              {...styles.input}
              _focus={{
                borderColor: "#95c11f",
                backgroundColor: "gray.200",
              }}
            />
          </InputOrderTab>
        </Box>
        <ConfirmAddress
          onPress={handleConfirmAddressPress}
          error={errors.setOnMap?.message}
          isConfirmed={getValues("setOnMap")}
        />
      </VStack>
    </ScrollView>
  );
};

export default AddressFormBase;
