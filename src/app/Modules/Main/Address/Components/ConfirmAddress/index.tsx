import { HStack, IPressableProps, Pressable, Text, VStack } from "native-base";
import React from "react";
import { Resources } from "src/app/Context/Utils/Resources";
import { Checkbox, Square, WarningOutline } from "src/assets/Icons/Flaticon";
import customStyles from "src/app/Modules/Main/Address/Components/ConfirmAddress/styles";

interface Props extends IPressableProps {
  isConfirmed?: boolean;
  error?: string;
}

const ConfirmAddress: React.FC<Props> = ({ isConfirmed, error, ...rest }) => {
  const resources = Resources.get();
  const styles = customStyles();
  return (
    <VStack {...styles.container}>
      <Pressable
        bgColor={isConfirmed ? "lime.500" : "lime.300"}
        borderColor={isConfirmed ? "green.500" : "lime.300"}
        {...styles.pressable}
        {...rest}
      >
        <HStack {...styles.mainContent}>
          {isConfirmed ? (
            <>
              <Text {...styles.textConfirm}>{resources.address.components.confirm_address.address_confirmed}</Text>
              <Checkbox {...styles.icon.style} />
            </>
          ) : (
            <>
              <Text {...styles.textConfirm}>{resources.address.components.confirm_address.confirm_address}</Text>
              <Square {...styles.iconSquare.style} />
            </>
          )}
        </HStack>
      </Pressable>

      {error ? (
        <HStack {...styles.errorContainer}>
          <Text color="danger.500" {...styles.textConfirm}>
            {error}
          </Text>
          <WarningOutline {...styles.iconWarning.style} />
        </HStack>
      ) : null}
    </VStack>
  );
};

export default ConfirmAddress;
