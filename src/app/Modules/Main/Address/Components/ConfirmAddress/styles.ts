import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        marginHorizontal: wp("5%"),
        marginBottom: verticalScale(8),
      },
    },
    pressable: {
      style: {
        borderRadius: moderateScale(10),
        borderWidth: moderateScale(1.5),
        paddingVertical: verticalScale(15),
        paddingHorizontal: horizontalScale(10),
        marginBottom: verticalScale(8),
      },
    },
    mainContent: {
      style: {
        alignItems: "center",
        justifyContent: "space-between",
      },
    },
    errorContainer: {
      style: {
        marginTop: verticalScale(3),
        paddingHorizontal: horizontalScale(10),
        paddingVertical: horizontalScale(5),
        alignItems: "center",
        justifyContent: "space-between",
        backgroundColor: ThemesApp.getTheme().colors.red[700],
      },
    },

    textConfirm: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        fontWeight: "600",
        color: "white",
      },
    },

    icon: {
      style: {
        fill: ThemesApp.getTheme().colors.success[200],
      },
    },

    iconSquare: {
      style: {
        fill: ThemesApp.getTheme().colors.green[700],
        width: 20,

      },
    },

    iconWarning: {
      style: {
        fill: ThemesApp.getTheme().colors.danger[200],
      },
    },
  };
};

export default customStyles;
