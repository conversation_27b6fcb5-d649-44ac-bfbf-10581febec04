import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {Box, Fab, FlatList} from "native-base";
import React from "react";
import StackHeader from "src/app/Components/Header/StackHeader";
import ItemNotFound from "src/app/Components/ItemNotFound";
import ModalProgress from "src/app/Components/ModalProgress";
import useTranslation from "src/app/Hooks/useTranslation";
import AddressCardList from "src/app/Modules/Main/Address/Presentation/Home/Components/AdressCardList";
import customStyles from "src/app/Modules/Main/Address/Presentation/Home/styles";
import useGetUserAddresses from "src/app/Modules/Main/Address/Query/useGetAddresses";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import {Plus} from "src/assets/Icons/Flaticon";

type Props = NativeStackScreenProps<MainAppStackParamList, "AddressHome">;

const AddressHome: React.FC<Props> = ({navigation, route}) => {
  const styles = customStyles();

  const userAddressesQuery = useGetUserAddresses();

  const addresses = userAddressesQuery.data;

  const addAddress = () => {
    navigation.navigate("AddressCreate");
  };

  const {
    address: {create: resources},
  } = useTranslation();

  const onRefresh = () => {
    userAddressesQuery.refetch();
  };

  return (
    <>
      <StackHeader navigation={navigation} route={route} />
      {userAddressesQuery.isLoading && <ModalProgress />}

      {(!addresses || addresses.length <= 0) &&
        !userAddressesQuery.isPending && (
          <ItemNotFound
            flex={1}
            title={resources.button.not_found}
            directionComponent="column"
          />
        )}
      <Box {...styles.mainBox}>
        <FlatList
          data={addresses}
          renderItem={({item}: any) => <AddressCardList address={item} />}
          keyExtractor={(item: any) => item.id}
          extraData={addresses}
          refreshing={userAddressesQuery.isRefetching}
          onRefresh={onRefresh}
        />
      </Box>

      <Box {...styles.vStackStaagger}>
        <Fab
          renderInPortal={false}
          shadow={2}
          {...styles.fabIcon}
          icon={<Plus {...styles.iconPlus.style} />}
          onPress={addAddress}
        />
      </Box>
    </>
  );
};

export default AddressHome;
