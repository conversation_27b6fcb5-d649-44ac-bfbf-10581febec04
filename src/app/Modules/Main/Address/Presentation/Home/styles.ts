import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainBox: {
      style: {
        flex: 1,
        marginTop: hp("2%"),
      },
    },

    container: {
      style: {
        margin: wp("2%"),
      },
    },

    boxNoAddresses: {
      style: {
        justifyContent: "center",
        alignContent: "center",
        height: hp("100%"),
      },
    },

    textNoAddresses: {
      style: {
        textAlign: "center",
        fontSize: RFValue(18),
        lineHeight: RFValue(20),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    vStackStaagger: {
      style: {
        margin: wp("2%"),
        position: "absolute",
        bottom: verticalScale(0),
        right: horizontalScale(0),
      },
    },

    hStackItemStagger: {
      style: {
        borderTopRightRadius: moderateScale(999),
        borderBottomRightRadius: moderateScale(999),
        borderTopLeftRadius: moderateScale(400),
        borderBottomLeftRadius: moderateScale(400),
        alignItems: "center",
        marginRight: wp("2%"),
        marginTop: hp("1%"),
      },
    },

    textItemStagger: {
      style: {
        paddingRight: wp("4%"),
        paddingLeft: wp("2%"),
        fontSize: RFValue(10),
        lineHeight: RFValue(14),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    iconPin: {
      style: {
        width: moderateScale(16),
        height: moderateScale(16),
        color: ThemesApp.getTheme().colors.cardList,
      },
    },

    iconPlus: {
      style: {
        width: moderateScale(24),
        height: moderateScale(24),
        fill: ThemesApp.getTheme().colors.white,
      },
    },

    fabIcon: {
      size: moderateScale(40),
      style: {
        elevation: 4,
        borderRadius: moderateScale(999),
        backgroundColor: ThemesApp.getTheme().colors.heartIcon,
        marginLeft: wp("3%"),
        marginTop: hp("1%"),
      },
    },
  };
};

export default customStyles;
