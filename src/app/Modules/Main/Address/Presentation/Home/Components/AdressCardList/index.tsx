/* eslint-disable react/no-unstable-nested-components */
import {useQueryClient} from "@tanstack/react-query";
import {formatWithMask, ZIP_CODE} from "input-mask-native-base-rhf";
import {
  Box,
  Button,
  Divider,
  HStack,
  Menu,
  Pressable,
  Text,
  VStack,
} from "native-base";

import React, {useState} from "react";

import ModalRN from "src/app/Components/Modal";
import showToastError from "src/app/Components/Toast/toastError";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import useTranslation from "src/app/Hooks/useTranslation";
import customStyles from "src/app/Modules/Main/Address/Presentation/Home/Components/AdressCardList/styles";
import useDeleteAddress from "src/app/Modules/Main/Address/Query/useDeleteAddress";
import useUpdateAddress from "src/app/Modules/Main/Address/Query/useUpdateAddress";
import {viewAddressOnMap} from "src/app/Utils/Address/ViewAddressOnMap";
import {rootGoBack, rootNavigation} from "src/app/Utils/RootNavigation";
import {EllipsisVertical, Motorcycle} from "src/assets/Icons/Flaticon";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {IFormEditAddress} from "src/business/DTOs/Forms/Edit/Address";
import {Address} from "src/business/Models/Address/Address";

interface IAddressCardList {
  address: Address;
}

const AddressCardList = ({address}: IAddressCardList) => {
  const styles = customStyles();
  const [visible, setVisible] = useState(false);
  const {
    address: {create: resources, home: homeResources},
  } = useTranslation();

  const queryClient = useQueryClient();

  const updateAddressMutation = useUpdateAddress();
  const deleteAddressMutation = useDeleteAddress();

  const deleteAddress = async (id: string) => {
    deleteAddressMutation.mutate(id, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.GET_USER_ADDRESSES],
        });
        setVisible(!visible);
        showToastSuccess(resources.toasts.delete_success);
      },
      onError: () => {
        showToastError(resources.toasts.delete_error);
      },
    });
  };

  const onCancel = () => {
    setVisible(false);
  };

  const handleDefaultAddress = async () => {
    address.isDefault = true;

    updateAddressMutation.mutate(address as IFormEditAddress, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.GET_USER_ADDRESSES],
        });
        showToastSuccess(resources.toasts.edit_success);
        rootGoBack();
      },
    });
  };

  const editAddress = async () => {
    rootNavigation("MainApp", {
      screen: "AddressEdit",
      params: {addressId: address.id},
    });
  };

  return (
    <>
      <Pressable
        onPress={() => editAddress()}
        _pressed={{opacity: 0.5}}
        {...styles.pressableContainer}>
        <HStack space={2}>
          <Box {...styles.boxIcon}>
            <Motorcycle {...styles.iconAddress.style} />
          </Box>

          <VStack space={1} {...styles.vStackAddress}>
            <Text
              ellipsizeMode="tail"
              numberOfLines={2}
              {...styles.textAddressNickname}>
              {address.nickname ? address.nickname : address.street}
            </Text>
            <Divider />
            <Text
              ellipsizeMode="tail"
              numberOfLines={2}
              {...styles.textAddressInfo}>
              {address.street}, {address.number}, {address.district},
              {address.complement}
            </Text>
            <Text {...styles.textAddressInfo}>
              {address.city} , {address.state}
            </Text>
            <Text {...styles.textAddressInfo}>
              {address.country} ,
              {
                formatWithMask({
                  text: address.postcode.toString(),
                  mask: ZIP_CODE,
                }).masked
              }
            </Text>
          </VStack>
          {address.isDefault ? (
            <Box {...styles.defaultBox}>
              <Text {...styles.defaultText}>{homeResources.text.default}</Text>
            </Box>
          ) : null}
          <Menu
            placement="left top"
            trigger={triggerProps => {
              return (
                <Pressable _pressed={{opacity: 0.5}} {...triggerProps}>
                  <EllipsisVertical {...styles.iconMenu.style} />
                </Pressable>
              );
            }}>
            <Menu.Item
              _text={{...styles.textMenu}}
              onPress={() => handleDefaultAddress()}
              {...styles.menuItemOption}>
              {homeResources.menu.default}
            </Menu.Item>
            <Menu.Item
              _text={{...styles.textMenu}}
              onPress={() =>
                viewAddressOnMap(address.latitude, address.longitude)
              }
              {...styles.menuItemOption}>
              {homeResources.menu.map}
            </Menu.Item>
            <Menu.Item
              _text={{...styles.textMenuDelete}}
              onPress={() => setVisible(!visible)}
              {...styles.menuItemOption}>
              {homeResources.menu.delete}
            </Menu.Item>
          </Menu>
        </HStack>
      </Pressable>

      <ModalRN
        title={resources.text.deleteModalTittle}
        visibility={visible}
        onClose={() => setVisible(!visible)}
        componentFooter={
          <Button.Group direction="row" {...styles.buttonGroup}>
            <Button
              onPress={() => {
                onCancel();
              }}
              {...styles.buttonNo}>
              {resources.button.no}
            </Button>
            <Button
              onPress={() => {
                deleteAddress(address.id);
              }}
              isLoading={deleteAddressMutation.isPending}
              {...styles.buttonYes}>
              {resources.button.yes}
            </Button>
          </Button.Group>
        }>
        {resources.text.deleteModalText}
      </ModalRN>
    </>
  );
};

export default AddressCardList;
