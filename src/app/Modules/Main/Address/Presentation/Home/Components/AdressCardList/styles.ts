import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    pressableContainer: {
      style: {
        borderColor: ThemesApp.getTheme().colors.muted[400],
        marginVertical: hp("1%"),
        marginHorizontal: wp("3%"),
        padding: wp("2%"),
        elevation: 3,
        borderRadius: moderateScale(5),
        backgroundColor: ThemesApp.getTheme().colors.cardList,
        borderWidth: moderateScale(1),
      },
    },

    textMenu: {
      style: {
        color: ThemesApp.getTheme().colors.textColor,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    textMenuDelete: {
      style: {
        color: ThemesApp.getTheme().colors.deleteAccountText,
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    boxIcon: {
      style: {
        alignItems: "center",
        justifyContent: "center",
        width: wp("10%"),
      },
    },

    textAddressInfo: {
      style: {
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    vStackAddress: {
      style: {
        flex: 1,
      },
    },

    iconAddress: {
      style: {
        fill: ThemesApp.getTheme().colors.textColor,
        width: moderateScale(25),
        height: moderateScale(25),
      },
    },

    iconMenu: {
      style: {
        fill: ThemesApp.getTheme().colors.textColor,
        width: moderateScale(16),
        height: moderateScale(16),
      },
    },

    toastSuccess: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.green[600],
      },
    },

    toastError: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.red[600],
      },
    },

    buttonGroup: {
      style: {
        justifyContent: "flex-end",
        width: wp("100%"),
      },
    },

    buttonNo: {
      style: {
        width: wp("30%"),
      },
    },

    buttonYes: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.error,
      },
    },

    textAddressNickname: {
      style: {
        fontWeight: "bold",
        fontSize: RFValue(12),
        lineHeight: RFValue(14),
      },
    },

    menuItemOption: {
      padding: horizontalScale(6),
    },

    defaultBox: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.blue[600],
        height: hp("3%"),
        paddingHorizontal: wp("3%"),
        alignItems: "center",
        justifyContent: "center",
      },
    },
    defaultText: {
      style: {
        color: ThemesApp.getTheme().colors.white,
        fontWeight: "bold",
        fontSize: RFValue(10),
        lineHeight: RFValue(14),
      },
    },
  };
};

export default customStyles;
