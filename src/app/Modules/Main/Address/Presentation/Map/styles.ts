import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    arrowLeftIcon: {
      style: {
        width: moderateScale(36),
        height: moderateScale(36),
        fill: ThemesApp.getTheme().colors.muted[700],
        position: "absolute",
        top: verticalScale(15),
        left: horizontalScale(15),
      },
    },

    boxLeftIcon: {
      size: moderateScale(36),
      opacity: 0.3,
      style: {
        backgroundColor: ThemesApp.getTheme().colors.muted[50],
        borderRadius: moderateScale(999),
        position: "absolute",
        top: verticalScale(15),
        left: horizontalScale(15),
        alignItems: "center",
        justifyContent: "center",
      },
    },
  };
};

export default customStyles;
