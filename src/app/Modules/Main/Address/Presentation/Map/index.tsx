import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {Box, Pressable} from "native-base";
import React from "react";
import Maps from "src/app/Components/Maps";
import customStyles from "src/app/Modules/Main/Address/Presentation/Map/styles";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import {rootGoBack} from "src/app/Utils/RootNavigation";
import {ArrowLeftCircleOutline} from "src/assets/Icons/Flaticon";

type Props = NativeStackScreenProps<MainAppStackParamList, "Map">;

const MapScreen = ({route}: Props) => {
  const styles = customStyles();
  const {calcRoute, marker} = route.params;
  return (
    <Box flex={1}>
      <Maps calcRoute={calcRoute} marker={marker} />
      <ArrowLeftCircleOutline {...styles.arrowLeftIcon.style} />
      <Pressable
        onPress={() => rootGoBack()}
        _pressed={{opacity: 0.5}}
        {...styles.boxLeftIcon}
      />
    </Box>
  );
};

export default MapScreen;
