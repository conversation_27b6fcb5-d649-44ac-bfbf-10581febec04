import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { horizontalScale, moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        flex: 1,
      },
    },
    submitButton: {
      style: {
        height: wp("12%"),
        marginHorizontal: wp("5%"),
        marginBottom: wp("5%"),
      },
    },
    editMap: {
      flexDirection: "row",
      alignItems: "center",
      style: {
        marginHorizontal: wp("5%"),
        elevation: 1,
        marginBottom: wp("5%"),
        height: verticalScale(60),
        borderRadius: moderateScale(2),
        paddingHorizontal: horizontalScale(5),
      },
    },
    iconMenu: {
      style: {
        width: moderateScale(25),
        height: moderateScale(25),
        marginRight: horizontalScale(5),
        fill: ThemesApp.getTheme().colors.secondary[700],
      },
    },
    confirmButton: {
      style: {
        position: "absolute",
        bottom: hp("3%"),
        left: "50%",
        transform: [{ translateX: -50 }],
      },
    },
    box: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.primary[500],
        elevation: 6,
        padding: moderateScale(15),
      },
    },
    boxText: {
      style: {
        textAlign: "center",
        color: ThemesApp.getTheme().colors.white,
      },
    },
  };
};

export default customStyles;
