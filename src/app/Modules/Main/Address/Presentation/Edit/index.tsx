import {zodResolver} from "@hookform/resolvers/zod";
import {useFocusEffect} from "@react-navigation/native";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {useQueryClient} from "@tanstack/react-query";
import {<PERSON><PERSON>, ScrollView, View} from "native-base";
import React, {useCallback, useEffect, useState} from "react";
import {FormProvider} from "react-hook-form";
import {useFormBase} from "src/app/Components/FormBase";
import StackHeader from "src/app/Components/Header/StackHeader";
import ModalProgress from "src/app/Components/ModalProgress";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import {Resources} from "src/app/Context/Utils/Resources";
import AddressFormBase from "src/app/Modules/Main/Address/Components/FormBase";
import getSchemaCreateAddress from "src/app/Modules/Main/Address/Components/FormBase/validation";
import customStyles from "src/app/Modules/Main/Address/Presentation/Edit/styles";
import useGetAddressById from "src/app/Modules/Main/Address/Query/useGetAddressById";
import useUpdateAddress from "src/app/Modules/Main/Address/Query/useUpdateAddress";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import {rootGoBack} from "src/app/Utils/RootNavigation";
import QUERY_KEYS from "src/business/Config/Query/query-keys";
import {IFormEditAddress} from "src/business/DTOs/Forms/Edit/Address";
import {Address} from "src/business/Models/Address/Address";
import {z} from "zod";

export type Props = NativeStackScreenProps<
  MainAppStackParamList,
  "AddressEdit"
>;

const EditAddress = ({route, navigation}: Props) => {
  const styles = customStyles();

  const {addressId, hasNickname} = route.params;

  const setFormValues = (data: Address) => {
    const entries = Object.entries(data);

    entries.forEach(([key, value]) => {
      if (key === "postcode" || key === "number") {
        methods.setValue(key, String(value));
      } else {
        methods.setValue(key, value);
      }
    });
  };

  const addressQuery = useGetAddressById(addressId, data => {
    setFormValues(data);
  });

  const [buttonVisibility, setButtonVisibility] = useState<boolean>(true);

  const {methods, handleSubmitFormBase} = useFormBase<IFormEditAddress>({
    mode: "onBlur",
    resolver: zodResolver(z.object(getSchemaCreateAddress())),
  });

  useFocusEffect(
    React.useCallback(() => {
      if (!addressQuery.isFetching && addressQuery.data) {
        setFormValues(addressQuery.data);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [addressQuery.data]),
  );

  useEffect(() => {
    const unsubscribeBlur = navigation.addListener("blur", () => {
      methods.reset();
    });

    return unsubscribeBlur;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const queryClient = useQueryClient();

  const resources = Resources.get();

  const {
    address: {edit: addressResources},
  } = resources;

  const updateAddressMutation = useUpdateAddress();

  const onSubmit = async (formData: IFormEditAddress) => {
    updateAddressMutation.mutate(formData, {
      onSuccess: () => {
        showToastSuccess(resources.address.create.toasts.edit_success);

        queryClient.invalidateQueries({queryKey: [QUERY_KEYS.GET_USER_STORES]});
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.GET_USER_ADDRESSES],
        });

        queryClient.setQueryData<Address>(
          [QUERY_KEYS.GET_ADDRESS_BY_ID, addressId],
          oldData => {
            const mergedObject = {...oldData, ...formData};

            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const {setOnMap, ...rest} = mergedObject;

            return rest;
          },
        );

        rootGoBack();
      },
    });
  };

  const onMapIsClose = useCallback(() => {
    setButtonVisibility(true);
  }, []);

  const onMapIsOpen = useCallback(() => {
    setButtonVisibility(false);
  }, []);

  return (
    <View {...styles.container}>
      {addressQuery.isLoading ? <ModalProgress /> : null}
      <StackHeader navigation={navigation} route={route} />
      <FormProvider {...methods}>
        <ScrollView>
          <AddressFormBase
            onMapIsClose={onMapIsClose}
            onMapIsOpen={onMapIsOpen}
            hasNickname={hasNickname}
          />
          {buttonVisibility ? (
            <Button
              {...styles.submitButton}
              onPress={() => handleSubmitFormBase(onSubmit)}
              isLoading={updateAddressMutation.isPending}>
              {addressResources.button.submit}
            </Button>
          ) : null}
        </ScrollView>
      </FormProvider>
    </View>
  );
};

export default EditAddress;
