import {zodResolver} from "@hookform/resolvers/zod";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {<PERSON>ton, ScrollView, View} from "native-base";
import React, {useCallback, useState} from "react";
import {FormProvider} from "react-hook-form";
import {useFormBase} from "src/app/Components/FormBase";
import StackHeader from "src/app/Components/Header/StackHeader";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import {Resources} from "src/app/Context/Utils/Resources";
import AddressFormBase from "src/app/Modules/Main/Address/Components/FormBase";
import addressFormInitialValues from "src/app/Modules/Main/Address/Components/FormBase/formInitialValues";
import getSchemaCreateAddress from "src/app/Modules/Main/Address/Components/FormBase/validation";
import customStyles from "src/app/Modules/Main/Address/Presentation/Create/styles";
import useCreateAddress from "src/app/Modules/Main/Address/Query/useCreateAddress";
import useGetUserAddresses from "src/app/Modules/Main/Address/Query/useGetAddresses";
import {MainAppStackParamList} from "src/app/Modules/Main/types";
import {rootGoBack} from "src/app/Utils/RootNavigation";
import {IFormCreateAddress} from "src/business/DTOs/Forms/Create/Address";
import {IAddressCreate} from "src/business/DTOs/Forms/Create/BaseAddress";
import {z} from "zod";

export type Props = NativeStackScreenProps<
  MainAppStackParamList,
  "AddressCreate"
>;

const CreateAddress = ({route, navigation}: Props) => {
  const styles = customStyles();
  const [buttonVisibility, setButtonVisibility] = useState<boolean>(true);

  const {methods, handleSubmitFormBase} = useFormBase<IFormCreateAddress>({
    defaultValues: addressFormInitialValues,
    mode: "onBlur",
    resolver: zodResolver(z.object(getSchemaCreateAddress())),
  });

  const userAddressesQuery = useGetUserAddresses();
  const createAddressMutation = useCreateAddress();

  const resources = Resources.get();

  const {
    address: {create: addressResources},
  } = resources;

  const onSubmit = (formData: IFormCreateAddress) => {
    const data: IAddressCreate = {
      city: formData.city,
      complement: formData.complement,
      country: formData.country,
      district: formData.district,
      latitude: formData.latitude,
      longitude: formData.longitude,
      nickname: formData.nickname,
      number: formData.number,
      postcode: formData.postcode,
      state: formData.state,
      street: formData.street,
      isDefault: formData.isDefault,
    };

    createAddressMutation.mutate(data, {
      onSuccess: () => {
        showToastSuccess(resources.address.create.toasts.create_success);

        methods.reset();
        rootGoBack();

        userAddressesQuery.refetch();
      },
    });
  };

  const onMapIsClose = useCallback(() => {
    setButtonVisibility(true);
  }, []);

  const onMapIsOpen = useCallback(() => {
    setButtonVisibility(false);
  }, []);

  return (
    <View {...styles.container}>
      <StackHeader navigation={navigation} route={route} />

      <FormProvider {...methods}>
        <ScrollView>
          <AddressFormBase
            onMapIsClose={onMapIsClose}
            onMapIsOpen={onMapIsOpen}
          />
          {buttonVisibility ? (
            <Button
              {...styles.submitButton}
              onPress={() => handleSubmitFormBase(onSubmit)}
              isLoading={createAddressMutation.isPending}>
              {addressResources.button.submit}
            </Button>
          ) : null}
        </ScrollView>
      </FormProvider>
    </View>
  );
};

export default CreateAddress;
