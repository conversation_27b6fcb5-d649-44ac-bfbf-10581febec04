import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    container: {
      style: {
        flex: 1,
      },
    },
    submitButton: {
      style: {
        marginHorizontal: wp("5%"),
        marginBottom: wp("5%"),
      },
    },
    confirmButton: {
      style: {
        position: "absolute",
        bottom: hp("3%"),
        left: "50%",
        transform: [{ translateX: -50 }],
      },
    },
    box: {
      style: {
        backgroundColor: ThemesApp.getTheme().colors.primary[500],
        elevation: 6,
        padding: moderateScale(15),
      },
    },
    boxText: {
      style: {
        textAlign: "center",
        color: ThemesApp.getTheme().colors.white,
      },
    },
  };
};

export default customStyles;
