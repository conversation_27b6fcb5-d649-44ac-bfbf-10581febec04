import React from "react";

import useTranslation from "src/app/Hooks/useTranslation";
import CreateAddress from "src/app/Modules/Main/Address/Presentation/Create";
import EditAddress from "src/app/Modules/Main/Address/Presentation/Edit";
import AddressHome from "src/app/Modules/Main/Address/Presentation/Home";
import MapScreen from "src/app/Modules/Main/Address/Presentation/Map";
import {Stack} from "src/app/Modules/Main/routes";

const AddressApp = () => {
  const {
    address: {home: resources},
  } = useTranslation();
  return (
    <Stack.Group
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen
        name="AddressHome"
        component={AddressHome}
        options={{
          title: resources.header.title,
          headerTransparent: true,
          headerTitleAlign: "center",
        }}
      />
      <Stack.Screen name="AddressCreate" component={CreateAddress} />
      <Stack.Screen name="AddressEdit" component={EditAddress} />
      <Stack.Screen name="Map" component={MapScreen} />
    </Stack.Group>
  );
};

export default AddressApp;
