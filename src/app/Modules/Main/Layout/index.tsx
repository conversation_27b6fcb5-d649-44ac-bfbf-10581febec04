import {NativeStackScreenProps} from "@react-navigation/native-stack";
import React, {useMemo} from "react";
import {View} from "react-native";
import BottomTab from "src/app/Components/BottomTab";
import styles from "src/app/Modules/Main/Layout/styles";
import {RootStackParamList} from "src/app/routes";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {INavigationService} from "src/business/Interfaces/Services/INavigation";

type NavigatorProps = NativeStackScreenProps<RootStackParamList, "MainApp">;

interface Props {
  navigation: NavigatorProps["navigation"];
  route: NavigatorProps["route"];
  children: JSX.Element;
}
const MainLayout = ({navigation, route, children}: Props) => {
  const navigationService = container.get<INavigationService>(
    TOKENS.NavigationService,
  );
  const currentRoute = navigationService.getCurrentRoute()?.name;

  const blackList = useMemo(
    () => ["Map", "TrackingOrder", "AddressCreate", "AddressEdit"],
    [],
  );

  const isBottomShow = currentRoute && blackList.includes(currentRoute);

  return (
    <View style={styles.container}>
      <View style={isBottomShow ? styles.full : styles.content}>
        {children}
      </View>
      {isBottomShow ? null : (
        <View style={styles.footer}>
          <BottomTab navigation={navigation} route={route} />
        </View>
      )}
    </View>
  );
};

export default MainLayout;
