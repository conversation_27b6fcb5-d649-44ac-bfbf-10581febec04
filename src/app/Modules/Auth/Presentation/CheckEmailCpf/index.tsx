import {zod<PERSON>esolver} from "@hookform/resolvers/zod";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {BRL_CPF, Input} from "input-mask-native-base-rhf";
import {Box, Button} from "native-base";
import React, {useState} from "react";
import {FormProvider} from "react-hook-form";
import {useFormBase} from "src/app/Components/FormBase";
import StackHeader from "src/app/Components/Header/StackHeader";
import ModalProgress from "src/app/Components/ModalProgress";
import useTranslation from "src/app/Hooks/useTranslation";
import {
  checkDeleteAccount,
  checkRegisteredCpf,
  checkRegisteredEmail,
} from "src/app/Modules/Auth/Presentation/CheckEmailCpf/checkFunctions";
import checkEmailCpfFormInitialValues from "src/app/Modules/Auth/Presentation/CheckEmailCpf/formInitialValues";
import formSettings from "src/app/Modules/Auth/Presentation/CheckEmailCpf/formSettings";
import customStyles from "src/app/Modules/Auth/Presentation/CheckEmailCpf/styles";
import useRefinement from "src/app/Modules/Auth/Presentation/CheckEmailCpf/useRefinement";
import getSchemaCheckEmailCpf from "src/app/Modules/Auth/Presentation/CheckEmailCpf/validation";
import {AuthAppStackParamList} from "src/app/Modules/Auth/types";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {IFormCheckEmailCpf} from "src/business/DTOs/Forms/CheckEmailCpf";

type Props = NativeStackScreenProps<AuthAppStackParamList, "CheckEmailCpf">;

const CheckEmailCpf = ({navigation, route}: Props) => {
  const resources = useTranslation();
  const styles = customStyles();

  const {
    users: {create: userResources},
  } = resources;

  const [isLoading, setIsLoading] = useState(false);

  const checkDeleteAccountRefine = useRefinement(checkDeleteAccount(), {
    debounce: 100,
  });

  const checkRegisteredEmailRefine = useRefinement(checkRegisteredEmail(), {
    debounce: 100,
  });

  const checkRegisteredCpfRefine = useRefinement(checkRegisteredCpf(), {
    debounce: 100,
  });

  const {methods, handleSubmitFormBase} = useFormBase<IFormCheckEmailCpf>({
    initialValues: checkEmailCpfFormInitialValues,
    defaultValues: checkEmailCpfFormInitialValues,
    mode: "onBlur",
    resolver: zodResolver(
      getSchemaCheckEmailCpf()
        .refine(checkDeleteAccountRefine, {
          message: userResources.errors.user_alreadyExists,
          path: ["email"],
        })
        .refine(checkRegisteredEmailRefine, {
          message: userResources.errors.email_alreadyExists,
          path: ["email"],
        })
        .refine(checkRegisteredCpfRefine, {
          message: userResources.errors.cpf_alreadyExists,
          path: ["cpf"],
        }),
    ),
  });

  const {
    formState: {errors},
  } = methods;

  const handleFormSubmit = (data: IFormCheckEmailCpf) => {
    rootNavigation("AuthApp", {
      screen: "SignUp",
      params: {...data},
    });
  };

  return (
    <>
      <StackHeader navigation={navigation} route={route} />
      {isLoading && <ModalProgress />}
      <Box {...styles.boxContainer}>
        <FormProvider {...methods}>
          <Input
            helperMessage={userResources.toasts.email}
            name="email"
            autoCapitalize="none"
            /**
             * NOTE: This field are set keyboard type to visible-password to avoid the bug where the apps navigate
             * back to the previous screen when the user uses the autocomplete feature of the keyboard. The bug was
             * detected only on Android 11 (MotoG31)
             */
            keyboardType="visible-password"
            label={userResources.placeholder.email}
            errorMessage={errors.email?.message}
            type="text"
            placeholder="<EMAIL>"
            maxLength={formSettings.fieldLength.email.max}
            isRequired={true}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth= "2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />
          <Input
            helperMessage={userResources.toasts.cpf}
            name="cpf"
            label={userResources.label.cpf}
            errorMessage={errors.cpf?.message}
            type="text"
            placeholder={userResources.placeholder.cpf}
            keyboardType="numeric"
            mask={BRL_CPF}
            height={60}
            isRequired={true}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth= "2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />
        </FormProvider>
      </Box>
      <Box {...styles.buttonBox}>
        <Button
          {...styles.submitButton}
          onPress={async () => {
            setIsLoading(true);
            await handleSubmitFormBase(handleFormSubmit);
            setIsLoading(false);
          }}
          isLoading={isLoading}>
          {userResources.button.continue}
        </Button>
      </Box>
    </>
  );
};

export default CheckEmailCpf;
