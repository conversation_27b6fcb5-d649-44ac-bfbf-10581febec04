import { RefinementCallback } from "src/app/Modules/Auth/Presentation/CheckEmailCpf/useRefinement";
import { rootNavigation } from "src/app/Utils/RootNavigation";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import { IFormCheckEmailCpf } from "src/business/DTOs/Forms/CheckEmailCpf";
import { IUserService } from "src/business/Interfaces/Services/IUser";
import AppError from "src/business/Tools/AppError";

const findDeleteAccount = async (email: string) => {
  const userService = container.get<IUserService>(TOKENS.UserService);

  const response = await userService.getDeleteAccountStatus(email);
  if (response && !(response instanceof AppError)) {
    rootNavigation("AuthApp", {
      screen: "ReactivateAccount",
      params: { email },
    });
  }
};

function checkDeleteAccount(): RefinementCallback<IFormCheckEmailCpf> {
  return async (data) => {
    await findDeleteAccount(data.email);
    return true;
  };
}

const findRegisteredCpf = async (cpf: string) => {
  const userService = container.get<IUserService>(TOKENS.UserService);
  const response = await userService.checkRegisteredCpf(cpf);
  if (response && !(response instanceof AppError)) return !response;
  return true;
};

function checkRegisteredCpf(): RefinementCallback<IFormCheckEmailCpf> {
  return async (data) => {
    const response = await findRegisteredCpf(data.cpf);
    return response;
  };
}

const findRegisteredEmail = async (email: string) => {
  const userService = container.get<IUserService>(TOKENS.UserService);
  const response = await userService.checkRegisteredEmail(email);
  if (response && !(response instanceof AppError)) return !response;
  return true;
};

function checkRegisteredEmail(): RefinementCallback<IFormCheckEmailCpf> {
  return async (data) => {
    const response = await findRegisteredEmail(data.email);
    return response;
  };
}

export { checkDeleteAccount, checkRegisteredCpf, checkRegisteredEmail };
