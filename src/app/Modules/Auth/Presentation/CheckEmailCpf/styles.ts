import { RFValue } from "react-native-responsive-fontsize";
import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxContainer: {
      style: {
        alignItems: "center",
        marginTop: "2%",
        padding: wp("4%"),
        flex: 1,
      },
    },

    buttonBox: {
      style: {
        width: "100%",
        justifyContent: "center",
        height: hp("10%"),
      },
    },

    submitButton: {
      style: {
        marginHorizontal: wp("5%"),
        marginBottom: wp("10%"),
        height: 60,
        borderRadius: 100,
        backgroundColor: "#95c11f",
      },
    },
  };
};

export default customStyles;
