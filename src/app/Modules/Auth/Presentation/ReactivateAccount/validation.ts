import { Resources } from "src/app/Context/Utils/Resources";
import formSettings from "src/app/Modules/Auth/Presentation/ReactivateAccount/formSettings";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import validateCPF from "src/app/Utils/ValidateCPF";
import ZodStringRequired from "src/app/Utils/Zod/ZodStringRequired";
import { z } from "zod";

const getSchemaReactivateAccount = () => {
  const resources = Resources.get();

  const schema = z.object({
    cpf: ZodStringRequired()
      .min(formSettings.fieldLength.cpf.min, {
        message: resources.generic.errors.invalid_cpf,
      })
      .refine((item) => validateCPF(item), {
        message: resources.generic.errors.invalid_cpf,
      }),
    email: ZodStringRequired()
      .email({
        message: resources.generic.errors.email,
      })
      .max(formSettings.fieldLength.email.max, {
        message: setResourceParameters(resources.generic.errors.long_field, formSettings.fieldLength.email.max),
      }),
  });

  return schema;
};

export default getSchemaReactivateAccount;
