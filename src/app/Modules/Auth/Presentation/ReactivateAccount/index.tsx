import {zod<PERSON>esolver} from "@hookform/resolvers/zod";
import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {BRL_CPF, Input} from "input-mask-native-base-rhf";
import {Box, Button, Text} from "native-base";
import React, {useEffect} from "react";
import {FormProvider} from "react-hook-form";
import {useFormBase} from "src/app/Components/FormBase";
import StackHeader from "src/app/Components/Header/StackHeader";
import useTranslation from "src/app/Hooks/useTranslation";
import reactivateAccountFormInitialValues from "src/app/Modules/Auth/Presentation/ReactivateAccount/formInitialValues";
import formSettings from "src/app/Modules/Auth/Presentation/ReactivateAccount/formSettings";
import customStyles from "src/app/Modules/Auth/Presentation/ReactivateAccount/styles";
import getSchemaReactivateAccount from "src/app/Modules/Auth/Presentation/ReactivateAccount/validation";
import {AuthAppStackParamList} from "src/app/Modules/Auth/types";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {WarningTriangle} from "src/assets/Icons/Flaticon";
import IFormReactivateAccount from "src/business/DTOs/Forms/ReactivateAccount";

type Props = NativeStackScreenProps<AuthAppStackParamList, "ReactivateAccount">;

const ReactivateAccount = ({navigation, route}: Props) => {
  const {email} = route.params;

  const {
    users: {create: userResources, reactivateAccount: resources},
  } = useTranslation();

  useEffect(() => {}, []);

  const handleFormSubmit = async (data: IFormReactivateAccount) => {
    rootNavigation("MainApp", {
      screen: "VerifyEmail",
      params: {reactivateAccount: data},
    });
  };

  const {methods, handleSubmitFormBase} = useFormBase<IFormReactivateAccount>({
    initialValues: {...reactivateAccountFormInitialValues, email},
    mode: "onBlur",
    resolver: zodResolver(getSchemaReactivateAccount()),
  });

  const {
    formState: {errors},
  } = methods;

  const styles = customStyles();

  return (
    <>
      <StackHeader navigation={navigation} route={route} />
      <Box {...styles.boxContainer}>
        <Box {...styles.boxTitle}>
          <WarningTriangle {...styles.titleIcon.style} />
          <Text {...styles.title}>{resources.label.title}</Text>
          <Text {...styles.subtitle}>{resources.label.subtitle}</Text>
        </Box>
        <FormProvider {...methods}>
          <Input
            helperMessage={userResources.toasts.phone}
            name="cpf"
            label={userResources.label.cpf}
            errorMessage={errors.cpf?.message}
            type="text"
            placeholder={userResources.placeholder.cpf}
            keyboardType="numeric"
            mask={BRL_CPF}
            isRequired={true}
          />
          <Input
            helperMessage={userResources.toasts.email}
            name="email"
            autoCapitalize="none"
            label={userResources.placeholder.email}
            errorMessage={errors.email?.message}
            type="text"
            placeholder="<EMAIL>"
            maxLength={formSettings.fieldLength.email.max}
            isRequired={true}
            isReadOnly={true}
          />
        </FormProvider>
      </Box>
      <Box {...styles.buttonBox}>
        <Button
          onPress={() => {
            handleSubmitFormBase(handleFormSubmit);
          }}
          {...styles.button}>
          <Text {...styles.submitText}>{resources.button.reactivate}</Text>
        </Button>
      </Box>
    </>
  );
};

export default ReactivateAccount;
