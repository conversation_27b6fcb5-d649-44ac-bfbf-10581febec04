import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    boxContainer: {
      style: {
        alignItems: "center",
        marginTop: "2%",
        padding: wp("4%"),
        flex: 1,
      },
    },

    boxTitle: {
      style: {
        alignItems: "center",
        borderRadius: moderateScale(5),
        padding: moderateScale(10),
        backgroundColor: ThemesApp.getTheme().colors.orange[200],
        marginBottom: wp("10%"),
      },
    },

    title: {
      fontSize: RFValue(14),
      lineHeight: RFValue(16),
      style: {
        textAlign: "justify",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    subtitle: {
      fontSize: RFValue(14),
      lineHeight: RFValue(16),
      style: {
        marginTop: moderateScale(10),
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.primary[800],
        alignSelf: "flex-start",
      },
    },

    submitText: {
      fontSize: RFValue(14),
      lineHeight: RFValue(16),
      style: {
        color: ThemesApp.getTheme().colors.white,
      },
    },

    titleIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginBottom: moderateScale(10),
        fill: ThemesApp.getTheme().colors.orange[700],
      },
    },

    subtitleIcon: {
      style: {
        width: moderateScale(20),
        height: moderateScale(20),
        marginBottom: moderateScale(10),
        fill: ThemesApp.getTheme().colors.primary[700],
      },
    },

    buttonBox: {
      style: {
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        marginVertical: moderateScale(10),
      },
    },

    button: {
      style: {
        justifyContent: "center",
        alignItems: "center",
        width: "90%",
        marginVertical: moderateScale(10),
      },
    },
  };
};

export default customStyles;
