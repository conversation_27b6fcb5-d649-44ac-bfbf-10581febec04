import { Resources } from "src/app/Context/Utils/Resources";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import formSettings from "src/app/Modules/Auth/Presentation/SignIn/Components/Form/formSettings";
import { z } from "zod";
import ZodStringRequired from "src/app/Utils/Zod/ZodStringRequired";

const getSchemaSignIn = () => {
  const resources = Resources.get().generic.errors;
  const { fieldLength } = formSettings;

  const schema = z.object({
    email: ZodStringRequired().email({
      message: resources.email,
    }),
    password: ZodStringRequired()
      .min(fieldLength.password.min, {
        message: setResourceParameters(
          resources.short_field,
          formSettings.fieldLength.password.min
        ),
      })
      .max(fieldLength.password.max, {
        message: setResourceParameters(
          resources.long_field,
          formSettings.fieldLength.password.max
        ),
      }),
  });

  return schema;
};

export default getSchemaSignIn;
