import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack} from "native-base";
import React, {useMemo, useState} from "react";

import {zodResolver} from "@hookform/resolvers/zod";
import {Input} from "input-mask-native-base-rhf";
import {FormProvider} from "react-hook-form";
import {Keyboard} from "react-native";
import {useFormBase} from "src/app/Components/FormBase";
import InputOrderTab from "src/app/Components/InputOrderTab";
import useTranslation from "src/app/Hooks/useTranslation";
import formSettings from "src/app/Modules/Auth/Presentation/SignIn/Components/Form/formSettings";
import customStyles from "src/app/Modules/Auth/Presentation/SignIn/Components/Form/styles";
import getSchemaSignIn from "src/app/Modules/Auth/Presentation/SignIn/Components/Form/validation";
import useSignIn from "src/app/Modules/Auth/Query/useSignIn";
import navigateHome from "src/app/Utils/NavigateHome";
import initNotifications from "src/app/Utils/Notifications";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import updateLanguageSettings from "src/app/Utils/UpdateLanguageSettings";
import {Eye, EyeOff} from "src/assets/Icons/Flaticon";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {LoginUser} from "src/business/DTOs/LoginUser";
import {IUserService} from "src/business/Interfaces/Services/IUser";
import AppError from "src/business/Tools/AppError";

const SignInForm = () => {
  const styles = customStyles();
  const [showPassword, setShowPassword] = useState(false);

  const [isLoading, setIsLoading] = useState(false);

  const {methods, handleSubmitFormBase} = useFormBase<LoginUser>({
    mode: "onBlur",
    resolver: zodResolver(getSchemaSignIn()),
  });

  const resources = useTranslation();
  const {
    watch,
    formState: {errors},
    setFocus,
    reset,
  } = methods;

  const {
    auth: {signin: authResources},
  } = resources;
  const email = watch("email");

  const signInMutation = useSignIn();

  const onSubmit = async (data: LoginUser) => {
    Keyboard.dismiss();
    setIsLoading(true);

    const userService = container.get<IUserService>(TOKENS.UserService);

    const response = await userService.getDeleteAccountStatus(email);

    if (response && !(response instanceof AppError)) {
      rootNavigation("AuthApp", {
        screen: "ReactivateAccount",
        params: {email},
      });

      setIsLoading(false);
    } else {
      // await dispatch(loginUserThunk({ data }));
      signInMutation.mutate(data, {
        onSuccess: () => {
          initNotifications();
          navigateHome(true);
          reset();
        },
        onSettled: () => {
          setIsLoading(false);
        },
      });

      updateLanguageSettings();
    }
  };

  const icon = useMemo(
    () => (
      <IconButton
        variant="unstyled"
        icon={
          showPassword ? (
            <Eye {...styles.iconPassword.style} />
          ) : (
            <EyeOff {...styles.iconPassword.style} />
          )
        }
        onPress={() => setShowPassword(!showPassword)}
      />
    ),
    [showPassword, styles.iconPassword.style],
  );

  return (
    <FormProvider {...methods}>
      <Stack {...styles.stack}>
        <InputOrderTab setFocus={setFocus}>
          <Input
            autoCapitalize="none"
            name="email"
            label={authResources.placeholder.email}
            errorMessage={errors.email?.message}
            type="text"
            placeholder="<EMAIL>"
            maxLength={formSettings.fieldLength.email.max}
            height={60}
            isRequired={true}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth= "2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />

          <Input
            name="password"
            label={authResources.placeholder.password}
            errorMessage={errors.password?.message}
            placeholder="************"
            type={showPassword ? "text" : "password"}
            InputRightElement={icon}
            maxLength={formSettings.fieldLength.password.max}
            isRequired={true}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth= "2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />
          <Button
            onPress={() => handleSubmitFormBase(onSubmit)}
            isLoading={isLoading || signInMutation.isPending}
            {...styles.button}>
            {authResources.button.signIn}
          </Button>
        </InputOrderTab>
      </Stack>
    </FormProvider>
  );
};

export default SignInForm;
