import {RFValue} from "react-native-responsive-fontsize";
import {widthPercentageToDP as wp} from "react-native-responsive-screen";
import {ThemesApp} from "src/app/Context/Utils/ThemesApp";
import {moderateScale} from "src/app/Utils/Metrics";
import {IStyleProps} from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    stack: {
      style: {
        margin: wp("1%"),
        marginBottom: wp("5%"),
      },
    },
    iconPassword: {
      style: {
        marginRight: wp("2%"),
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.muted[500],
      },
    },
    button: {
      style: {
        marginVertical: RFValue(5),
        height: 60,
        borderRadius: 100,
        backgroundColor: ThemesApp.getTheme().colors.primary[500],
      },
    },
  };
};

export default customStyles;
