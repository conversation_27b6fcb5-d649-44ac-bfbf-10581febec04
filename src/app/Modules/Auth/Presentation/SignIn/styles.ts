import { heightPercentageToDP as hp, widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale, verticalScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    mainView: {
      flex: 1,
    },

    boxLanguage: {
      style: {
        width: wp("95%"),
        alignItems: "flex-end",
      },
    },

    boxContainer: {
      style: {
        alignItems: "center",
        marginTop: "1%",
      },
    },

    boxFormik: {
      style: {
        width: wp("85%"),
      },
    },

    avatarLogo: {
      style: {
        width: moderateScale(200),
        height: moderateScale(200),
        fill: "#000000",
      },
    },

  
    googleIcon: {
      style: {
        width: moderateScale(24),
        height: moderateScale(24),
        fill: ThemesApp.getTheme().colors.red[500],
      },
    },

    facebookIcon: {
      style: {
        width: moderateScale(24),
        height: moderateScale(24),
        fill: ThemesApp.getTheme().colors.primary[500],
      },
    },

    hStack: {
      style: {
        justifyContent: "center",
        alignItems: "center",
        marginVertical: hp("1%"),
        marginBottom: hp("2%")
      },
    },

    buttonSignUp: {
      py: 0,
      style: {
        paddingLeft: wp("2%"),
      },
    },

    buttonGroup: {
      alignItems: "center",
      justifyContent: "center",
      style: {
        marginVertical: hp("2%"),
        margin: wp("1%"),
        flexDirection: "column",
      },
    },

    button: {
      width: wp("80%"),
      py: 2,
      style: {
        borderColor: ThemesApp.getTheme().colors.gray[300],
        marginVertical: verticalScale(5),
      },
    },

    boxLogoSetre: {
      alignItems: "center",
      minHeight: "6%",
      maxHeight: "8%",
      position: "relative",
      bottom: verticalScale(10),
      marginBottom: "5%"
    },

    imageLogoSetre: {
      w: "65%",
      h: "full",
      overflow: "visible",
      style: {
        backgroundColor: ThemesApp.getTheme().colors.logo,
        
      },
    },

    buttonForgotPassword: {
      py: 1,
      style: {
        justifyContent: "center",
        
      },
    },
  };
};

export default customStyles;
