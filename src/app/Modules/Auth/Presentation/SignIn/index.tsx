import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {Box, Button, Divider, HStack, Image, Text, View} from "native-base";
import {InterfaceImageProps} from "native-base/lib/typescript/components/primitives/Image/types";
import React, {useEffect} from "react";
import {Linking} from "react-native";
import useTranslation from "src/app/Hooks/useTranslation";
import SignInForm from "src/app/Modules/Auth/Presentation/SignIn/Components/Form";
import customStyles from "src/app/Modules/Auth/Presentation/SignIn/styles";
import {AuthAppStackParamList} from "src/app/Modules/Auth/types";
import navigateHome from "src/app/Utils/NavigateHome";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {Google, EntregaDigna} from "src/assets/Icons/Flaticon";
import logo from "src/assets/Images/SETRE_Logo_Horizontal.png";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {IAuthService} from "src/business/Interfaces/Services/IAuth";
import applicationSingleton from "src/business/Singletons/Application";
import {
  COGNITO_LOGOUT_URL,
  GOOGLE_SIGNIN_URL,
} from "src/infrastructure/constants";

type Props = NativeStackScreenProps<AuthAppStackParamList, "SignIn">;

const SignIn = ({navigation}: Props) => {
  const styles = customStyles();

  const {
    auth: {signin: resources},
  } = useTranslation();

  useEffect(() => {
    const linkingSubscription = Linking.addEventListener(
      "url",
      async ({url}) => {
        if (url.includes("code")) {
          const code = decodeURI(url.split("code=")[1]);

          const authService = container.get<IAuthService>(TOKENS.AuthService);

          const response = await authService.exchangeCode(code);

          if (response.id !== "" && !response.isDeleted) {
            navigateHome(true);
          }

          if (response.id !== "" && response.isDeleted) {
            if (
              response.isDeleted &&
              applicationSingleton.decodedIdToken?.email
            ) {
              rootNavigation("AuthApp", {
                screen: "ReactivateAccount",
                params: {email: applicationSingleton.decodedIdToken?.email},
              });
            }

            return;
          }

          if (response.id === "") {
            rootNavigation("AuthApp", {screen: "SocialSignUp"});
          }
        }
      },
    );

    return () => {
      linkingSubscription.remove();
    };
  }, []);

  const handleGoogleSignIn = async () => {
    if (
      (await Linking.canOpenURL(COGNITO_LOGOUT_URL)) &&
      (await Linking.canOpenURL(GOOGLE_SIGNIN_URL))
    ) {
      await Linking.openURL(COGNITO_LOGOUT_URL);
      await Linking.openURL(GOOGLE_SIGNIN_URL);
    }
  };

  return (
    <View {...styles.mainView}>
      <Box {...styles.boxContainer}>
        <EntregaDigna {...styles.avatarLogo.style} />
        <Box {...styles.boxFormik}>
          <SignInForm />

          <Button
            variant="link"
            onPress={() => {
              navigation.navigate("UserRecoverPassword");
            }}
            {...styles.buttonForgotPassword}>
            {resources.button.forgotPassword}
          </Button>

          <HStack {...styles.hStack}>
            <Text>{resources.button.createAccount}</Text>
            <Button
              variant="link"
              onPress={() => {
                navigation.navigate("CheckEmailCpf");
              }}
              {...styles.buttonSignUp}>
              {resources.button.signup}
            </Button>
          </HStack>

          <Divider />

          <Button.Group {...styles.buttonGroup}>
            <Button
              variant="outline"
              borderRadius="100"
              borderWidth={2}
              height={16}
              leftIcon={<Google {...styles.googleIcon.style} />}
              onPress={handleGoogleSignIn}
              {...styles.button}>
              {resources.button.signInGoogle}
            </Button>
          </Button.Group>
        </Box>
      </Box>
      <View {...styles.mainView} />
      <Box {...styles.boxLogoSetre}>
        <Image
          source={logo}
          alt="Logo SETRE"
          resizeMode="contain"
          {...(styles.imageLogoSetre as InterfaceImageProps)}
        />
      </Box>
    </View>
  );
};

export default SignIn;
