import {BRL_CPF, BRL_PHONE_CELL, Input} from "input-mask-native-base-rhf";
import {
  Checkbox,
  IconButton,
  ScrollView,
  Text,
  View,
  VStack,
} from "native-base";
import React, {useState} from "react";
import {useFormContext} from "react-hook-form";
import InputOrderTab from "src/app/Components/InputOrderTab";
import DatePickerCustom from "src/app/Components/NativeBase/DatePicker";
import useTranslation from "src/app/Hooks/useTranslation";
import formSettings from "src/app/Modules/Auth/Presentation/SignUp/Components/Form//formSettings";
import customStyles from "src/app/Modules/Auth/Presentation/SignUp/Components/Form/styles";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import {Eye, EyeOff} from "src/assets/Icons/Flaticon";
import {IFormUser} from "src/business/DTOs/Forms/User";

const SignUpForm = () => {
  const {
    setValue,
    watch,
    formState: {errors},
    setFocus,
  } = useFormContext<IFormUser>();
  const resources = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const styles = customStyles();
  const {
    users: {create: userResources},
  } = resources;

  const contactByEmailValue = watch("contactByEmail", false);
  const contactBySmsValue = watch("contactBySms", false);
  const contactByWhatsappValue = watch("contactByWhatsapp", false);

  return (
    <ScrollView>
      <View {...styles.vStack}>
        <InputOrderTab setFocus={setFocus}>
          <Input
            helperMessage={userResources.toasts.email}
            name="email"
            autoCapitalize="none"
            label={userResources.placeholder.email}
            errorMessage={errors.email?.message}
            type="text"
            placeholder="<EMAIL>"
            maxLength={formSettings.fieldLength.email.max}
            isRequired={true}
            isReadOnly={true}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth="2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />
          <Input
            helperMessage={userResources.toasts.name}
            name="firstName"
            label={userResources.placeholder.firstName}
            errorMessage={errors.firstName?.message}
            type="text"
            placeholder={userResources.placeholder.firstName}
            maxLength={formSettings.fieldLength.firstName.max}
            isRequired={true}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth="2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />

          <Input
            helperMessage={userResources.toasts.name}
            name="lastName"
            label={userResources.placeholder.lastName}
            errorMessage={errors.lastName?.message}
            type="text"
            placeholder={userResources.placeholder.lastName}
            maxLength={formSettings.fieldLength.lastName.max}
            isRequired={true}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth="2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />
          <Input
            helperMessage={userResources.toasts.phone}
            name="phone"
            label={userResources.label.phone}
            errorMessage={errors.phone?.message}
            type="text"
            placeholder={userResources.placeholder.phone}
            keyboardType="numeric"
            maxLength={formSettings.fieldLength.phone.max}
            mask={BRL_PHONE_CELL}
            isRequired={true}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth="2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />
          <Input
            helperMessage={userResources.toasts.cpf}
            name="cpf"
            label={userResources.label.cpf}
            errorMessage={errors.cpf?.message}
            type="text"
            placeholder={userResources.placeholder.cpf}
            keyboardType="numeric"
            mask={BRL_CPF}
            isRequired={true}
            isReadOnly={true}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth="2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
          />
          <DatePickerCustom
            name="dateOfBirth"
            label={userResources.label.dateOfBirth}
            errorMessage={errors.dateOfBirth?.message}
          />

          <VStack space={2} {...styles.vStackCheckboxes}>
            <Text {...styles.textCheckbox}>
              {userResources.label.contact_preference}
            </Text>
            <Checkbox
              name="contactByEmail"
              value="true"
              defaultIsChecked={contactByEmailValue}
              onChange={value => {
                setValue("contactByEmail", value);
              }}
              _text={{...styles.textCheckOptions}}>
              {userResources.label.email}
            </Checkbox>
            <Checkbox
              name="contactBySms"
              value="true"
              defaultIsChecked={contactBySmsValue}
              onChange={value => {
                setValue("contactBySms", value);
              }}
              _text={{...styles.textCheckOptions}}>
              {userResources.label.sms}
            </Checkbox>
            <Checkbox
              name="contactByWhatsapp"
              value="true"
              defaultIsChecked={contactByWhatsappValue}
              onChange={value => {
                setValue("contactByWhatsapp", value);
              }}
              _text={{...styles.textCheckOptions}}>
              {userResources.label.whatsapp}
            </Checkbox>
          </VStack>

          <Input
            helperMessage={userResources.toasts.password}
            name="password"
            label={userResources.placeholder.password}
            errorMessage={errors.password?.message}
            placeholder="************"
            type={showPassword ? "text" : "password"}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth="2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
            InputRightElement={
              <IconButton
                variant="unstyled"
                icon={
                  showPassword ? (
                    <Eye {...styles.iconPassword.style} />
                  ) : (
                    <EyeOff {...styles.iconPassword.style} />
                  )
                }
                onPress={() => setShowPassword(!showPassword)}
              />
            }
            maxLength={formSettings.fieldLength.password.max}
            infoLabel={{
              title: userResources.label.passwordRequirements,
              message: setResourceParameters(
                userResources.label.passTrackerContain,
                formSettings.fieldLength.password.min,
              ),
            }}
            isRequired={true}
          />

          <Input
            helperMessage={userResources.toasts.passwordConfirm}
            name="passwordConfirmation"
            label={userResources.placeholder.passwordConfirm}
            errorMessage={errors.passwordConfirmation?.message}
            placeholder={userResources.placeholder.retypePass}
            type={showPasswordConfirm ? "text" : "password"}
            height={60}
            size="md"
            borderRadius="lg"
            backgroundColor="gray.100"
            padding={3}
            fontSize="md"
            borderWidth="2"
            _focus={{
              borderColor: "#95c11f",
              backgroundColor: "gray.200",
            }}
            InputRightElement={
              <IconButton
                variant="unstyled"
                icon={
                  showPasswordConfirm ? (
                    <Eye {...styles.iconPassword.style} />
                  ) : (
                    <EyeOff {...styles.iconPassword.style} />
                  )
                }
                onPress={() => setShowPasswordConfirm(!showPasswordConfirm)}
              />
            }
            maxLength={formSettings.fieldLength.password.max}
            isRequired={true}
          />
        </InputOrderTab>
      </View>
    </ScrollView>
  );
};

export default SignUpForm;
