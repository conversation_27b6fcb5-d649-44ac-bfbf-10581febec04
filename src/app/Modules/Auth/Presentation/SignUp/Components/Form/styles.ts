import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { moderateScale } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    vStack: {
      style: {
        paddingHorizontal: wp("5%"),
        marginTop: wp("5%")
      },
    },

    iconPassword: {
      style: {
        marginRight: wp("2%"),
        width: moderateScale(20),
        height: moderateScale(20),
        fill: ThemesApp.getTheme().colors.muted[500],
      },
    },

    buttonAddress: {
      style: {
        marginBottom: wp("4%"),
      },
    },

    buttonRegister: {
      style: {
        marginTop: wp("2%"),
      },
    },
    vStackCheckboxes: {
      style: {
        marginBottom: wp("5%"),
      },
    },
    textCheckbox: {
      style: {
        marginBottom: wp("3%"),
        
      },
    },
    textCheckOptions: {
      fontSize: RFValue(14),
      lineHeight: RFValue(16),
      style: {
        color: ThemesApp.getTheme().colors.gray[500],
      },
    },
  };
};

export default customStyles;
