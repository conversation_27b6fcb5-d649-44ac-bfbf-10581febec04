import { IFormCreateAddress } from "src/business/DTOs/Forms/Create/Address";
import { IFormUser } from "src/business/DTOs/Forms/User";

export const userFormInitialValues: IFormUser = {
  firstName: "",
  lastName: "",
  phone: "",
  cpf: "",
  email: "",
  password: "",
  passwordConfirmation: "",
  contactByEmail: false,
  contactBySms: false,
  contactByWhatsapp: false,
  dateOfBirth: "",
};

export const addressFormInitialValues: IFormCreateAddress = {
  street: "",
  number: "",
  complement: "",
  district: "",
  country: "",
  city: "",
  state: "",
  postcode: "",
  nickname: "",
  isDefault: true,
  setOnMap: false,
  latitude: 0,
  longitude: 0,
};
