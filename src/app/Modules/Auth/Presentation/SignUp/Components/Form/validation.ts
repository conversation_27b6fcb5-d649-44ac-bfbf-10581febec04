import {Resources} from "src/app/Context/Utils/Resources";
import formSettings from "src/app/Modules/Auth/Presentation/SignUp/Components/Form/formSettings";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import validateCPF from "src/app/Utils/ValidateCPF";
import {validateDateByAge} from "src/app/Utils/ValidateDate";
import ZodStringRequired from "src/app/Utils/Zod/ZodStringRequired";
import configAppSingleton from "src/business/Singletons/ConfigApp";
import {z} from "zod";

const getSchemaCreateUser = () => {
  const resources = Resources.get();
  const {ageAllowed} = configAppSingleton;

  const schema = {
    firstName: ZodStringRequired().max(formSettings.fieldLength.firstName.max, {
      message: setResourceParameters(
        resources.generic.errors.long_field,
        formSettings.fieldLength.firstName.max,
      ),
    }),
    lastName: ZodStringRequired().max(formSettings.fieldLength.lastName.max, {
      message: setResourceParameters(
        resources.generic.errors.short_field,
        formSettings.fieldLength.lastName.max,
      ),
    }),
    phone: ZodStringRequired().min(formSettings.fieldLength.phone.min, {
      message: setResourceParameters(
        resources.generic.errors.phone_short_field,
        formSettings.fieldLength.phone.min,
      ),
    }),
    cpf: ZodStringRequired()
      .min(formSettings.fieldLength.cpf.min, {
        message: resources.generic.errors.invalid,
      })
      .refine(item => validateCPF(item), {
        message: resources.generic.errors.invalid,
      }),
    email: ZodStringRequired()
      .email({
        message: resources.generic.errors.email,
      })
      .max(formSettings.fieldLength.email.max, {
        message: setResourceParameters(
          resources.generic.errors.long_field,
          formSettings.fieldLength.email.max,
        ),
      }),
    password: ZodStringRequired()
      .min(formSettings.fieldLength.password.min, {
        message: setResourceParameters(
          resources.generic.errors.passwordMinSix,
          formSettings.fieldLength.password.min,
        ),
      })
      .max(formSettings.fieldLength.password.max, {
        message: setResourceParameters(
          resources.generic.errors.long_field,
          formSettings.fieldLength.password.max,
        ),
      })
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*?.])(?=.{8,})/, {
        message: resources.generic.errors.unMatch_rules,
      }),
    passwordConfirmation: ZodStringRequired()
      .min(formSettings.fieldLength.password.min, {
        message: setResourceParameters(
          resources.generic.errors.passwordMinSix,
          formSettings.fieldLength.password.min,
        ),
      })
      .max(formSettings.fieldLength.password.max, {
        message: setResourceParameters(
          resources.generic.errors.long_field,
          formSettings.fieldLength.password.max,
        ),
      })
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*?.])(?=.{8,})/, {
        message: resources.generic.errors.unMatch_rules,
      }),
    contactByEmail: z.boolean().default(false),
    contactBySms: z.boolean().default(false),
    contactByWhatsapp: z.boolean().default(false),
    dateOfBirth: ZodStringRequired().refine(
      data => {
        const isValidDate = validateDateByAge(data, ageAllowed);
        return isValidDate;
      },
      {
        message: setResourceParameters(
          resources.generic.errors.more_than_years,
          ageAllowed,
        ),
      },
    ),
  };

  return schema;
};

export default getSchemaCreateUser;
