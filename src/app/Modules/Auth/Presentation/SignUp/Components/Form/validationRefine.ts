import { Resources } from "src/app/Context/Utils/Resources";
import { IFormUser } from "src/business/DTOs/Forms/User";
import { RefinementCtx, z } from "zod";

const userPasswordValidation = (data: IFormUser, context: RefinementCtx) => {
  const resources = Resources.get();
  if (data.password !== data.passwordConfirmation) {
    context.addIssue({
      code: z.ZodIssueCode.custom,
      message: resources.generic.errors.password_match,
      path: ["passwordConfirmation"],
    });
  }
};

const userProfileValidation = (data: any, context: RefinementCtx) => {
  const resources = Resources.get();
  if (data.profileList === undefined) {
    context.addIssue({
      code: z.ZodIssueCode.custom,
      message: resources.generic.errors.profileList,
      path: ["profileList"],
    });
  }
  if (data.profileList){
    if (data.profileList.length === 0) {
      context.addIssue({
        code: z.ZodIssueCode.custom,
        message: resources.generic.errors.profileList,
        path: ["profileList"],
      });
    }
  }
};

export {userPasswordValidation, userProfileValidation};
