import {NativeStackScreenProps} from "@react-navigation/native-stack";
import React, {useCallback, useMemo, useState} from "react";
import MultiStep, {StepList} from "src/app/Components/Forms/Multistep";
import StackHeader from "src/app/Components/Header/StackHeader";
import ModalProgress from "src/app/Components/ModalProgress";
import showToastSuccess from "src/app/Components/Toast/toastSuccess";
import {Resources} from "src/app/Context/Utils/Resources";
import SignUpForm from "src/app/Modules/Auth/Presentation/SignUp/Components/Form";
import {
  addressFormInitialValues,
  userFormInitialValues,
} from "src/app/Modules/Auth/Presentation/SignUp/Components/Form/formInitialValues";
import schemaCreateUser from "src/app/Modules/Auth/Presentation/SignUp/validation";
import {AuthAppStackParamList} from "src/app/Modules/Auth/types";
import AddressFormBase from "src/app/Modules/Main/Address/Components/FormBase";
import ChooseProfile from "src/app/Modules/Main/Users/<USER>/ChooseProfile";
import useCreateUser from "src/app/Modules/Main/Users/<USER>/useCreateUser";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {IFormCreateUserDTO} from "src/business/DTOs/Forms/Create/User";

type Props = NativeStackScreenProps<AuthAppStackParamList, "SignUp">;

const SignUp = ({navigation, route}: Props) => {
  const [hideButton, setHideButton] = useState<boolean>(false);

  const resources = Resources.get();

  const {selectAddressLocationMessage} = resources.address.create.text;

  const createUserMutation = useCreateUser();

  const onSubmit = async (
    formData: IFormCreateUserDTO,
    onFinish: () => void,
    onSuccess: () => void,
  ) => {
    createUserMutation.mutate(formData, {
      onSuccess: () => {
        rootNavigation("AuthApp", {screen: "SignIn"});
        showToastSuccess(resources.users.create.toasts.create_success);

        onSuccess();
      },
      onSettled: onFinish,
    });
  };

  const handleHideButton = useCallback(() => {
    setHideButton(true);
  }, []);

  const handleShowButton = useCallback(() => {
    setHideButton(false);
  }, []);

  const steps = useMemo(() => {
    const result: StepList[] = [
      {
        step: <SignUpForm />,
        isOptional: false,
        initialValues: {
          ...userFormInitialValues,
          email: route.params?.email || "",
          cpf: route.params?.cpf || "",
        },
      },
      {
        step: (
          <AddressFormBase
            onMapIsClose={handleShowButton}
            onMapIsOpen={handleHideButton}
            onMapOpenInfoMessage={selectAddressLocationMessage}
          />
        ),
        isOptional: false,
        initialValues: addressFormInitialValues,
      },
      {step: <ChooseProfile />, isOptional: false, initialValues: undefined},
    ];
    return result;
  }, [
    handleHideButton,
    handleShowButton,
    route.params?.email,
    route.params?.cpf,
    selectAddressLocationMessage,
  ]);

  return (
    <>
      <StackHeader withMultistep navigation={navigation} route={route} />
      {createUserMutation.isPending && <ModalProgress />}
      <MultiStep
        steps={steps}
        hideButtons={hideButton}
        schema={schemaCreateUser()}
        onSubmit={onSubmit}
      />
    </>
  );
};
export default SignUp;
