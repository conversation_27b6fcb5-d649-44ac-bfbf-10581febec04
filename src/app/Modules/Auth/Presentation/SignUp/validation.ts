import getSchemaCreateClient from "src/app/Components/Forms/Client/validation";
import getSchemaCreateDeliveryman from "src/app/Components/Forms/Deliveryman/validation";
import getSchemaCreateShopkeeper from "src/app/Components/Forms/Shopkeeper/validation";
import getSchemaCreateUser from "src/app/Modules/Auth/Presentation/SignUp/Components/Form/validation";
import { userPasswordValidation } from "src/app/Modules/Auth/Presentation/SignUp/Components/Form/validationRefine";
import getSchemaCreateAddress from "src/app/Modules/Main/Address/Components/FormBase/validation";
import { z } from "zod";

const schemaCreateUser = () => {
  const schemaUser = getSchemaCreateUser();
  const schemaAddress = getSchemaCreateAddress();
  const schemaShopkeeper = getSchemaCreateShopkeeper();
  const schemaDeliveryman = getSchemaCreateDeliveryman();
  const schemaClient = getSchemaCreateClient();

  const schema = z
    .discriminatedUnion("formStep", [
      z.object({ formStep: z.literal("0"), ...schemaUser }),
      z.object({ formStep: z.literal("1"), ...schemaAddress }),
      z.object({ formStep: z.literal("2"), ...schemaShopkeeper }),
      z.object({ formStep: z.literal("3"), ...schemaDeliveryman }),
      z.object({ formStep: z.literal("4"), ...schemaClient }),
    ])
    .superRefine((data, context) => {
      if (data.formStep === "0") {
        userPasswordValidation(data, context);
      }
      // if (data.formStep === "2") {
      //   userProfileValidation(data, context);
      // }
      // if (data.formStep === "3") {
      //   userProfileValidation(data, context);
      // }
      // if (data.formStep === "4") {
      //   userProfileValidation(data, context);
      // }
    });

  return schema;
};

export default schemaCreateUser;
