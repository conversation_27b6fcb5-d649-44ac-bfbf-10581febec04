import {Resources} from "src/app/Context/Utils/Resources";
import formSettings from "src/app/Modules/Auth/Presentation/SocialSignUp/Components/Form/formSettings";
import setResourceParameters from "src/app/Utils/SetResourceParameters";
import validateCPF from "src/app/Utils/ValidateCPF";
import {validateDateByAge} from "src/app/Utils/ValidateDate";
import ZodString from "src/app/Utils/Zod/ZodString";
import ZodStringRequired from "src/app/Utils/Zod/ZodStringRequired";
import configAppSingleton from "src/business/Singletons/ConfigApp";
import {z} from "zod";

const getSchemaSocialSignUp = () => {
  const resources = Resources.get();
  const {ageAllowed} = configAppSingleton;

  const schema = z.object({
    firstName: ZodString().min(formSettings.fieldLength.firstName.min, {
      message: setResourceParameters(
        resources.generic.errors.short_field,
        formSettings.fieldLength.firstName.min,
      ),
    }),
    lastName: ZodString().min(formSettings.fieldLength.lastName.min, {
      message: setResourceParameters(
        resources.generic.errors.short_field,
        formSettings.fieldLength.lastName.min,
      ),
    }),
    phone: ZodString().min(formSettings.fieldLength.phone.min, {
      message: setResourceParameters(
        resources.generic.errors.phone_short_field,
        formSettings.fieldLength.phone.min,
      ),
    }),
    email: ZodString().email({
      message: resources.generic.errors.email,
    }),
    cpf: ZodStringRequired()
      .min(formSettings.fieldLength.cpf.min, {
        message: resources.generic.errors.invalid,
      })
      .refine(item => validateCPF(item), {
        message: resources.generic.errors.invalid,
      }),
    dateOfBirth: ZodStringRequired().refine(
      data => {
        const isValidDate = validateDateByAge(data, ageAllowed);
        return isValidDate;
      },
      {
        message: setResourceParameters(
          resources.generic.errors.more_than_years,
          ageAllowed,
        ),
      },
    ),
  });

  return schema;
};

export default getSchemaSocialSignUp;
