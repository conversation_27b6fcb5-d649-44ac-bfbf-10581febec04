import {zod<PERSON>esolver} from "@hookform/resolvers/zod";
import {BRL_CPF, BRL_PHONE_CELL, Input} from "input-mask-native-base-rhf";
import {Button, VStack} from "native-base";
import React from "react";
import {FormProvider} from "react-hook-form";
import {Keyboard} from "react-native";
import {useFormBase} from "src/app/Components/FormBase";
import InputOrderTab from "src/app/Components/InputOrderTab";
import DatePickerCustom from "src/app/Components/NativeBase/DatePicker";
import useTranslation from "src/app/Hooks/useTranslation";
import formSettings from "src/app/Modules/Auth/Presentation/SocialSignUp/Components/Form/formSettings";
import customStyles from "src/app/Modules/Auth/Presentation/SocialSignUp/Components/Form/styles";
import getSchemaSocialSignUp from "src/app/Modules/Auth/Presentation/SocialSignUp/Components/Form/validation";
import useCreateSocialLogin from "src/app/Modules/Auth/Query/useCreateSocialLogin";
import {IFormSocialSignUp} from "src/business/DTOs/Forms/Create/GoogleUser";
import applicationSingleton from "src/business/Singletons/Application";

const SocialSignUpForm = () => {
  const styles = customStyles();
  const {methods, handleSubmitFormBase} = useFormBase<IFormSocialSignUp>({
    initialValues: {
      firstName: applicationSingleton.decodedIdToken?.given_name as string, // REVIEW |-> social login working? <-|
      lastName: applicationSingleton.decodedIdToken?.family_name as string,
      phone: applicationSingleton.decodedIdToken?.phone_number as string,
      cpf: "",
      email: applicationSingleton.decodedIdToken?.email,
    },
    mode: "onChange",
    resolver: zodResolver(getSchemaSocialSignUp()),
  });

  const {
    formState: {errors},
    setFocus,
  } = methods;

  const resources = useTranslation();

  const createSocialLoginMutation = useCreateSocialLogin();

  const {
    users: {create: userResources},
  } = resources;

  const onSubmit = async (data: IFormSocialSignUp) => {
    Keyboard.dismiss();

    if (applicationSingleton.decodedIdToken?.sub) {
      data.cognitoIdGoogle = applicationSingleton.decodedIdToken?.sub;
    }

    createSocialLoginMutation.mutate(data);
  };

  return (
    <VStack {...styles.vStack}>
      <FormProvider {...methods}>
        <InputOrderTab setFocus={setFocus}>
          <Input
            helperMessage={userResources.toasts.name}
            name="firstName"
            label={userResources.placeholder.firstName}
            errorMessage={errors.firstName?.message}
            type="text"
            placeholder={userResources.placeholder.firstName}
            maxLength={formSettings.fieldLength.firstName.max}
            isRequired={true}
          />
          <Input
            helperMessage={userResources.toasts.name}
            name="lastName"
            label={userResources.placeholder.lastName}
            errorMessage={errors.lastName?.message}
            type="text"
            placeholder={userResources.placeholder.lastName}
            maxLength={formSettings.fieldLength.lastName.max}
            isRequired={true}
          />
          <Input
            helperMessage={userResources.toasts.phone}
            name="phone"
            label={userResources.label.phone}
            errorMessage={errors.phone?.message}
            type="text"
            placeholder={userResources.placeholder.phone}
            keyboardType="numeric"
            maxLength={formSettings.fieldLength.phone.max}
            mask={BRL_PHONE_CELL}
            isRequired={true}
          />
          <Input
            helperMessage={userResources.toasts.phone}
            name="cpf"
            label={userResources.label.cpf}
            errorMessage={errors.cpf?.message}
            type="text"
            placeholder={userResources.placeholder.cpf}
            keyboardType="numeric"
            mask={BRL_CPF}
            isRequired={true}
          />
          <DatePickerCustom
            name="dateOfBirth"
            label={userResources.label.dateOfBirth}
            errorMessage={errors.dateOfBirth?.message}
          />
          <Input
            helperMessage={userResources.toasts.email}
            name="email"
            autoCapitalize="none"
            label={userResources.placeholder.email}
            errorMessage={errors.email?.message}
            type="text"
            placeholder="<EMAIL>"
            maxLength={formSettings.fieldLength.email.max}
            isRequired={true}
            isDisabled
          />
          <Button
            onPress={() => handleSubmitFormBase(onSubmit)}
            isLoading={createSocialLoginMutation.isPending}
            {...styles.buttonRegister}>
            {userResources.button.register}
          </Button>
        </InputOrderTab>
      </FormProvider>
    </VStack>
  );
};

export default SocialSignUpForm;
