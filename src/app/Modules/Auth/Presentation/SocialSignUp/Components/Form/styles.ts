import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    vStack: {
      style: {
        marginHorizontal: wp("4%"),
      },
    },

    iconPassword: {
      style: {
        marginRight: wp("2%"),
        color: ThemesApp.getTheme().colors.muted[500],
      },
    },

    buttonAddress: {
      style: {
        marginBottom: wp("4%"),
      },
    },

    buttonRegister: {
      style: {
        marginTop: wp("2%"),
      },
    },
    vStackCheckboxes: {
      style: {
        marginBottom: wp("4%"),
      },
    },
    textCheckbox: {
      style: {
        marginBottom: wp("3%"),
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
  };
};

export default customStyles;
