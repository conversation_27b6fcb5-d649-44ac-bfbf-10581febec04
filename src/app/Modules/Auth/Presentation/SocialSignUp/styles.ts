import { RFValue } from "react-native-responsive-fontsize";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { verticalScale, horizontalScale, widthPercentage as wp } from "src/app/Utils/Metrics";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    hStack: {
      style: {
        alignItems: "center",
        marginHorizontal: wp("2%"),
        marginVertical: wp("3%"),
        padding: wp("1%"),
        borderRadius: horizontalScale(5),
        backgroundColor: ThemesApp.getTheme().colors.orange[200],
        flex: 1,
      },
    },
    icon: {
      style: {
        fill: ThemesApp.getTheme().colors.secondary[200],
        height: verticalScale(20),
        width: horizontalScale(20),
      },
    },
    title: {
      style: {
        fontSize: RFValue(12),
        fontWeight: "bold",
        textAlign: "justify",
        flex: 1,
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
  };
};

export default customStyles;
