import React, {useEffect} from "react";

import {HStack, Stack, Text} from "native-base";

import {NativeStackScreenProps} from "@react-navigation/native-stack";
import {BackHand<PERSON>, ScrollView} from "react-native";
import StackHeader from "src/app/Components/Header/StackHeader";
import useTranslation from "src/app/Hooks/useTranslation";
import SocialSignUpForm from "src/app/Modules/Auth/Presentation/SocialSignUp/Components/Form";
import customStyles from "src/app/Modules/Auth/Presentation/SocialSignUp/styles";
import {AuthAppStackParamList} from "src/app/Modules/Auth/types";
import {rootNavigation} from "src/app/Utils/RootNavigation";
import {WarningTriangle} from "src/assets/Icons/Flaticon";
import applicationSingleton from "src/business/Singletons/Application";

type Props = NativeStackScreenProps<AuthAppStackParamList, "SocialSignUp">;

const SocialSignUp: React.FC<Props> = ({navigation, route}) => {
  const styles = customStyles();
  const {users: resources} = useTranslation();

  const handleBackAction = () => {
    applicationSingleton.authenticationResult = undefined;
    applicationSingleton.decodedIdToken = undefined;
    rootNavigation("AuthApp", {screen: "SignIn"});
    return true;
  };

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      handleBackAction,
    );
    return () => backHandler.remove();
  }, []);

  return (
    <>
      <StackHeader
        navigation={navigation}
        route={route}
        overrideBackBehavior={handleBackAction}
      />
      <ScrollView>
        <Stack>
          <HStack space={2} {...styles.hStack}>
            <WarningTriangle {...styles.icon.style} />
            <Text {...styles.title} numberOfLines={2} ellipsizeMode="clip">
              {resources.create.text.complete_register}
            </Text>
          </HStack>
          <SocialSignUpForm />
        </Stack>
      </ScrollView>
    </>
  );
};
export default SocialSignUp;
