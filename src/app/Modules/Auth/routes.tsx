import { createNativeStackNavigator } from "@react-navigation/native-stack";
import React from "react";
import useTranslation from "src/app/Hooks/useTranslation";
import CheckEmailCpf from "src/app/Modules/Auth/Presentation/CheckEmailCpf";
import ReactivateAccount from "src/app/Modules/Auth/Presentation/ReactivateAccount";
import SignIn from "src/app/Modules/Auth/Presentation/SignIn";
import SignUp from "src/app/Modules/Auth/Presentation/SignUp";
import SocialSignUp from "src/app/Modules/Auth/Presentation/SocialSignUp";
import { AuthAppStackParamList } from "src/app/Modules/Auth/types";
import RecoverPassword from "src/app/Modules/Main/Users/<USER>/RecoverPassword";

const Stack = createNativeStackNavigator<AuthAppStackParamList>();

const AuthApp = () => {
  const {
    auth: { signin },
  } = useTranslation();
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="SignIn"
        component={SignIn}
        options={() => ({
          title: signin.header.tittle,
          headerShown: false,
        })}
      />
      <Stack.Screen name="CheckEmailCpf" component={CheckEmailCpf} />
      <Stack.Screen name="SignUp" component={SignUp} />
      <Stack.Screen name="UserRecoverPassword" component={RecoverPassword} />
      <Stack.Screen name="SocialSignUp" component={SocialSignUp} />
      <Stack.Screen name="ReactivateAccount" component={ReactivateAccount} />
    </Stack.Navigator>
  );
};

export default AuthApp;
