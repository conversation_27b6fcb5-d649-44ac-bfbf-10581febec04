import {useMutation} from "@tanstack/react-query";
import handleQueryError from "src/app/Utils/HandleQueryError";
import navigateHome from "src/app/Utils/NavigateHome";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {IFormSocialSignUp} from "src/business/DTOs/Forms/Create/GoogleUser";
import {IAuthService} from "src/business/Interfaces/Services/IAuth";
import {IUserService} from "src/business/Interfaces/Services/IUser";

const useCreateSocialLogin = () => {
  const userService = container.get<IUserService>(TOKENS.UserService);
  const authService = container.get<IAuthService>(TOKENS.AuthService);

  const createSocialLoginMutation = useMutation({
    mutationFn: (data: IFormSocialSignUp) => {
      return userService.createSocialLoginUser(data);
    },
    onError: e => handleQueryError(e),
    onSuccess: async () => {
      await authService.saveAuthenticationResultOnLocalStorage();

      navigateHome(true);
    },
  });

  return createSocialLoginMutation;
};

export default useCreateSocialLogin;
