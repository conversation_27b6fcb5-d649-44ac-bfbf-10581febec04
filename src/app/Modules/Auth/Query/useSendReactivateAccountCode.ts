import {useMutation} from "@tanstack/react-query";
import handleQueryError from "src/app/Utils/HandleQueryError";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import IFormReactivateAccount from "src/business/DTOs/Forms/ReactivateAccount";
import {IAuthService} from "src/business/Interfaces/Services/IAuth";

const useSendReactivateAccountCode = () => {
  const authService = container.get<IAuthService>(TOKENS.AuthService);

  const sendReactivateAccountCodeMutation = useMutation({
    mutationFn: (data: IFormReactivateAccount) => {
      return authService.sendReactivateAccountCode(data);
    },
    onError: e => handleQueryError(e),
  });

  return sendReactivateAccountCodeMutation;
};

export default useSendReactivateAccountCode;
