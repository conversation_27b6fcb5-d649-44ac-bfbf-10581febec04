import {useMutation} from "@tanstack/react-query";
import handleQueryError from "src/app/Utils/HandleQueryError";
import TOKENS from "src/business/Config/Inversify/InjectionTokens";
import container from "src/business/Config/Inversify/InversifyConfig";
import {LoginUser} from "src/business/DTOs/LoginUser";
import {IAuthService} from "src/business/Interfaces/Services/IAuth";

const useSignIn = () => {
  const authService = container.get<IAuthService>(TOKENS.AuthService);

  const signInMutation = useMutation({
    mutationFn: (data: LoginUser) => {
      return authService.login(data);
    },
    onError: e => handleQueryError(e),
  });

  return signInMutation;
};

export default useSignIn;
