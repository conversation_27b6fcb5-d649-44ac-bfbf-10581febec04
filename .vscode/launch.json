{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Android",
      "request": "launch",
      "type": "reactnative",
      "cwd": "${workspaceFolder}",
      "platform": "android",
      "sourceMaps": true,
      "env": {
        "ENVFILE": ".env.development"
      }
    },
    { "name": "Attach to packager", "request": "attach", "type": "reactnative", "cwd": "${workspaceFolder}" },
    {
      "name": "Debug Android Hermes",
      "request": "launch",
      "type": "reactnativedirect",
      "cwd": "${workspaceFolder}",
      "platform": "android",
      "sourceMaps": true,
      "env": {
        "ENVFILE": ".env.development"
      }
    }
  ]
}
