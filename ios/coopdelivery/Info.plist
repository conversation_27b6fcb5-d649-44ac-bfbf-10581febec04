<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>Coop Delivery</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>coopdelivery</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>coopdelivery</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>coopdelivery</string>
			<string>frontend</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<string>NSLocationWhenInUseUsageDescription</string>
		<key>NSCameraUsageDescription</key>
		<string></string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<dict>
			<!-- Do not change NSAllowsArbitraryLoads to true, or you will risk app rejection! -->
			<key>NSAllowsArbitraryLoads</key>
			<false />
			<key>NSAllowsLocalNetworking</key>
			<true />
		</dict>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Location Permission</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string></string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string></string>
		<key>UIAppFonts</key>
		<array>
			<string>Roboto-Black.ttf</string>
			<string>Roboto-BlackItalic.ttf</string>
			<string>Roboto-Bold.ttf</string>
			<string>Roboto-BoldItalic.ttf</string>
			<string>Roboto-Italic.ttf</string>
			<string>Roboto-Light.ttf</string>
			<string>Roboto-LightItalic.ttf</string>
			<string>Roboto-Medium.ttf</string>
			<string>Roboto-MediumItalic.ttf</string>
			<string>Roboto-Regular.ttf</string>
			<string>Roboto-Thin.ttf</string>
			<string>Roboto-ThinItalic.ttf</string>
			<string>AntDesign.ttf</string>
			<string>Entypo.ttf</string>
			<string>EvilIcons.ttf</string>
			<string>Feather.ttf</string>
			<string>FontAwesome.ttf</string>
			<string>FontAwesome5_Brands.ttf</string>
			<string>FontAwesome5_Regular.ttf</string>
			<string>FontAwesome5_Solid.ttf</string>
			<string>Fontisto.ttf</string>
			<string>Foundation.ttf</string>
			<string>Ionicons.ttf</string>
			<string>MaterialCommunityIcons.ttf</string>
			<string>MaterialIcons.ttf</string>
			<string>Octicons.ttf</string>
			<string>SimpleLineIcons.ttf</string>
			<string>Zocial.ttf</string>
		</array>
		<key>UIBackgroundModes</key>
		<array>
			<string>processing</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		</array>
		<key>LSApplicationQueriesSchemes</key>
		<array>
				<string>comgooglemaps</string>
				<string>citymapper</string>
				<string>uber</string>
				<string>lyft</string>
				<string>transit</string>
				<string>truckmap</string>
				<string>waze</string>
				<string>yandexnavi</string>
				<string>moovit</string>
				<string>yandextaxi</string>
				<string>yandexmaps</string>
				<string>kakaomap</string>
				<string>tmap</string>
				<string>szn-mapy</string>
				<string>mapsme</string>
				<string>osmandmaps</string>
				<string>gett</string>
				<string>nmap</string>
				<string>dgis</string>
				<string>lftgpas</string>
				<string>sygic</string>
		</array>
	</dict>
</plist>