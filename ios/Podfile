# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip
require_relative '../node_modules/react-native-permissions/scripts/setup'

platform :ios, '13.4'
prepare_react_native_project!
 
linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end
 
production = ENV["PRODUCTION"] == "1"

setup_permissions([
   'AppTrackingTransparency',
   'Bluetooth',
   'Calendars',
   'CalendarsWriteOnly',
   'Camera',
   'Contacts',
   'FaceID',
   'LocationAccuracy',
   'LocationAlways',
   'LocationWhenInUse',
   'MediaLibrary',
   'Microphone',
   'Motion',
   'Notifications',
   'PhotoLibrary',
   'PhotoLibraryAddOnly',
   'Reminders',
   'Siri',
   'SpeechRecognition',
   'StoreKit',
])


# React Native Maps dependencies
# The following line is only needed if building on an Apple silicon Mac without rosetta.
pod 'Google-Maps-iOS-Utils', :git => 'https://github.com/Simon-TechForm/google-maps-ios-utils.git', :branch => 'feat/support-apple-silicon'

rn_maps_path = '../node_modules/react-native-maps'
pod 'react-native-google-maps', :path => rn_maps_path

pod 'react-native-video', :path => '../node_modules/react-native-video/react-native-video.podspec'

target 'coopdelivery' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'

  target 'coopdeliveryTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
