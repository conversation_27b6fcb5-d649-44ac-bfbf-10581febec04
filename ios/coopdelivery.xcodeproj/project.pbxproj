// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* coopdeliveryTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* coopdeliveryTests.m */; };
		0F85D48EB0267D8ABE881C7B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 1C8BD138097C4ACB10D7C23C /* PrivacyInfo.xcprivacy */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		303662DC3949120B842A0138 /* libPods-coopdelivery-coopdeliveryTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 957A7AEE89D7B04961BBBB33 /* libPods-coopdelivery-coopdeliveryTests.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		9CA5D3E92B9F93DA0037ECE4 /* Roboto-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3E82B9F93DA0037ECE4 /* Roboto-BlackItalic.ttf */; };
		9CA5D3EA2B9F93DA0037ECE4 /* Roboto-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3E82B9F93DA0037ECE4 /* Roboto-BlackItalic.ttf */; };
		9CA5D3EC2B9F93E70037ECE4 /* Roboto-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3EB2B9F93E70037ECE4 /* Roboto-Black.ttf */; };
		9CA5D3ED2B9F93E70037ECE4 /* Roboto-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3EB2B9F93E70037ECE4 /* Roboto-Black.ttf */; };
		9CA5D3EF2B9F93F30037ECE4 /* Roboto-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3EE2B9F93F30037ECE4 /* Roboto-BoldItalic.ttf */; };
		9CA5D3F02B9F93F30037ECE4 /* Roboto-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3EE2B9F93F30037ECE4 /* Roboto-BoldItalic.ttf */; };
		9CA5D3F22B9F93FD0037ECE4 /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3F12B9F93FD0037ECE4 /* Roboto-Bold.ttf */; };
		9CA5D3F32B9F93FD0037ECE4 /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3F12B9F93FD0037ECE4 /* Roboto-Bold.ttf */; };
		9CA5D3F52B9F94070037ECE4 /* Roboto-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3F42B9F94070037ECE4 /* Roboto-MediumItalic.ttf */; };
		9CA5D3F62B9F94070037ECE4 /* Roboto-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3F42B9F94070037ECE4 /* Roboto-MediumItalic.ttf */; };
		9CA5D3F82B9F94130037ECE4 /* Roboto-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3F72B9F94130037ECE4 /* Roboto-Medium.ttf */; };
		9CA5D3F92B9F94130037ECE4 /* Roboto-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3F72B9F94130037ECE4 /* Roboto-Medium.ttf */; };
		9CA5D3FB2B9F941B0037ECE4 /* Roboto-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3FA2B9F941B0037ECE4 /* Roboto-Italic.ttf */; };
		9CA5D3FC2B9F941B0037ECE4 /* Roboto-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3FA2B9F941B0037ECE4 /* Roboto-Italic.ttf */; };
		9CA5D3FE2B9F94230037ECE4 /* Roboto-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3FD2B9F94230037ECE4 /* Roboto-Regular.ttf */; };
		9CA5D3FF2B9F94230037ECE4 /* Roboto-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D3FD2B9F94230037ECE4 /* Roboto-Regular.ttf */; };
		9CA5D4012B9F942C0037ECE4 /* Roboto-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D4002B9F942C0037ECE4 /* Roboto-LightItalic.ttf */; };
		9CA5D4022B9F942C0037ECE4 /* Roboto-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D4002B9F942C0037ECE4 /* Roboto-LightItalic.ttf */; };
		9CA5D4042B9F94360037ECE4 /* Roboto-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D4032B9F94350037ECE4 /* Roboto-Light.ttf */; };
		9CA5D4052B9F94360037ECE4 /* Roboto-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D4032B9F94350037ECE4 /* Roboto-Light.ttf */; };
		9CA5D4072B9F943D0037ECE4 /* Roboto-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D4062B9F943D0037ECE4 /* Roboto-ThinItalic.ttf */; };
		9CA5D4082B9F943D0037ECE4 /* Roboto-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D4062B9F943D0037ECE4 /* Roboto-ThinItalic.ttf */; };
		9CA5D40A2B9F94440037ECE4 /* Roboto-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D4092B9F94440037ECE4 /* Roboto-Thin.ttf */; };
		9CA5D40B2B9F94440037ECE4 /* Roboto-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9CA5D4092B9F94440037ECE4 /* Roboto-Thin.ttf */; };
		E5ABF284B37F1AF06E5A4124 /* libPods-coopdelivery.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BF989609C5E82718FB0504E4 /* libPods-coopdelivery.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = coopdelivery;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* coopdeliveryTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = coopdeliveryTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* coopdeliveryTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = coopdeliveryTests.m; sourceTree = "<group>"; };
		06949B3118454AAD90435C12 /* MaterialIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; };
		1338619E3D5343EF9A2567CC /* EvilIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = EvilIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* coopdelivery.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = coopdelivery.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = coopdelivery/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.mm; path = coopdelivery/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = coopdelivery/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = coopdelivery/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = coopdelivery/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = coopdelivery/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		1C8BD138097C4ACB10D7C23C /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = coopdelivery/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		21926E7F8C5140A7A56581FB /* Feather.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Feather.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Feather.ttf"; sourceTree = "<group>"; };
		30FAAA0AAC5A491DB08A1C78 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Brands.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; };
		401F2450F48B43E7BAC992BA /* FontAwesome.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; };
		53BE1F2AF0B24A41AC2BDE06 /* AntDesign.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = AntDesign.ttf; path = "../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf"; sourceTree = "<group>"; };
		5468BCC7988640BD8F5D02CF /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SimpleLineIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; };
		5732627D2E544819B50AEF1D /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialCommunityIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; };
		5E1AD404D68D4E1EAA25F3B5 /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Regular.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; };
		669A1A623B3AAD8DB7B7DE49 /* Pods-coopdelivery.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-coopdelivery.debug.xcconfig"; path = "Target Support Files/Pods-coopdelivery/Pods-coopdelivery.debug.xcconfig"; sourceTree = "<group>"; };
		683709BD43AA4FC5A3E7A142 /* Octicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Octicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Octicons.ttf"; sourceTree = "<group>"; };
		79AF3FBC00C644E7B2FD8798 /* Foundation.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Foundation.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Foundation.ttf"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = coopdelivery/LaunchScreen.storyboard; sourceTree = "<group>"; };
		850AD79491C742DE8E1073C8 /* Entypo.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Entypo.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Entypo.ttf"; sourceTree = "<group>"; };
		88E36D4A925D421C99C86752 /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Solid.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; };
		957A7AEE89D7B04961BBBB33 /* libPods-coopdelivery-coopdeliveryTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-coopdelivery-coopdeliveryTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		9C67893211474202923A9104 /* Ionicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Ionicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		9CA5D3E82B9F93DA0037ECE4 /* Roboto-BlackItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-BlackItalic.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-BlackItalic.ttf"; sourceTree = "<group>"; };
		9CA5D3EB2B9F93E70037ECE4 /* Roboto-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-Black.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-Black.ttf"; sourceTree = "<group>"; };
		9CA5D3EE2B9F93F30037ECE4 /* Roboto-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-BoldItalic.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-BoldItalic.ttf"; sourceTree = "<group>"; };
		9CA5D3F12B9F93FD0037ECE4 /* Roboto-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-Bold.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-Bold.ttf"; sourceTree = "<group>"; };
		9CA5D3F42B9F94070037ECE4 /* Roboto-MediumItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-MediumItalic.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-MediumItalic.ttf"; sourceTree = "<group>"; };
		9CA5D3F72B9F94130037ECE4 /* Roboto-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-Medium.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-Medium.ttf"; sourceTree = "<group>"; };
		9CA5D3FA2B9F941B0037ECE4 /* Roboto-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-Italic.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-Italic.ttf"; sourceTree = "<group>"; };
		9CA5D3FD2B9F94230037ECE4 /* Roboto-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-Regular.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-Regular.ttf"; sourceTree = "<group>"; };
		9CA5D4002B9F942C0037ECE4 /* Roboto-LightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-LightItalic.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-LightItalic.ttf"; sourceTree = "<group>"; };
		9CA5D4032B9F94350037ECE4 /* Roboto-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-Light.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-Light.ttf"; sourceTree = "<group>"; };
		9CA5D4062B9F943D0037ECE4 /* Roboto-ThinItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-ThinItalic.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-ThinItalic.ttf"; sourceTree = "<group>"; };
		9CA5D4092B9F94440037ECE4 /* Roboto-Thin.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "Roboto-Thin.ttf"; path = "../../../../Downloads/Roboto-2/Roboto-Thin.ttf"; sourceTree = "<group>"; };
		9CD042452BD04FF000DD9A90 /* coopdeliveryDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = coopdeliveryDebug.entitlements; sourceTree = "<group>"; };
		BF989609C5E82718FB0504E4 /* libPods-coopdelivery.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-coopdelivery.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		C0427791B1334B34A7C56CFE /* Zocial.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Zocial.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Zocial.ttf"; sourceTree = "<group>"; };
		C3EBFBE4F44041AFAFB077C2 /* Fontisto.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Fontisto.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf"; sourceTree = "<group>"; };
		DCF457AACD50F7701A2F0DBF /* Pods-coopdelivery-coopdeliveryTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-coopdelivery-coopdeliveryTests.debug.xcconfig"; path = "Target Support Files/Pods-coopdelivery-coopdeliveryTests/Pods-coopdelivery-coopdeliveryTests.debug.xcconfig"; sourceTree = "<group>"; };
		E0FF2F041DADDF3C04587483 /* Pods-coopdelivery.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-coopdelivery.release.xcconfig"; path = "Target Support Files/Pods-coopdelivery/Pods-coopdelivery.release.xcconfig"; sourceTree = "<group>"; };
		E3C4F1576D564735691D9B30 /* Pods-coopdelivery-coopdeliveryTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-coopdelivery-coopdeliveryTests.release.xcconfig"; path = "Target Support Files/Pods-coopdelivery-coopdeliveryTests/Pods-coopdelivery-coopdeliveryTests.release.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				303662DC3949120B842A0138 /* libPods-coopdelivery-coopdeliveryTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E5ABF284B37F1AF06E5A4124 /* libPods-coopdelivery.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* coopdeliveryTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* coopdeliveryTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = coopdeliveryTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		10CFB4DCF8D44D7E94D4D673 /* Resources */ = {
			isa = PBXGroup;
			children = (
				9CA5D3E82B9F93DA0037ECE4 /* Roboto-BlackItalic.ttf */,
				9CA5D3EE2B9F93F30037ECE4 /* Roboto-BoldItalic.ttf */,
				53BE1F2AF0B24A41AC2BDE06 /* AntDesign.ttf */,
				9CA5D3EB2B9F93E70037ECE4 /* Roboto-Black.ttf */,
				9CA5D3F72B9F94130037ECE4 /* Roboto-Medium.ttf */,
				850AD79491C742DE8E1073C8 /* Entypo.ttf */,
				9CA5D4092B9F94440037ECE4 /* Roboto-Thin.ttf */,
				9CA5D4032B9F94350037ECE4 /* Roboto-Light.ttf */,
				9CA5D3FD2B9F94230037ECE4 /* Roboto-Regular.ttf */,
				9CA5D4062B9F943D0037ECE4 /* Roboto-ThinItalic.ttf */,
				9CA5D3F12B9F93FD0037ECE4 /* Roboto-Bold.ttf */,
				9CA5D4002B9F942C0037ECE4 /* Roboto-LightItalic.ttf */,
				9CA5D3F42B9F94070037ECE4 /* Roboto-MediumItalic.ttf */,
				1338619E3D5343EF9A2567CC /* EvilIcons.ttf */,
				9CA5D3FA2B9F941B0037ECE4 /* Roboto-Italic.ttf */,
				21926E7F8C5140A7A56581FB /* Feather.ttf */,
				401F2450F48B43E7BAC992BA /* FontAwesome.ttf */,
				30FAAA0AAC5A491DB08A1C78 /* FontAwesome5_Brands.ttf */,
				5E1AD404D68D4E1EAA25F3B5 /* FontAwesome5_Regular.ttf */,
				88E36D4A925D421C99C86752 /* FontAwesome5_Solid.ttf */,
				C3EBFBE4F44041AFAFB077C2 /* Fontisto.ttf */,
				79AF3FBC00C644E7B2FD8798 /* Foundation.ttf */,
				9C67893211474202923A9104 /* Ionicons.ttf */,
				5732627D2E544819B50AEF1D /* MaterialCommunityIcons.ttf */,
				06949B3118454AAD90435C12 /* MaterialIcons.ttf */,
				683709BD43AA4FC5A3E7A142 /* Octicons.ttf */,
				5468BCC7988640BD8F5D02CF /* SimpleLineIcons.ttf */,
				C0427791B1334B34A7C56CFE /* Zocial.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* coopdelivery */ = {
			isa = PBXGroup;
			children = (
				9CD042452BD04FF000DD9A90 /* coopdeliveryDebug.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				1C8BD138097C4ACB10D7C23C /* PrivacyInfo.xcprivacy */,
			);
			name = coopdelivery;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				BF989609C5E82718FB0504E4 /* libPods-coopdelivery.a */,
				957A7AEE89D7B04961BBBB33 /* libPods-coopdelivery-coopdeliveryTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* coopdelivery */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* coopdeliveryTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				E233CBF5F47BEE60B243DCF8 /* Pods */,
				10CFB4DCF8D44D7E94D4D673 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* coopdelivery.app */,
				00E356EE1AD99517003FC87E /* coopdeliveryTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E233CBF5F47BEE60B243DCF8 /* Pods */ = {
			isa = PBXGroup;
			children = (
				669A1A623B3AAD8DB7B7DE49 /* Pods-coopdelivery.debug.xcconfig */,
				E0FF2F041DADDF3C04587483 /* Pods-coopdelivery.release.xcconfig */,
				DCF457AACD50F7701A2F0DBF /* Pods-coopdelivery-coopdeliveryTests.debug.xcconfig */,
				E3C4F1576D564735691D9B30 /* Pods-coopdelivery-coopdeliveryTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* coopdeliveryTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "coopdeliveryTests" */;
			buildPhases = (
				68DB23E401DCB9185EEE9954 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				31E820ECE8C6C4D49A018109 /* [CP] Embed Pods Frameworks */,
				14438513B832A38CD3A74DF5 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = coopdeliveryTests;
			productName = coopdeliveryTests;
			productReference = 00E356EE1AD99517003FC87E /* coopdeliveryTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* coopdelivery */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "coopdelivery" */;
			buildPhases = (
				7CF6CE60513C4831A5F3BBF3 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				5F5DA6015DD3E55228DC452B /* [CP] Embed Pods Frameworks */,
				8A6852EB7FBF0FB7B92A790F /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = coopdelivery;
			productName = coopdelivery;
			productReference = 13B07F961A680F5B00A75B9A /* coopdelivery.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "coopdelivery" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* coopdelivery */,
				00E356ED1AD99517003FC87E /* coopdeliveryTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9CA5D3F02B9F93F30037ECE4 /* Roboto-BoldItalic.ttf in Resources */,
				9CA5D3F62B9F94070037ECE4 /* Roboto-MediumItalic.ttf in Resources */,
				9CA5D4052B9F94360037ECE4 /* Roboto-Light.ttf in Resources */,
				9CA5D4082B9F943D0037ECE4 /* Roboto-ThinItalic.ttf in Resources */,
				9CA5D3FF2B9F94230037ECE4 /* Roboto-Regular.ttf in Resources */,
				9CA5D3F92B9F94130037ECE4 /* Roboto-Medium.ttf in Resources */,
				9CA5D3EA2B9F93DA0037ECE4 /* Roboto-BlackItalic.ttf in Resources */,
				9CA5D4022B9F942C0037ECE4 /* Roboto-LightItalic.ttf in Resources */,
				9CA5D40B2B9F94440037ECE4 /* Roboto-Thin.ttf in Resources */,
				9CA5D3FC2B9F941B0037ECE4 /* Roboto-Italic.ttf in Resources */,
				9CA5D3F32B9F93FD0037ECE4 /* Roboto-Bold.ttf in Resources */,
				9CA5D3ED2B9F93E70037ECE4 /* Roboto-Black.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9CA5D40A2B9F94440037ECE4 /* Roboto-Thin.ttf in Resources */,
				9CA5D3F82B9F94130037ECE4 /* Roboto-Medium.ttf in Resources */,
				9CA5D4012B9F942C0037ECE4 /* Roboto-LightItalic.ttf in Resources */,
				9CA5D4042B9F94360037ECE4 /* Roboto-Light.ttf in Resources */,
				9CA5D3FB2B9F941B0037ECE4 /* Roboto-Italic.ttf in Resources */,
				9CA5D3FE2B9F94230037ECE4 /* Roboto-Regular.ttf in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				9CA5D3E92B9F93DA0037ECE4 /* Roboto-BlackItalic.ttf in Resources */,
				9CA5D3F22B9F93FD0037ECE4 /* Roboto-Bold.ttf in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				9CA5D3F52B9F94070037ECE4 /* Roboto-MediumItalic.ttf in Resources */,
				9CA5D3EF2B9F93F30037ECE4 /* Roboto-BoldItalic.ttf in Resources */,
				9CA5D3EC2B9F93E70037ECE4 /* Roboto-Black.ttf in Resources */,
				9CA5D4072B9F943D0037ECE4 /* Roboto-ThinItalic.ttf in Resources */,
				0F85D48EB0267D8ABE881C7B /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		14438513B832A38CD3A74DF5 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-coopdelivery-coopdeliveryTests/Pods-coopdelivery-coopdeliveryTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-coopdelivery-coopdeliveryTests/Pods-coopdelivery-coopdeliveryTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-coopdelivery-coopdeliveryTests/Pods-coopdelivery-coopdeliveryTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		31E820ECE8C6C4D49A018109 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-coopdelivery-coopdeliveryTests/Pods-coopdelivery-coopdeliveryTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-coopdelivery-coopdeliveryTests/Pods-coopdelivery-coopdeliveryTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-coopdelivery-coopdeliveryTests/Pods-coopdelivery-coopdeliveryTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5F5DA6015DD3E55228DC452B /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-coopdelivery/Pods-coopdelivery-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-coopdelivery/Pods-coopdelivery-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-coopdelivery/Pods-coopdelivery-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		68DB23E401DCB9185EEE9954 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-coopdelivery-coopdeliveryTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		7CF6CE60513C4831A5F3BBF3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-coopdelivery-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8A6852EB7FBF0FB7B92A790F /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-coopdelivery/Pods-coopdelivery-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-coopdelivery/Pods-coopdelivery-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-coopdelivery/Pods-coopdelivery-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00E356F31AD99517003FC87E /* coopdeliveryTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* coopdelivery */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DCF457AACD50F7701A2F0DBF /* Pods-coopdelivery-coopdeliveryTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = coopdeliveryTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/coopdelivery.app/coopdelivery";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E3C4F1576D564735691D9B30 /* Pods-coopdelivery-coopdeliveryTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = coopdeliveryTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/coopdelivery.app/coopdelivery";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 669A1A623B3AAD8DB7B7DE49 /* Pods-coopdelivery.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = coopdeliveryDebug.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = coopdelivery/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = coopdelivery;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E0FF2F041DADDF3C04587483 /* Pods-coopdelivery.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = coopdelivery/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = coopdelivery;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "coopdeliveryTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "coopdelivery" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "coopdelivery" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
