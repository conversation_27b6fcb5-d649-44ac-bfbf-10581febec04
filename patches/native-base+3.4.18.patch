diff --git a/node_modules/native-base/src/core/color-mode/hooks.tsx b/node_modules/native-base/src/core/color-mode/hooks.tsx
index 8e7302a..2065c73 100644
--- a/node_modules/native-base/src/core/color-mode/hooks.tsx
+++ b/node_modules/native-base/src/core/color-mode/hooks.tsx
@@ -31,8 +31,8 @@ export const useAppState = () => {
     () => ({
       getCurrentValue: () => AppState.currentState,
       subscribe: (callback: () => void) => {
-        AppState.addEventListener('change', callback);
-        return () => AppState.removeEventListener('change', callback);
+        const eventList = AppState.addEventListener('change', callback);
+        return () => eventList.remove();
       },
     }),
     []
